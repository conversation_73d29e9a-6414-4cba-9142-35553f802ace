.HomeForfaits {
    background: url('~@app/img/<EMAIL>') left -80px bottom no-repeat;
    background-size: 1048px 277px;
    height: 515px;
    width: 100%;
    display: flex;
    overflow: hidden;
    @media (max-width: 1199px) {
        background-position: left -190px bottom -100px;
    }
    @media (max-width: 1100px) {
        height: auto;
        padding: 50px 0;
        background-position: left -330px bottom -130px;
    }
    @media (max-width: 767px) {
        height: auto;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column-reverse;
        background-position: left -370px bottom -180px;
    }
    &__slider-item {
        display: flex;
        height: 100%;
        padding: 0;
        @media (max-width: 767px) {
            justify-content: center;
        }
    }
    &__devices {
        position: relative;
        flex: 0 0 50%;
        &-image {
            position: absolute;
            left: 240px;
            bottom: 60px;
            transform: translateX(-200%);
            transition: $t-primary;
            @media (max-width: 1612px) {
                left: 140px
            }
            @media (max-width: 1300px) {
                left: 70px
            }
            @media (max-width: 1199px) {
                max-width: 439px!important;
                height: auto!important;
            }
            @media (max-width: 1100px) {
                max-width: 239px!important;
                height: auto!important;
            }
            @media (max-width: 767px) {
                max-width: 206px!important;
                height: auto!important;
                left: 12px;
                bottom: 160px;
            }
        }
        @media (max-width: 1749px) {
            flex: 0 0 54%;
        }
        @media (max-width: 1199px) {
            flex: 0 0 48%;
        }
        @media (max-width: 767px) {
            flex: 0 0 55%;
        }
    }
    &__forfait {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        position: relative;
        &:before {
            content: "";
            transform: scaleX(-1) rotate(-180deg);
            transform-origin: center;
            width: 160px;
            height: 160px;
            position: absolute;
            left: -160px;
            top: 130px;
            //background: url('~@app/img/Icon/drawing-arrow01-light-grey.svg') center center no-repeat;
            background-size: 100%;
            transition: $t-primary;
            transition-delay: 0.5s;
            opacity: 0;
            @media (max-width: 1448px) {
                width: 80px;
                height: 80px;
                left: -80px;
            }
            @media (max-width: 1100px) {
                width: 40px;
                height: 40px;
                left: -50px;
            }
            @media (max-width: 767px) {
                display: none;
            }
        }
        &-title {
            font: 900 50px $f-primary;
            text-transform: uppercase;
            margin-bottom: 20px;
            transition: $t-bounce;
            transform: translateY(-250%);
            @media (max-width: 1100px) {
                font-size: 30px;
                margin-bottom: 2px;
            }
            @media (max-width: 767px) {
                margin: 0 auto 18px;
            }
        }
        &-exp {
            max-width: 700px;
        }
        &-cheap {
            position: absolute;
            right: -217px;
            top: 150px;
            transition: $t-primary;
            span {
                font: 300 16px/20px $f-secondary;
                color: $c-primary-darker;
                text-align: center;
                @media (max-width: 1100px) {
                    font-size: 12px;
                }
            }
            p {
                position: relative;
                opacity: 0;
                transform: translateX(200%);
                transition: $t-primary;
                transition-delay: 1s;
                &:before {
                    width: 78px;
                    height: 78px;
                    content: "";
                    position: absolute;
                    left: -62px;
                    top: 12px;
                    background: url('~@app/img/Icon/drawing-arrow03-bourgogne.svg') center center no-repeat;
                    background-size: 100%;
                    transform: rotate(130deg);
                    @media (max-width: 1699px) {
                        transform: rotate(-170deg);
                        top: -84px;
                        left: -34px;
                    }
                    @media (max-width: 1379px) {
                        transform: scaleX(-1) rotate(-140deg);
                        left: 54px;
                    }
                    @media (max-width: 1100px) {
                        width: 30px;
                        height: 30px;
                        transform: scaleX(-1) rotate(270deg);
                        top: -40px;
                        left: 40px;
                    }
                    @media (max-width: 767px) {
                        transform: scaleX(1) rotate(140deg);
                        top: 20px;
                        left: -24px;
                    }
                }
            }
            @media (max-width: 1699px) {
                right: -45px;
                top: 360px;
            }
            @media (max-width: 1379px) {
                right: 75px;
            }
            @media (max-width: 1100px) {
                top: 195px;
                right: -60px;
            }
            @media (max-width: 767px) {
                top: 365px;
                left: 240px;
                display: none;
            }

        }
        .ButtonEffect {
            margin-top: 40px;

            border: 2px solid $c-black;
            @media (max-width: 1100px) {
                margin-top: 25px;
            }
            &:hover {
                border-color: $c-grey;
            }
        }
        @media (max-width: 767px) {
            align-items: center;
            padding-top: 90px;
            box-sizing: border-box;
            flex: 0 0 95%;
            width: 100%;
        }
    }
    &__pictos {
        margin: 0;
        padding: 0;

        &-item {
            display: inline-block;
            position: relative;
            padding-right: 30px;
            opacity: 0;
            text-align: center;
            .icon {
                width: 100px;
                height: 100px;
                fill: $c-grey;
                @media (max-width: 1100px) {
                    width: 60px;
                    height: 60px;
                }
                @media (max-width: 767px) {
                    width: 50px;
                    height: 50px;
                }
                // @media (max-width: 480px) {
                //     width: 40px;
                //     height: 40px;
                // }
            }
            p {
                width: 100%;
                text-align: center;
                font: 700 16px/22px $f-primary;
                color: $c-medium-grey;
                @media (max-width: 1100px) {
                    line-height: 16px;
                }
                @media (max-width: 767px) {
                    font: 700 14px/16px $f-primary;
                }
            }
            &--price {
                @media (max-width: 767px) {
                    padding-left: 10px;
                }
                p {
                    font: 900 100px/100px $f-primary;
                    position: relative;
                    color: $c-grey;
                    sup {
                        font: 900 35px/50px $f-primary;
                        position: absolute;
                        left: 110px;
                        top: 10px;
                        @media (max-width: 1100px) {
                            font-size: 18px;
                            left: 70px;
                            top: 0px;
                        }
                        @media (max-width: 767px) {
                            font-size: 14px;
                            left: 53px;
                            top: 0px;
                        }
                        &.usd-sign {
                            left: -10px;
                            font: 900 20px/50px $f-primary;
                        }
                    }
                    @media (max-width: 1100px) {
                        font-size: 60px;
                        line-height: 60px;
                        padding-top: 0px;
                    }
                    @media (max-width: 767px) {
                        font-size: 45px;
                        line-height: 45px;
                        padding-top: 7px;
                    }
                    @media (max-width: 480px) {
                        padding-top: 0;
                    }
                }
                &--small {
                    p {
                        font: 900 70px/100px $f-primary;
                        @media (max-width: 1100px) {
                            font: 900 46px/70px $f-primary;
                        }
                        @media (max-width: 767px) {
                            font: 900 34px/50px $f-primary;
                        }
                    }
                }

                $pricePath: &;

                @at-root {
                    html:lang(en) {
                        #{ $pricePath } {
                            padding-left: 15px;
                        }
                    }
                }

                $pricePath: null;
            }
            &:first-of-type {
                &:after {
                    content: "";
                    width: 20px;
                    height: 20px;
                    background: url('~@app/img/Icon/ic_add-grey-medium.svg') center center no-repeat;
                    background-size: 100%;
                    position: absolute;
                    right: 8px;
                    top: 38px;
                    @media (max-width: 1100px) {
                        width: 15px;
                        height: 15px;
                        top: 22px;
                    }
                    @media (max-width: 767px) {
                        right: 0;
                    }
                    // @media (max-width: 480px) {
                    //     top: 12px;
                    // }
                }
            }
            &:nth-of-type(2) {
                @media (max-width: 767px) {
                    padding-right: 15px;
                }
                &:after {
                    content: "";
                    width: 20px;
                    height: 20px;
                    background: url('~@app/img/Icon/ic_add-grey-medium.svg') center center no-repeat;
                    background-size: 100%;
                    position: absolute;
                    right: -8px;
                    top: 38px;
                    @media (max-width: 1100px) {
                        width: 15px;
                        height: 15px;
                        top: 22px;
                    }
                    // @media (max-width: 480px) {
                    //     top: 12px;
                    // }
                }
            }
            &:nth-last-child(2) {
                &:after {
                    content: "";
                    width: 20px;
                    height: 20px;
                    background: url('~@app/img/Icon/ic_equal-grey-medium.svg') center center no-repeat;
                    background-size: 100%;
                    position: absolute;
                    right: 7px;
                    top: 38px;

                    @media (max-width: 1100px) {
                        width: 15px;
                        height: 15px;
                        top: 22px;
                    }
                    @media (max-width: 767px) {
                        right: 6px;
                    }
                    // @media (max-width: 480px) {
                    //     top: 12px;
                    // }
                }

                &:nth-of-type(2) {
                    &:after {
                        right: 4px;
                    }
                }
            }
            &:last-of-type {
                vertical-align: top;
            }
            &-mois {
                font: 300 14px $f-secondary!important;
                position: absolute!important;
                right: -74px;
                top: 48px;
                transform: rotate(5deg);
                transform-origin: center;
                @media (max-width: 1100px) {
                    font-size: 10px!important;
                    top: 30px;
                    right: -45px;
                }
                @media (max-width: 767px) {
                    font-size: 10px!important;
                    left: 42px;
                    top: 20px;
                }
                @media (max-width: 480px) {
                    font-size: 10px!important;
                    left: -10px;
                    top: 35px;
                }
            }
            @media (max-width: 1100px) {
                padding-right: 25px;
            }
            @media (max-width: 767px) {
                text-align: center;
                padding-right: 15px;
            }

        }
    }
}

.HomeForfaits--shown {
    .HomeForfaits__devices {
        &-image {
            transform: translateX(0);
            max-width: 548px;
            height: 366px;
        }
        @media (max-width: 767px) {
            display: none;
        }
    }
    @for $i from 1 through 4 {
        li:nth-of-type(#{$i}) {
          @include transition(0.5s linear (0.5s + ($i - 1) * 0.2s));
          opacity: 1;
        }
    }
    .HomeForfaits__forfait-title {
        transform: translateY(0);
    }
    .HomeForfaits__forfait {
        &:before {
            opacity: 1;
        }
        .Button {
            transform: translateY(0);
        }
    }
    .HomeForfaits__forfait-cheap {
        p {
            opacity: 1;
            transform: translateX(0);
        }
    }
}

.HomeForfaits__slider {
    height: 100%;
    margin-left: 0;
    flex: 0 0 50%;
    .owl-height {
        height: 100%!important;
        //overflow: visible!important;
    }
    .owl-stage {
        height: 100%!important;
    }
    .owl-item {
        height: 100%!important;
    }
    @media (max-width: 1749px) {
        flex: 0 0 46%;
    }
    @media (max-width: 1199px) {
        flex: 0 0 52%;
    }
}

.HomeForfait__slider-item--ontario {
    .HomeForfaits__pictos-item {
        &:first-of-type {
            &:after {
                right: -5px!important;
            }
        }
        // &:nth-of-type(2) {
        //     &:after {
        //         display: none!important;
        //     }
        // }
        &:last-of-type:after {
            right: auto;
            left: -38px;
        }
    }
}

.owl-dot {
    &:focus {
        outline: 2px solid blue!important;
    }
}