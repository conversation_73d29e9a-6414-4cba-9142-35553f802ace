.TeaserButton {
	appearance: none;
	border: none;
	background: $c-white;
	color: $c-primary;
	font: 700 16px/16px $f-primary;
	text-transform: uppercase;
	padding: 19px 30px 17px;
	border-radius: 6px;
	cursor: pointer;
	box-shadow: 0 12px 10px -6px rgba(#000, 0);
	transform: translateY(0);
	transition: all 0.2s $cubic;

	&:hover {
		// background: $c-primary;
		// color: $c-white;
		box-shadow: 0 27px 18px -13px rgba(#000, 0.3);
		transform: translateY(-7px);
	}

	&--mouse {
		position: relative;

		&:after {
			content: '';
			display: inline-block;
			position: absolute;
			width: 56px;
			height: 67px;
			background: {
				image: url('../../../img/teaser-tv/mouse.svg');
				size: cover;
				position: center;
				repeat: no-repeat;
			};
		}
	}
}