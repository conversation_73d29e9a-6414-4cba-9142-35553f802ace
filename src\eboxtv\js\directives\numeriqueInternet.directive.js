/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { CookiesService } from '@common/ts/services/Cookies.service';

export class NumeriqueInternetDirective extends Directive {

	_$OrderService = OrderOnlineService.getInstance()

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[numeriqueInternet]';
    sku_internet = '';

    selection = {
        downloadSpeed: "",
        forfait: "",
        modem: "rent",
        planPrice: "",
        router: "rent",
        technology: "",
        title: "",
        cableTech: ""
    }

    cardInternet = '';
    minTv = false;
    minVitesse = 25;

	constructor(host) {
		super(host, []);

        if(host.classList.contains('minTv')){
            this.minTv = true;
        }

        this.fournisseur = host.getAttribute('data-fournisseur');
        var selection_infoNuagique = this._$OrderService._reteboxTVOptions();

        if( this.fournisseur == 'cv-cable' ){
            this.minVitesse = 50;
        }else if( this.fournisseur == 'c-cable' ){
            this.minVitesse = 15;
        }else if( this.fournisseur == 'v-cable'){
            this.minVitesse = 30;
        }

        var selection_internet = this._$OrderService.retInternetPlan();
        if(selection_internet && selection_internet.cableTech == "DSL"){
            this.minVitesse = 25;
            jQuery('.minVitesseText').text(this.minVitesse);
        }

        if( selection_infoNuagique && selection_infoNuagique.cloudSpace && selection_infoNuagique.cloudSpace.min && selection_infoNuagique.cloudSpace.min > this.minVitesse){
            this.minVitesse = parseInt(selection_infoNuagique.cloudSpace.min);
        }

		this._onInit();
	}

	_onInit() {
        this.cardInternet = document.querySelectorAll('.item-internet');
        
        // Setter une variable essentiel au channel
        var ebtvPackage = CookiesService.getCookies('ebtv__tvPackage');
        if(ebtvPackage == null){
            CookiesService.setCookies({
                name: 'ebtv__tvPackage',
                value: '%7B%22ID%22:7073,%22type%22:%22payant%22,%22name%22:%22EBOX%20TV%22%7D'
            });
        }

        this.initForm();
        this.cardInternet.forEach(el => {
            el.addEventListener('click', function(evt){
                var classlist = Array.from(evt.currentTarget.classList);
                if(!classlist.includes('VirageNum__packages-slider-item--active')){
                    this.toggleSelection(evt.currentTarget)
                }
            }.bind(this))
        });
	}

    initForm(){
        this.selection = this._$OrderService.retInternetPlan();
        if(this.selection &&!Object.keys(this.selection).length){
            this.selection.forfait = 'none';
        }

        // Si on cherche la vitesse minimal pour le eboxTV
        if(this.minTv){
            var found = 0;
            this.cardInternet.forEach(el => {
                if( this.selection.forfait && el.getAttribute('data-vitesse') >= this.minVitesse && found == 0 ){
                    el.classList.add('VirageNum__packages-slider-item--active');
                    this.toggleSelection(el);
                    found = 1;
                }
            });

        // Si on cherche s'il y a une selection
        }else{

            if(this.selection ){
                
                this.cardInternet.forEach(el => {
                    if( this.selection.forfait && el.getAttribute('data-sku') == this.selection.forfait ){
                        el.classList.add('VirageNum__packages-slider-item--active');
                    }
                });
            }

        }

    }

    toggleSelection(target){
        this.cardInternet.forEach(el => {
            el.classList.remove('VirageNum__packages-slider-item--active');
        });
        target.classList.add('VirageNum__packages-slider-item--active');
        if(target.getAttribute('data-sku') !== 'none'){
            this.selection.forfait = target.getAttribute('data-sku');
            this.selection.modem = 'rent';
            this.selection.router = 'rent';
            this.selection.downloadSpeed = target.getAttribute('data-vitesse');
            this.selection.planPrice = target.getAttribute('data-price');
            this.selection.technology = target.getAttribute('data-fournisseur');
            this.selection.title = target.getAttribute('data-title');
            this.selection.cableTech = target.getAttribute('data-cabletech');
            this.selection.prmModem = target.getAttribute('data-prm-modem');
            this.selection.prmRouter = target.getAttribute('data-prm-routeur');

            const expirationDate = new Date();
            expirationDate.setDate(expirationDate.getDate() + 3);

            CookiesService.setCookies({
                name: 'CommandeStarted',
                value: 'oui',
                expires: expirationDate
            });

        }
        else{
            this.selection = {};
        }
         
        this._$OrderService.saveInternetService(this.selection);
        this._$OrderService.saveEquipementInternet([]);

        var selection =  this._$OrderService.retPhoneOrder();
        if( !(selection && !jQuery.isEmptyObject(selection)) ){
            this._$OrderService.savePhoneService({});
        }
    }

}
