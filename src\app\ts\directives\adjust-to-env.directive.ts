import { Directive } from '@core/directive';
import { CookiesService } from '@common/ts/services/Cookies.service';

export class AdjustToEnvDirective extends Directive<HTMLAnchorElement> {
	public static selector: string = 'a[adjust-to-env]';

	public attrs: {
		options: {
			[key: string]: string;
		};
	};

	private envProv: string = CookiesService.getCookies('eboxprovince');

	constructor(host: HTMLAnchorElement) {
		super(host, [
			{name: 'adjust-to-env', as: 'options', type: 'eval'}
		]);
		this.adjustHref();
	}

	private adjustHref(): void {
		if (this.envProv in this.attrs.options) {
			this.host.setAttribute('href', this.attrs.options[this.envProv]);
		}
	}
}