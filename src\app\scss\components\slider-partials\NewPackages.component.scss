$carouselTimeOffset: 0.3s;

.NewPackages {
	height: 709px;

	@media (max-width: 1500px) {
		height: 609px;
	}
	@media (max-width: 1199px) {
		height: 355px;
	}
	@media (max-width: 767px) {
		height: 350px;
	}

	&__wrapper {
		position: relative;
		display: flex;
		height: 100%;
	}

	&__col {
		&--content {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 0 0 63.3334%;
			padding: 60px 15% 60px 15%;
			height: 100%;
			background: {
				image: url('../../../img/slideshow/bg-slider-demenagement.png');
				size: 100% 100%;
				repeat: no-repeat;
				position: left center;
			};
			z-index: 1;

			@include mediaScreenRatio(2) {
				@media (min-width: 1025) {
					background-image: url('../../../img/slideshow/<EMAIL>');
				}
			}

			@media (max-width: 1920px) {
				padding: 60px 15% 60px 60px;
			}
			@media (max-width: 1450px) {
				flex: 0 0 75%;
				padding: 30px 18% 30px 30px;
			}
			@media (max-width: 767px) {
				flex: 0 0 100%;
				background-image: url('../../../img/slideshow/bg-slider-demenagement--mobile.png');
				padding: 30px 50% 30px 30px;
			}
			@media (max-width: 599px) {
				padding: 30px 50% 15% 30px;
			}
			@media (max-width: 460px) {
				background-size: auto;
				background-position: center top;
				padding: 15px 40% 10% 15px;
				justify-content: flex-start;
			}
		}

		&--image {
			position: absolute;
			top: 0;
			right: 0;
			height: 100%;
			width: 60%;
			background: {
				image: url('../../../img/slideshow/<EMAIL>');
				size: cover;
				position: center;
			};
			transform: scale(1);
			transition-delay: $carouselTimeOffset;

			@media (max-width: 767px) {
				width: 70%;
				background-position: top left;
				background-image: url('../../../img/slideshow/fireworks.jpg');
			}
			@media (max-width: 599px) {
				background-position: -50% -50%;
				background-image: url('../../../img/slideshow/<EMAIL>');
			}
		}
	}

	&__content-wrapper {
		color: $c-light-grey;
		text-align: right;

		@media (max-width: 460px) {
			text-align: left;
		}
	}

	&__title {
		margin-bottom: 75px;
		font: 900 50px/66px $f-primary;
		text-transform: uppercase;

		@media (max-width: 1199px) {
			font: 900 34px/48px $f-primary;
			margin-bottom: 45px;
		}
		@media (max-width: 991px) {
			font: 900 24px/30px $f-primary;
		}
		@media (max-width: 767px) {
			font: 900 20px/28px $f-primary;
		}
	}

	&__title-part.NewPackages__title-part {
		display: block;
		white-space: nowrap;

		&--doodled {
			display: inline-block;
			color: #FFF;
			margin-top: 3px;

			&:before, &:after {
				opacity: 0.0001;
				transition-delay: $carouselTimeOffset;
			}
			
			&:before {
				height: 165%;
			}

			&:after {
				height: 12%;
    			top: 85%;
			}
		}
	}

	&__title-part-wrapper {
		display: block;
		overflow: hidden;
	}

	&__title-part-content {
		display: inline-block;
		transform: translateY(100%);
		opacity: 0.0001;
		transition-delay: $carouselTimeOffset;
	}

	&__btn-wrapper.NewPackages__btn-wrapper {
		&:after {
			opacity: 0.0001;
			transition-delay: $carouselTimeOffset;

			@media (max-width: 1199px) {
				top: 100%;
				left: 100%;
				width: 32px;
				height: 32px;
			}
			@media (max-width: 560px) {
				top: 115%;
				left: 85%;
			}
			@media (max-width: 460px) {
				top: 125%;
				left: 0;
				right: auto;
				transform: rotateY(180deg);
			}
		}
	}

	&__btn {
		transform: scale(0.75);
		opacity: 0.0001;
		transition-delay: $carouselTimeOffset;

		&:after {
			background: #FFF;
		}

		&:hover {
			color: $c-primary !important;
		}

		&-nomobile-part {
			@media (max-width: 1199px) {
				display: none;
			}
		}
	}
}


// Animation on slide active
.owl-item {
	&.active {
		.NewPackages {
			&__col {
				&--image {
					will-change: transform;
					transform: scale(1.1);
					transition: transform 10s cubic-bezier(0.25, 0.46, 0.45, 0.94);
					transition-delay: 0s;
				}
			}

			&__title-part {
				@for $i from 1 through 3 {
					&:nth-child(#{ $i }) {
						.NewPackages {
							&__title-part-content {
								transition-delay: #{ (($i - 1) * 0.1s) + $carouselTimeOffset};
							}
						}
					}
				}

				&:before, &:after {
					opacity: 1;
					transition: opacity 0.25s $cubic;
				}

				&:before {
					transition-delay: #{ 0.65s + $carouselTimeOffset};
				}

				&:after {
					transition-delay: #{ 0.5s + $carouselTimeOffset };
				}
			}

			&__title-part-content {
				opacity: 1;
				transform: translateY(0);
				will-change: transform, opacity;
				transition: transform 0.3s $cubic;
			}

			&__btn {
				transform: scale(1);
				opacity: 1;
				will-change: transform, opacity;
				transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 2) #{ 0.75s + $carouselTimeOffset },
							opacity 0.4s cubic-bezier(0.68, -0.55, 0.265, 2) #{ 0.75s + $carouselTimeOffset },
							color 0.25s $cubic 0s;
			}

			&__btn-wrapper.NewPackages__btn-wrapper {
				&:after {
					opacity: 1;
					transition: all 0.4s $cubicElastic;
					transition-delay: #{ 1s + $carouselTimeOffset };
					animation: click 0.3s steps(1) #{ 1.5s + $carouselTimeOffset };

					@media (max-width: 460px) {
						animation-name: clickInverse;
					}
				}
			}
		}
	}
}

@keyframes click {
	0%, 100% {
		transform: scale(1);
	}

	50% {
		transform: scale(0.85);
	}
}

@keyframes clickInverse {
	0%, 100% {
		transform: rotateY(180deg) scale(1);
	}

	50% {
		transform: rotateY(180deg) scale(0.85);
	}
}