$gridWidth: 1028px;

@mixin wrapper() {
	width: 100%;
	max-width: $gridWidth + 60px;
	padding: 0 30px;
	margin: 0 auto;

	@media (max-width: map-get($breakpoints, carouselMobile)) {
		max-width: $gridWidth + 38px;
		padding: 0 19px;
	}
}

@mixin bigSectionTitle() {
	color: $c-primary;
	font: 900 30px/50px $f-primary;
	text-transform: uppercase;
	text-align: center;
	margin-bottom: 30px;
}

@mixin fromRootSelector($rootSelector: 'body') {
	@at-root {
		#{ $rootSelector } #{&} {
			@content;
		}
	}
}

@mixin pseudoElDecoration($iconURL, $width: 100%, $height: 9px, $bottom: -2px) {
	&:after {
		content: '';
		position: absolute;
		bottom: $bottom;
		left: 50%;
		transform: translateX(-50%);
		display: block;
		width: $width;
		height: $height;
		background: {
			image: url($iconURL);
			size: 100% 100%;
			position: center;
			repeat: no-repeat;
		};
		@content;
	}
}