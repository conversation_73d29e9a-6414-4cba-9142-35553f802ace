/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";
import { PriceCalcService } from "@eboxtv/js/services/PriceCalc.service";
import { CookiesService } from '@common/ts/services/Cookies.service';
import { CoreTools } from '@core/helpers';
export class NumeriquePaiementCommandeDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()
	_$tvService = TVOrderService.getInstance();
	_$priceCalc = PriceCalcService.getInstance();
	_$priceCalc = PriceCalcService.getInstance();

	//SELECTEUR DE LA DIRECTIVE
	static selector = '[numerique-commande-paiement]';

	sectionMonthly = document.getElementById('monthlyCharges');
	sectionOneTime = document.getElementById('oneTimeCharges');

	internetEquipement = {};
	phoneEquipement = {};

	internetPlan = {};
	phonePlan = {};

	order = {};

	billInfo = {};
	decodeur = {};
	discount = [];

	// VAR FOR TAXES
	province = document.forms['info'].elements['province'].value;
	TPS = 0.05;
	TVQ = 0.09975;
	TVH = 0.13;


	oneTimeTPS = document.getElementById('oneTimeTPS');
	oneTimeTVQ = document.getElementById('oneTimeTVQ');
	oneTimeTVH = document.getElementById('oneTimeTVH');


	monthlySousTotal = 0;
	oneTimeSousTotal = 0;

	monthlyTotal = 0;
	oneTimeTotal = 0;

	total = 0

	sousTotalOneTimeSection = document.getElementById('OneTimeSousTotal');
	sousTotalMonthlySection = document.getElementById('monthlySousTotal');

	totalSection = document.getElementById('total');

	//var promo
	promoDuo = {};
	promoInternet = {};
	promoInfo = {};

	isADSL = false;

	taxe_exempt = 0;

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
		jQuery('.full-screen-spinner-background').hide();
		this._$OrderService.initOrderConfirmation();

		this.billInfo = this._$OrderService._retBillInfo();
		this.internetEquipement = this._$OrderService._retInternetEquipement();
		this.phoneEquipement = this._$OrderService._retPhoneEquipement();

		this.internetPlan = this._$OrderService._retInternetOrder();
		this.phonePlan = this._$OrderService._retPhoneOrder();

		this.order = this._$OrderService._retOrder();
		this.infoNuagique = this._$OrderService._reteboxTVOptions();

		//promo
		this.promoDuo = this._$OrderService.retPromoDuo()
		this.promoInfo = this._$OrderService.retPromoInfo()
		this.promoInternet = this._$OrderService.retInternetPromo()
		this.decodeur = this._$OrderService.retDecodeur();
		this.promoDecodeur = this._$OrderService.retPromoDecodeur();
		this.equipementInternetPromo = this._$OrderService.retEquipementInternetPromotion();


		this.displayCharges();
	}

	displayCharges() {
		this.addFees();
		console.log('avant internet');
		this.addInternetPlan();
		console.log('avant phone');

		this.addPhonePlan();
		console.log('avant tv');

		this.addTV();
		this.addDeposits();
		this.addEcoFrais();

		console.log('avant discount');

		this.addDiscount();

		this.displaySousTotaux();
		this.calcTaxes();
		const url = window.location.href;
		if(url.includes('beta')){
			this.addprixEbox();
		}
	}

	addFees() {
		if (parseFloat(this.order.installFee) > 0) {
			this.addCharges(this.sectionOneTime, CoreTools.translate("Frais d'installation", "Installation costs"), this.order.installFee, '');
		}
		if (parseFloat(this.order.shippingFee) > 0) {
			this.addCharges(this.sectionOneTime, CoreTools.translate("Frais de livraison", "Shipping cost"), this.order.shippingFee, '');
		}

	}


	addInternetPlan() {
		this.isADSL = this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'? true : false;
		const qualif = CookiesService.getCookies('eboxQualification', true).details;
		const adslInfo = qualif.adslinfo ? qualif.adslinfo : false;

		var total_internet = 0;


		this.addCharges(this.sectionMonthly, this.internetPlan.title, this.internetPlan.planPrice, '');
		total_internet = parseFloat(total_internet) + parseFloat(this.internetPlan.planPrice);
		if (this.billInfo && this.billInfo.broadband_fund.broadband_fund_required) {
			this.addCharges(this.sectionMonthly, CoreTools.translate('Fonds pour la large bande', 'Broadband fund'), this.billInfo.broadband_fund.broadband_fund_qty);
			total_internet = parseFloat(total_internet) + parseFloat(this.billInfo.broadband_fund.broadband_fund_qty);
		}
		if(this.isADSL){
			adslInfo.forEach(plan => {

				if(qualif.province == 'on'){
					var plan_numeric = plan.dsl_plan_on_numeric;
				}else{
					var plan_numeric = plan.dsl_plan_numeric;
				}

				if(plan_numeric == this.internetPlan.forfait){
					var adslSelected = plan;
					if(this.internetPlan.downloadSpeed == 6){
						this.addCharges(this.sectionOneTime, CoreTools.translate("Frais d'activation de la ligne",'Line activation fee'), 0.00);
						//total_internet = parseFloat(total_internet) + parseFloat(20);
					}
					if(adslSelected.dryloop_price && this.internetPlan.downloadSpeed != 6){
						this.addCharges(this.sectionMonthly, CoreTools.translate('Frais de ligne sèche','Dryloop'), adslSelected.dryloop_price);
						total_internet = parseFloat(total_internet) + parseFloat(adslSelected.dryloop_price);

					}
					if(adslSelected.dryloop_rural){
						this.addCharges(this.sectionMonthly, CoreTools.translate('Frais de ligne sèche rurale','Rural dryloop'), adslSelected.dryloop_rural_price);
						total_internet = parseFloat(total_internet) + parseFloat(adslSelected.dryloop_rural_price);
					}
				}
			});
		}

		this.internetEquipement.forEach(equipement => {
			let parent = '';
			let price = '';
			let term = ''
			if(equipement.type == 'modem'){
				term = 'Modem';
			}
			else{
				term = CoreTools.translate('Routeur', 'Router');
			}
			if (equipement.buyRent == 'rent') {
				parent = this.sectionMonthly;
				price = equipement.rentPrice;
			}
			else if (equipement.buyRent == 'buy') {
				parent = this.sectionOneTime;
				price = equipement.buyPrice;
				if (equipement.sit) {
					price = price - equipement.rebate;
				}

			}

			if (equipement.buyRent != 'own') {
				this.addCharges(parent, term, price, '');
				total_internet = parseFloat(total_internet) + parseFloat(price);
			}
		});

		//this.addCharges(this.sectionMonthly, 'Internet', total_internet);
	}

	addPhonePlan() {
		if (!jQuery.isEmptyObject(this.phonePlan) && this.phonePlan.type != 'none' && this.internetPlan.forfait) {
			this.addCharges(this.sectionMonthly, this.phonePlan.type, this.phonePlan.planPrice - 5.00, '');
			if (this.billInfo.taxe_911.price) {
				this.addCharges(this.sectionMonthly, CoreTools.translate('Taxe 911', '911 taxe'), this.billInfo.taxe_911.price, '')
			}
			if(this.phoneEquipement && Object.keys(this.phoneEquipement).length !== 0){
				this.phoneEquipement.forEach(equipement => {
					let parent = '';
					let price = '';
					if (equipement.buyRent == 'rent') {
						parent = this.sectionMonthly;
						price = equipement.price;
					}
					else if (equipement.buyRent == 'buy') {
						parent = this.sectionOneTime;
						price = equipement.price;
						if (equipement.sit) {
							price = price;
						}
					}
	
					if (equipement.buyRent != 'own') {
						//this.addCharges(parent, CoreTools.translate('Adaptateur VOIP', 'VOIP Adapter'), price, '');
					}
				});
			}

			//this.discount.push({ text: CoreTools.translate('Rabais ligne téléphonique', 'Phone line discount'), price: '5.00', monthly: true })
		}

	}

	addTV() {
		const hasTV = CookiesService.getCookies('hasTV');
		if (hasTV == 'true') {
			let baseChannel = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvChannels.base : [];
			let tvRegion = baseChannel[0].area_name;
			const theme = this._$tvService.getSelectedBundle();
			if (theme) {
				if(theme.isFeatured == false ){
					const text = CoreTools.translate('Télévision : Service de base ('+ tvRegion+')','Television : Basic service ('+ tvRegion +')' );
					this.addCharges(this.sectionMonthly, text, this._$priceCalc.totals.channels['base'], '');
				}
				var price_theme = theme.cost;
				if (this.decodeur.qty) {
					price_theme += parseFloat(this.decodeur.price);
				}

				var selection_internet = this._$OrderService.retInternetPlan();
        		if( selection_internet && !jQuery.isEmptyObject(selection_internet) ){
					var internet_dispo = ['NUM00002', 'NUM00003', 'NUM00004', 'NUM00005', 'NUM00016', 'NUM00017',
										  'NUM00018', 'NUM00019', 'NUM00020', 'NUM00021', 'NUM00022', 'NUM00012',
										  'NUM00013', 'NUM00014', 'NUM00015', 'NUM00035', 'NUM00036'];
					if( internet_dispo.includes(selection_internet.forfait) ){
						
						var forfait_choisi = this._$OrderService._retForfaitOption();
						if( (forfait_choisi && !jQuery.isEmptyObject(forfait_choisi)) ){
							if(forfait_choisi.prmSku != ""){
								price_theme -= parseFloat(forfait_choisi.prmPrix);
							}
						}
					}
				}

				this.addCharges(this.sectionMonthly, theme.name, price_theme, '');
			}
			else{
				const text = CoreTools.translate('Télévision : Service de base ('+ tvRegion+')','Television : Basic service ('+ tvRegion +')' );
				this.addCharges(this.sectionMonthly, text, this._$priceCalc.totals.channels['base'], '');
			}

			const listeChannels = this._$tvService.selectedChannels;
			let textStandAlone = '';
			let prixStandAlone = this._$priceCalc.totals.channels['a-la-carte'];
			let firstPrenium = true;
			for (const property in listeChannels) {
				if (property === 'a-la-carte') {
					textStandAlone += 'À la carte : ';
					listeChannels[property].forEach(element => {
						const channel = this._$tvService.getChannelById(element)
						textStandAlone += ' + ' + channel.post_title + '( '+channel.extraCost+' ) ';
					});
					if(this._$priceCalc.totals.channels['a-la-carte'] > 0){
						this.addCharges(this.sectionMonthly, textStandAlone, prixStandAlone, '');
					}
				}
				if(this._$priceCalc.totals.channels['others'] > 0){
					if (property === 'others') {
						listeChannels[property].forEach(element => {
							const channel = this._$tvService.getChannelById(element)
					// 		textStandAlone += ' + ' + channel.post_title + ' ';
							this.addCharges(this.sectionMonthly, channel.post_title, channel.cost, '');
						});
					}
				}

				if (property === 'premium') {
					let prix = 0;
					let text = 'Premium : ';
					listeChannels[property].forEach(element => {
						const channel = this._$tvService.getChannelById(element);
						if(!firstPrenium){
							text += ' + ';
						}
						firstPrenium = false;

						if(channel.post_title.includes('Crave')){
							text += 'Crave' + ' ';
						}
						else{
							text +=  channel.post_title + ' ';

						}

						prix += channel.cost;
					});

					if(prix > 0){
						this.addCharges(this.sectionMonthly, text, prix, '');
					}
				}
			}
			if (this.decodeur.qty && this.decodeur.qty > 1) {
				let parent = '';
				if (this.decodeur.buyRent === 'rent') {
					parent = this.sectionMonthly;
				}
				else if (this.decodeur.buyRent === 'buy') {
					parent = this.sectionOneTime;
				}
				if(this.decodeur.buyRent !== 'own' && this.decodeur.qty){
					this.addCharges(parent, CoreTools.translate('Récepteurs ', 'Receivers ') + (this.decodeur.qty) + " " + CoreTools.translate('(1 récepteur inclus)', '(1 receiver included)'), parseFloat(this.decodeur.qty - 1) * parseFloat(this.decodeur.price), '');
				}
			}

			if (this.infoNuagique.cloudSpace) {
				this.addCharges(this.sectionMonthly, this.infoNuagique.cloudSpace.title, this.infoNuagique.cloudSpace.price, '');
			}
		}

	}

	addDeposits() {
		if (this.billInfo.credit_response && this.billInfo.credit_response.action === 'DEPOSIT_FEE_REQUIRED' && this.billInfo.credit_response.total_deposit_amount > 0) {
			this.addCharges(this.sectionOneTime, CoreTools.translate('Dépôt', 'Deposit'), this.billInfo.credit_response.total_deposit_amount, '');
			let amount = typeof(this.billInfo.credit_response.total_deposit_amount) == 'string' ? parseFloat(this.billInfo.credit_response.total_deposit_amount) : this.billInfo.credit_response.total_deposit_amount;
			this.taxe_exempt +=  amount;
		}
	}

	addEcoFrais(){
		let frais = 0;
		if (this.billInfo.eco_fees.net.qty > 0 && this.billInfo.eco_fees.net.qty) {
			frais += this.billInfo.eco_fees.net.price * this.billInfo.eco_fees.net.qty;
		}
		const hasTV = CookiesService.getCookies('hasTV');

		if(hasTV == 'true'){
			if (this.billInfo.eco_fees.tv.qty > 0 && this.billInfo.eco_fees.tv.qty) {
				frais += this.billInfo.eco_fees.tv.price * this.billInfo.eco_fees.tv.qty;
			}
		}

		if(frais > 0){
			this.addCharges(this.sectionOneTime, CoreTools.translate('Écofrais', 'Eco-Fee'), frais, '');
		}

	}

	addCharges(parent, text, price, state) {
		let add = true;
		let li = document.createElement('LI');
		let p = document.createElement('p');
		let span = document.createElement('SPAN');
		span.classList.add('service_price');
		p.textContent = text;
		span.textContent = price + '$';

		li.classList.add('service_detail-product');

		if (state === 'discount') {
			add = false;
			p.classList.add('service_detail-product--red');
			span.classList.add('service_price--red');
			if(CoreTools.lang == 'fr'){
				span.classList.add('frAlign');
				span.textContent = '-' + price + '$';
			}
			else{
				span.textContent = '-$' + price;
			}
		}
		else {
			add = true;
			li.classList.add('service_detail-product');
			if(CoreTools.lang == 'fr'){
				span.classList.add('frAlign');
				span.textContent = parseFloat(price).toFixed(2) + '$';
			}
			else{
				span.textContent = '$'+parseFloat(price).toFixed(2);
			}
		}
		li.append(p);
		li.append(span);
		parent.append(li);

		if (parent == this.sectionMonthly) {
			this.monthlySousTotal = this.calcSousTotaux(this.monthlySousTotal, price, add);
		}
		else {
			this.oneTimeSousTotal = this.calcSousTotaux(this.oneTimeSousTotal, price, add);
		}
	}

	addDiscount() {
		this.discount.forEach(element => {
			let parent = ''
			element.monthly ? parent = this.sectionMonthly : this.sectionOneTime;
			this.addCharges(parent, element.text, element.price, 'discount');
		});



		if (this.promoDuo) {
			this.promoDuo.forEach(promo => {
				let section = '';
				if(parseFloat(promo.prix) > 0){
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}
			});
		}

		if (this.promoInfo && Array.isArray(this.promoInfo)) {
			this.promoInfo.forEach(promo => {
				let section = '';
				if(parseFloat(promo.prix) > 0){
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}

			});
		}

		if (this.promoInternet && Array.isArray(this.promoInternet)) {
			this.promoInternet.forEach(promo => {
				if(parseFloat(promo.prix) > 0){
					let section = '';
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}
			});
		}
		const qualif = CookiesService.getCookies('eboxQualification', true).details;
		const adslInfo = qualif.adslinfo ? qualif.adslinfo : false;
		if(this.isADSL){
			adslInfo.forEach(plan => {

				if(qualif.province == 'on'){
					var plan_numeric = plan.dsl_plan_on_numeric;
				}else{
					var plan_numeric = plan.dsl_plan_numeric;
				}

				if(plan_numeric == this.internetPlan.forfait){
					var adslSelected = plan;
					if(adslSelected.credit_dryloop_price){
						this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais de ligne sèche','Dryloop discount'), adslSelected.credit_dryloop_price, 'discount');
					}
					if(adslSelected.credit_dryloop_rural){
						this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais de ligne sèche','Rural Dryloop discount'), adslSelected.credit_dryloop_rural_price);
					}
				}
			});
		}

		if (this.equipementInternetPromo) {
			this.equipementInternetPromo.forEach(promo => {
				if(parseFloat(promo.prix) > 0){
					let section = '';
					if (promo.recurrent == 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}
			});
		}
		if(this.billInfo.promo_free_month_tv.promo_qty){
				this.addCharges(this.sectionOneTime, CoreTools.translate('Un mois de télévision gratuite', 'One month of free television'), this.billInfo.promo_free_month_tv.promo_qty.toFixed(2), 'discount');
		}

		if(this.promoDecodeur.length>0){
			this.promoDecodeur.forEach(promo => {
				this.addCharges(this.sectionMonthly, promo.titre_panier, promo.prix, 'discount')
			});
		}
	}

	calcSousTotaux(total, amount, add) {
		if (add) {
			total = total + parseFloat(amount);
		}
		else {
			total = total - parseFloat(amount);
		}
		total = Math.round((total + Number.EPSILON) * 100) / 100;
		return total;
	}


	displaySousTotaux() {
		if(CoreTools.lang == 'fr'){
			this.sousTotalOneTimeSection.parentElement.classList.add('frSpacing');
		}
		this.sousTotalMonthlySection.textContent =  CoreTools.translate(this.monthlySousTotal.toFixed(2)+'$', '$'+this.monthlySousTotal.toFixed(2));
		this.oneTimeSousTotal = this.oneTimeSousTotal + this.monthlySousTotal;
		this.sousTotalOneTimeSection.textContent = CoreTools.translate(this.oneTimeSousTotal.toFixed(2)+'$', '$'+this.oneTimeSousTotal.toFixed(2));
	}



	calcTaxes() {
		if (this.province == 'on') {
			const taxTVHMOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TVH);
			this.displayTotals(this.oneTimeTVH, CoreTools.translate(taxTVHMOneTime.toFixed(2)+'$', '$'+taxTVHMOneTime.toFixed(2)));
			this.total = this.roundAmounts(this.oneTimeSousTotal + taxTVHMOneTime);

		}
		else if (this.province == 'qc') {
			//CALCULER LES TAXES PAR MOIS ET TOTALES
			const taxTPSOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TPS);
			const taxTVQOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TVQ);

			// AFFICHER LES TAXES PAS MOIS ET TOTALES
			this.displayTotals(this.oneTimeTPS, CoreTools.translate(taxTPSOneTime.toFixed(2)+'$','$'+taxTPSOneTime.toFixed(2)));
			this.displayTotals(this.oneTimeTVQ, CoreTools.translate(taxTVQOneTime.toFixed(2)+'$','$'+taxTVQOneTime.toFixed(2)));


			//CALCULER ET AFFICHER LES TOTAUX APRES TAXES

			this.total = this.roundAmounts(this.oneTimeSousTotal + taxTVQOneTime + taxTPSOneTime);

		}


		this.displayTotals(this.totalSection, CoreTools.translate(this.total.toFixed(2)+'$','$'+this.total.toFixed(2)));
		localStorage.setItem('total', this.total);
	}


	displayTotals(section, amount) {
		if(CoreTools.lang == 'fr'){
			section.parentElement.classList.add('frSpacing');
		}
		section.textContent = amount;
	}

	roundAmounts(amount) {
		let res = Math.round((amount + Number.EPSILON) * 100) / 100;
		return res;
	}

	addprixEbox(){
		const sectionEboxPrice = document.getElementById('eboxPrice');
		sectionEboxPrice.textContent = this.billInfo.expected_amount_total;
	}


}
