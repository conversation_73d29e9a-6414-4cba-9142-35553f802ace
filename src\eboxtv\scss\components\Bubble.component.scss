@import '~@eboxtv/scss/helpers/_mixins.scss';
@import '~@eboxtv/scss/global/font-names.scss';

$closeBtnBreakpoint: map-get($ebtvBreakpoints, tabletLandscape);

.Bubble {
    padding-bottom: 13px;
    position: absolute;
    left: 50%;
    bottom: 0;
    margin-bottom: 0px;
    opacity: 0;
    will-change: transform, opacity;
    z-index: 10;

    &--always-show-closer {
        .Bubble {
            &__wrapper {
                padding-top: 40px;
            }

            &__close-btn {
                display: block !important;
            }

            &__content {
                margin-bottom: 0;
                max-height: none;
                overflow: visible;
            }
        }
    }

    &--step-up {
        margin-bottom: 30px;
    }

    &--small {
        .Bubble {
            &__bubble {
                display: flex;
                max-width: 275px;
                max-height: 150px;
                overflow: hidden;
                padding: 8px;
            }

            &__wrapper {
                display: flex;
                flex-direction: column;
                flex: 1 1 auto;
                overflow: auto;
            }

            &__content {
                font: 400 13px/16px $ebtv-f-primary;
                margin-bottom: 0;
                overflow: visible;
            }

            &__close-btn {
                display: none;
            }
        }

        &.Bubble.Bubble {
            &:after {
                margin-left: -13px;
                margin-top: -13px;
                width: 26px;
                height: 8px;
            }
        }
    }

    &--is-bottom {
        &.Bubble.Bubble {
            &:after {
                top: auto;
                bottom: 100%;
                transform: rotate(180deg);
            }
        }
    }

    &__bubble {
        position: relative;
        width: 400px;
        max-width: 100%;
        padding: 18px 20px;
        background: $c-white;
        color: $c-black;
        font-family: $ebtv-f-primary;
        text-align: left;
        cursor: initial;
        border-radius: 0;
        box-shadow: 0 2px 7px 0 rgba(#000, 0.50);
        z-index: 1;

        @media (max-width: $closeBtnBreakpoint) {
            padding-top: 30px;
            width: 300px;
        }
        @media (max-width: 500px) {
            width: 180px;
            left:5px!important;
            right: 0!important;
        }
    }

    &:after {
        content: '';
        position: absolute;
        display: block;
        top: 100%;
        left: 50%;
        margin-left: -18px;
        margin-top: -13px;
        width: 36px;
        height: 13px;
        overflow: hidden;
        background: {
            image: url('../../img/arrow_pop-up.svg');
            position: center -4px;
            size: cover;
        };
        z-index: 1;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            content: none;
        }
    }

    &--is-tooltip {
        &:after {
            @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
                content: '';
            }
        }
    }

    &__wrapper {
        position: relative;

        @media (max-width: $closeBtnBreakpoint) {
            position: static;
        }
    }

    &__corner-container {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
    }

    &__origin-flag {
        display: inline-block;
        width: 28px;
        height: 20px;

        &--mobile {
            display: none;
        }

        @media (max-width: $closeBtnBreakpoint) {
            display: none;

            &--mobile {
                display: inline-block;
            }
        }
    }

    &__position {
        display: inline-block;
        margin: 0 5px;
        padding: 2px 5px;
        color: $c-grey-faded;
        border-left: 1px solid currentColor;
        font: 400 16px/18px $ebtv-f-primary;
    }

    &__title {
        font: 700 20px/24px $ebtv-f-primary;
        margin: 0 0 5px;
    }

    &__pricing-container {
        margin-bottom: 8px;
    }

    &__content-title, &__features-title {
        font: 700 14px/20px $ebtv-f-primary;
        color: $c-medium-grey;
    }

    &__features {
        margin-bottom: 30px;
    }

    &__features-list {
        list-style: none;
        padding: 0;
        margin: -2px;
    }

    &__feature-item {
        display: inline-block;
        font: 400 14px/20px $ebtv-f-primary;
        color: $c-medium-grey;
        padding: 2px;
        
        &:after {
            content: '';
            position: relative;
            display: inline-block;
            height: 20px;
            width: 1px;
            background-color: currentColor;
            font-size: 20px;
            vertical-align: middle;
            color: currentColor;
            margin: 0 5px;
            transform: rotate(15deg);
        }

        &:last-child {
            &:after {
                content: none;
            }
        }
    }

    &__feature-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        fill: currentColor;
        vertical-align: middle;
    }

    &__feature-title {
        vertical-align: middle;
    }

    &__content { }

    &__content-container {
        font: 400 14px/20px $ebtv-f-primary;
        //max-height: 172px;
        overflow: auto;
        margin-bottom: 6px;
        p {
            font: 400 14px/20px $ebtv-f-primary;
            margin: 0 0 10px;
        }
    }

    &__close-btn, &__add-btn {
        @include removeBtnStyles;
    }

    &__close-btn {
        display: none;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        top: 0;
        right: 0;
        width: 44px;
        height: 44px;
        background: $c-black;
        color: $c-white;
        border-radius: 0;

        @media (max-width: $closeBtnBreakpoint) {
            display: flex;
        }
    }

    &__close-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        fill: currentColor;
        margin: auto;
    }

    &__add-btn {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;
        vertical-align: middle;
        color: $c-primary;
        font: 700 14px/20px $ebtv-f-primary;
        height: 39px;
        padding: 0 24px;
        background: $c-primary;
        color: #FFF;
        border-radius: 6px;
        transition: all 0.2s $cubic;
        text-transform: uppercase;
        z-index: auto;

        &:before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            background: $c-white;
            opacity: 1;
            transition: all 0.2s $cubic;
        }
        &:after {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            // z-index: -1;
            left: 0;
            top: 0;
            transform: translateX(-100%);
            background: $c-grey;
            transition: all 0.2s $cubic;
        }
        &:hover {
            background: $c-grey;
            color: $c-white;
            box-shadow: 5px 5px 5px rgba($c-grey, 0.5);
            &:after {
                transform: translateX(0);

            }
            &:before {
                opacity: 0;
            }
        }
    }

    &__add-btn-title, &__add-icon {
        position: relative;
        z-index: 1;
    }

    &__add-icon {
        display: inline-block;
        position: relative;
        bottom: 2px;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 5px;
        fill: currentColor;
    }

    &__bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}