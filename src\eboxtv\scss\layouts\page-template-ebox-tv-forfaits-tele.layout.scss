$f-secondary: "Gloria Hallelujah";
$darkColor: #343434;
$lightColor: #626262;

.page-template-commande-tv-forfait-tele {

	.radio__input {
		display: none;
	  }

	  .radio__radio {
		  align-self: center;
		width: 17px;
		height: 17px;
		border: 2px solid $c-primary;
		border-radius: 50%;
		margin-right: 10px;
		box-sizing: border-box;
		padding: 2px;
		flex-shrink: 0;
		&--marginR {
		  margin-right: 45px;
		}

		&::after {
		  content: "";
		  width: 100%;
		  height: 100%;
		  display: block;
		  background: $c-primary;
		  border-radius: 50%;
		  transform: scale(0);
		  border: 1px solid $c-primary;
		  padding: 2px;
		}
	  }
	  .radio__input:checked + .radio__radio::after {
		transform: scale(1);
	  }

	.col-md-3 {
		margin: 10px 0 0 30px;
	}
	.col-md-9 {
		@media(max-width: 1024px){
			width: 100%;
		}
	}
	.col-md-8 {
		@media(max-width: 1024px){
			width: 100%;
		}
	}

	h2 {
		padding-bottom: 30px;
	}

	.include-txt {
		margin-bottom: 35px;
		}



	.uploadSpeed {
		width: 100%;
		border: 1px solid $lightColor;
		border-radius: 12px;
		text-align: center;
		padding: 40px 25px;
		margin-bottom: 35px;

		&__alert {
			font-size: 18px;
			font-weight: bold;
			padding-bottom: 25px;
		}
	}

	.radioTV {
		display: flex;
		width: 100%;
		align-items: center;
		@media (max-width: 767px) {
			flex-wrap: wrap;
		}
	}

	.surMesure {
		display: flex;
		align-items: center;
		color: $c-primary;
		span {
			color: $darkColor;
		}


		a {
			color: $darkColor;
			&:hover {
				color: $c-primary;
			}
		}

	}
	.page-template-ebox-tv-forfaits-tele__forfait-items {
		.redLink {
			padding-bottom: 10px;
		}
		.TVForfaitItem {
			margin-bottom: 40px;
			&__row {
				padding: 10px 0 !important;
				&--title {
					flex: 0 0 100%;
					flex-direction: row;
					border-radius: 12px 12px 0 0 ;
				}

				&--content {
					flex: 0 0 100%;
					height: 100%;
					.radio__input {
						margin: 20px 0px 0px 30px;
						@media(max-width: 767px){
							display: none;
						}
					}
					.radio__radio {
						margin-left: 40px;
						@media (max-width: 767px) {
							margin: 0 auto;
							display: none;
						}
					}
				}
			}

	&__active {
		border: 2px solid $c-primary;
	}

			&__pricing {
				flex: 0 0 27%;
				@media(max-width: 1200px){
					pointer-events: none;

				}
				@media(max-width: 767px){
					flex: 0 0 100%;
					.ButtonEffect {
						display: inline-block!important;
						pointer-events: none;
					}
				}
			}

			&__features {
				flex: 0 0 62%;
				@media(max-width: 767px){
					flex: 0 0 100%;
				}
			}
			&__feature {
				@media(max-width:767px){
					align-items: center;
				}
			}
			&__feature-title-container {
				h5 {
					font-size: 14px;
				}
			}
		}

}

	.basechannels {
		// padding-bottom: 75px !important;
		margin-right: 60px;
		padding-bottom: 0;
		@media(max-width: 1024px){
			margin-right: 0;
		}
		.mg-bottom {
			margin-bottom: 80px;
		}
	}
		.voip {
			 .voip__title {
				 margin-bottom: 33px;
				 h4 {
					 margin-bottom: 25px;
				 }
				p {
					margin-bottom: 20px;
				}
			 }
			.three-blocs {
				margin-top: 20px;
				.three-blocs__items {
					width: 100%;
				  display: flex;
				  align-items: center;
				  justify-content: space-between;
				  padding: 35px;
				  border: 1px solid #f2f2f2;
				  border-radius: 12px;
				  box-shadow: 0px 1px 6px -1px $darkColor;
				  position: relative;
				  margin-bottom: 30px;
				  background-color: $c-white;
				  @media (max-width: 767px) {
					justify-content: left;
					padding: 25px;
				  }
				  &::before {
					content: "";
					width: 1px;
					height: 90px;
					background: #c2c2c2;
					display: inline-block;
					position: absolute;
					right: 207px;
					bottom: 21px;
					@media (max-width: 767px) {
					  left: 90px;
					  right: 0;
					  height: 65px;
					}
				  }

				  &--selected {
					border: 3px solid $c-primary;
				  }

				  // responsive mobile bandeau noir

				  .top-spec {
					display: none;
					@media (max-width: 767px) {
					  display: flex;
					  flex-direction: row;
					  position: absolute;
					  top: -3px;
					  left: -3px;
					  width: 101.5%;
					  color: white;
					  background-color: #626262;
					  border-radius: 8px 8px 0px 0px;
					  padding: 10px 0;

					  .spec-detail {
						display: flex;
						align-items: center;
						margin-left: 25px;
						&--right {
						  margin-left: 10px;
						}

						.speed-value {
						  font-weight: 500;
						  font-size: 14px;
						  margin: 0 10px;
						}

						.text-credit {
							font-size: 10px;
							margin: 0 10px;
						}

						svg {
						  width: 50px;
						  height: 50px;
						  fill: #c7c7c7;
						}
					  }
					}
				  }
				  //////////
				  .items__left {
					display: flex;
					width: 100%;
					@media (max-width: 767px) {
					  margin: 70px 0px 0px 10px;
					  //width: auto;
					}
					.radio {
					  display: inline-flex;
					  align-items: center;
					  cursor: pointer;
					  margin-right: 10px;
					  font-size: 20px;
					  color: $darkColor;
					  padding: 10px 0;
					  font-weight: 600;

					  img {
						padding: 0px 20px 0px 53px;
					  }
					}

					.optionChoice {
					  display: flex;
					  align-items: center;
					  margin-left: 45px;
					  @media (max-width: 1199px) {
						max-width: 260px;
					  }
					  @media (max-width: 767px) {
						display: none;
					  }
					  svg {
						width: 50px;
						height: 50px;
						fill: #c7c7c7;
					  }
					  .optionChoice-txt {
						margin-left: 20px;
						display: flex;
						flex-direction: column;

						.choiceDetail {
						  font-size: 20px;
						  font-weight: bold;
						}

						.optionChoice-soustexte {
						  font-size: 12px;
						  color: #626262;
						  line-height: 14px;

						}
					  }
					}
					.radioQty {
						position: absolute;
						right: 245px;
						top: 47px;
						@media(max-width: 767px){
							left: 120px;
							top: 98px;
						}
						@media(max-width: 375px){
							right: 190px;
							top: 98px;
						}

						.qtyradio {
							padding: 0px 15px;
							background: #f2f2f2;
							font-size: 18px;
							font-weight: bold;
							text-align: center;
							border: none;
							&::-webkit-inner-spin-button {
								opacity: 1;
							  }
						}
					}
				  }
				  .items__right {
					padding-right: 40px;
					display: flex;
					@media (max-width: 767px) {
					  padding-right: 20px;
					  margin-top: 57px;
					  //flex-grow: 1;
					  //justify-content: center;
					}
					.Listing__prix {
					  font-size: 46px;
					  font-weight: 900;
					  color: $c-primary !important;
					  position: relative;
					  margin-left: 0;
					  padding-left: 20px;
					  @media (max-width: 767px) {
						  margin-left: 100px;
					  }
					  sup {
						font-size: 16px;
						vertical-align: super;
						top: 3px;
					  }
					  span {
						position: absolute;
						right: -27px;
						top: 30px;
						font: 400 10px $f-secondary;
					  }
					  .fr{
						right: -17px;
					  }
					  .en{
						right: -34px;
					  }
					  &--promo {
						font-size: 20px;
						margin-right: 10px;
						color: #343434;
						position: absolute;
						top: 64px;
						right: 150px;
						display: inline-block;
						@media (max-width: 767px) {
						  left: 110px;
						  width: 42px;
						  right: 0;
						  top: 106px;
						}
						sup {
						  font-size: 12px;
						}
						&:after {
						  content: "";
						  width: 100%;
						  height: 1px;
						  background: $c-light-grey;
						  opacity: 1;
						  position: absolute;
						  left: -4px;
						  top: 15px;
						  transform: rotate(0deg);
						}
					  }
					}

					.Listing__promo {
					  width: 82px;
					  height: 72px;
					//   background-image: url(../../img/promo-fr.svg);
					  background-size: 100%;
					  position: absolute;
					  display: inline-block;
					  top: -4px;
					  right: -4px;
					  opacity: 1;
					}
				  }
				}
				.error-message {
				  color: #BE2323;
				  display: none;
				}
			  }

			  .three-blocs.error {
				.error-message {
				  display: block;
				}
			  }
			  .voip {
				margin: 85px 0;
			  }
		// Infonuagique

		&__infonuagique {
			margin-right: 60px;
			@media(max-width: 1024px){
				margin-right: 0;
			}
			svg {
				width: 50px;
				height: 50px;
				fill: #c7c7c7;
			}

			.three-blocs__items {
				width: 100%;
				@media(max-width: 767px){
					padding: 80px 38px 20px !important;
				}

				.top-spec {
					padding: 10px 20px 10px 0 !important;
					.spec-detail{
						@media(max-width: 767px){
							margin-left: 30px !important;

							.text-spec {
								margin-left: 10px;
								line-height: 18px;
							}
							.speed-value {
								font-size: 14px !important;
								margin: 0 !important;
							}
						}
					}
			}


			.cloud-selector__choice-item {
					width: 100%;
					display: flex;
					@media(max-width: 767px){
						align-items: center;
					}
				.cloud-selector__fake-radio{
					align-self: center;
					@media(max-width: 767px){
						margin-top: 30px;
					}
				}

				.infoNuagique--title {
					display: flex;
					align-items: center;
					padding-left: 40px;
					@media(max-width: 767px){
						display: none;
					}
					p {
						max-width: 350px;
						font-size: 18px;
						font-weight: bold;
						padding-left: 20px;
					}
				}

				.items__left {
					@media(max-width:767px){
						margin: 0 !important;
						padding-bottom: 22px;
					}
				}

				.items__right {
					@media(max-width: 767px){
						margin-top: 0 !important;
					}
				}
			}
		}
		}
	}

}