

body.page-template-ebox-tv-landing-php , body.page-template-ebox-tv-landing-copy-php{
	/*background: {
		image: url('../../../img/Body.jpg');
	};*/
	position: relative;
	background: white !important;
}

.FixedContent {
	position: absolute;
	width: 100%;
	box-shadow: 0 0 8px rgba($c-black, 0);
	background-image: url('~@common/img/ebox-texture-background--black.jpg');
	transition: none;
	z-index: 10;

	&--sticky {
		background-image: url('~@common/img/ebox-texture-background--black.jpg');
		box-shadow: 0 4px 8px rgba($c-black, 0.4);
		// padding: 0 0 15px;
		transition: all 0.25s $cubic;

		.FixedContent {
			&__btmborder {
				border-color: transparent;
			}

			&__menu {
				padding: 15px 0;
            }
            &__button {
                margin-left: 10px;
            }
		}
	}

	@media (max-width: 767px) {
		display: none;
	}

	&__title {
		font-size: 25px;
		font-weight: 700;
		color: #C7C7C7;
	}
	&__menu {
		float: right;
		display: flex;
		align-items: center;
		padding: 25px 0;
		transition: padding 0.25s $cubic;
	}
	&__link {
		position: relative;
		display: inline-block;
		font-size: 16px;
		margin: 0 20px;
		line-height: 16px;
		vertical-align: sub;
		text-decoration: none;
		transition: color 0.2s $cubic;

		&--white {
			color: #c7c7c7;
		}

		&:after {
			content: '';
			position: absolute;
			display: block;
			background: {
				image: url('../../../img/hover.svg');
				position: left center;
				repeat: no-repeat;
				size: 100% 15px;
			};
			top: 0;
			left: 0;
			height: 100%;
			width: 0%;
			transition: all 0.1s ease-in-out;
		}

		&:hover {
			color: #FFF;
			&:after {
				width: 100%;
			}
		}

		&:hover, &:focus, &:active {
			text-decoration: none;
		}
	}
	&__btmborder {
		border-bottom: 1px #fff solid;
		transition: border 0.1s $cubic;

		> .row {
			display: flex;
			align-items: center;
		}
	}
	&__nobtmborder {
		transition: border 0.1s $cubic;

		> .row {
			display: flex;
			align-items: center;
		}
	}
}

.HomeMain {
	width: 100%;
	overflow: hidden;
	background: url('../../../img/slidebg.png') center top 0px no-repeat;
	background-size: 100% 280px;
	padding-bottom: 120px;

	&__noBackground{
		background: none;
	}

	@media (min-width: 1500px)  {
		background-size: 100% 270px;
	}
	@media (min-width: 768px) and (max-width: 1199px) {
		background-size: 100% 220px;
	}
	@media (min-width: 320px) and (max-width: 767px) {
		background-size: 100% 110px;
	}
	@media (max-width: 767px) {
		padding-bottom: 50px;
	}

	&__ThreeCallTos.HomeMain__ThreeCallTos {
		padding: 70px 0;
	}

	&__content {
		position: relative;
		opacity: 0;
		transform: scale(0.5);
		transform-origin: center bottom;
		transition: all 0.4s $cubicElastic;

		@media (max-width: 767px) {
			float: left;
			width: 100%;
			padding: 0 15px;
		}
	}

	&__content-arrow {
		display: inline-block;
		position: absolute;
		width: 87px;
		height: 201px;
		bottom: calc(100% - 50px);
		right: 7%;
		opacity: 0;
		transition: opacity 0.2s ease-out;

		@media (max-width: 1199px) {
			width: 60px;
			height: calcFromRatio(60px, 201 / 87);
			bottom: calc(100% - 35px);
			right: 7%;
		}
		@media (max-width: 991px) {
			display: none;
		}
	}

	&__subtitle{
		color: white;
		text-align: center;
		font: 24px $f-secondary;
	}

	&__nopb{
		padding-bottom: 0 !important;
	}

	&__desc {
		font: 400 16px/22px;
		text-align: center;
		color: $c-medium-grey;
		color: #FFF;

	}
	&__svg--white::after {
		@media (max-width: 767px) {
			width: 60px;
			height: 60px;
			right: -54px;
			bottom: -52px;
		}
	}
	&__title {
		text-transform: uppercase;
		text-align: center;
		font: 900 45px/50px $f-primary !important;
		color: $c-medium-grey;
		padding-top: 20px;
		padding-bottom: 20px;
		color: #FFF;
		@media (max-width: 1199px) {
			font: 900 26px/32px $f-primary !important;
		}
		@media (max-width: 767px) {
			font: 900 24px/24px $f-primary !important;
		}
	}
	&__main-btn {
		margin-top: 65px;
		display: flex;
		justify-content: center;
		width: 100%;
		text-align: center;
	}

	&__spacer{
		margin-top: 115px;

		@media screen and (max-width: 430px){
			margin-top: 0;
		}
	}

	&__exclamation_icon{
		height: 60px;
		width: 60px;

		@media screen and (max-width: 500px){
			display: none;
		}
	}

	&__smile_icon{
		fill: white;
		height: 40px;
		width: 60px;
		bottom: 11%;
		left: 31%;

		@media screen and (max-width: 780px){
			left: 22%;
		}

		@media screen and (max-width: 530px){
			left: 10%;
		}

		@media screen and (max-width: 430px){
			display: none;
		}
	}

	&__peace_icon{
		fill: white;
		height: 60px;
		width: 60px;
		margin-top: 20px;

		@media screen and (max-width: 500px){
			display: none;
		}
	}

	&__subtitle_abitibi{
		margin-top: 20px;
	}

	.ebox {
		float: none;
		width: 858px;
		height: 353px;
		margin: 100px auto 0;
		background: {
			image: url('../../../img/ebox-tv-decodeur.webp');
			size: 90%;
			repeat: no-repeat;
			position: center;
		};

		&--loaded {
			+ .HomeMain {
				&__content {
					opacity: 1;
					transform: scale(1);

					.HomeMain__content-arrow {
						opacity: 1;
						transition-delay: 0.4s;
					}
				}
			}
		}

		

		@include mediaScreenRatio(2) {
			background-image: url('../../../img/ebox-tv-decodeur.webp');

			@media (max-width: 1200px) {
				background-image: url('../../../img/ebox-tv-decodeur.webp');
			}
		}	
		@media (max-width: 1199px) {
			max-width: 684px;
			//background-size: contain;
			//background-position: 20px 0;
			margin: 120px auto 0;
			height: 290px;
		}
		@media (max-width: 767px) {
			max-width: 300px;
			//background-size: contain;
			//background-position: 5px 0;
			background-image: url('../../../img/ebox-tv-decodeur.webp');
			margin: 40px auto 0;
			height: 150px;
		}
		@media (max-width: 767px) {
			max-width: 300px;
			//background-size: contain;
			//background-position: 5px 0;
			background-image: url('../../../img/ebox-tv-decodeur.webp');
			margin: 40px auto 0;
			height: 150px;
		}
	}

	.ebox--fullService {
		float: none;
		width: 858px;
		height: 520px;
		margin: 100px auto 45px;
		background: {
			image: url('../../../img/home-forfait-device2x.png');
			size: 90%;
			repeat: no-repeat;
			position: center;
		};

		&--loaded {
			+ .HomeMain {
				&__content {
					opacity: 1;
					transform: scale(1);
				}
			}
		}

		@media screen and (max-width: 768px ) {
			width: 700px;
		}

		@media screen and (max-width: 450px ) {
			width: 440px;
			height: 290px;
			margin: 45px auto;
		}

		@media screen and (max-width: 375px ) {
			width: 330px;
			height: 225px;
			margin: 45px auto;
		}
	}
}