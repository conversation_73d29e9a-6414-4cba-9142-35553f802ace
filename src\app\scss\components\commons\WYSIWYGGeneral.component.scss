.WYSIWYGGeneral {
    padding: 100px 0;
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    li {
        a {
            color: #BE2323;
            &:hover {
                color: $c-black;
            }
        }
    }
    h1,
    h2,
    h3 {
        margin: 0 0 30px;
    }
    h4, h5, h6 {
        margin: 0 0 10px;
    }
    p {
        margin: 0 0 30px;
        &:last-of-type {
            margin: 0;
        }
    }
    h2 + p, h3 + p, h4 + p, h5 + p, h6 + p, ul + p {
        margin: 0 0 30px!important;
    }
    .offpeak-left {
        padding-left: 0;
    }
    .offpeak-right {
        padding-right: 0;
    }
    &__custom {
        text-align: center;
    }
    &__customTitle {
        text-transform: uppercase;
        color: $c-grey;
        font: 900 45px/50px $f-primary;
        text-align: center;
        margin: 0;
        @media (max-width: 991px) {
            font: 900 26px/32px $f-primary;
        }
        @media (max-width: 767px) {
            font: 900 22px/24px $f-primary;
        }
    }
}

body.page-id-5923 {
    .WYSIWYGGeneral {
        h2 {
            margin: 0!important;
        }
    }
}

body.postid-7737 {
    .Wysiwyg {
        p.p1 {
            margin: 0 0 30px;
        }
    }
}

.tg {
    margin-bottom: 30px;
}

.nopaddingbottom {
    padding-bottom: 0!important;
}