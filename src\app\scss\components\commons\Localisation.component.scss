
.Localisation {
    position: relative;
}
.ChangeLocalisation {
    background: url('../../../img/ebox-texture-background--red.jpg') left top repeat;
    position: absolute;
    min-width: 210px;
    border-radius: 15px;
    box-shadow: 0 2px 14px rgba($c-black, 0.5);
    top: -400px;
    right: -140px;
    opacity: 1;
    transition: $t-primary;
    z-index: 10;
    visibility: hidden;
    @media (max-width: 1460px) {
        right: 0;
    }
    &--opened {
        opacity: 1;
        top: 50px;
    }
    &--footer {
        top: auto;
        bottom: -230px;
        right: 30px;
        position: fixed

    }
    &__title {
        background: $c-grey;
        width: 100%;
        padding-left: 20px;
        padding-right: 20px;
        text-align:left;
        border-top-left-radius: 15px;
        border-top-right-radius: 15px;
        p {
            font: 700 16px/45px $f-primary!important;
            color: $c-white!important;
        }
        h2 {
            font: 700 16px/45px $f-primary!important; 
            text-transform: none!important;
        }
        button {
            border:0;
            background: 0;
            position:absolute;
            right: 10px;
            top: 10px;
            svg {
                width: 20px!important;
                height: 20px!important;
                fill: $c-white!important;
            }
            &:focus {
                outline: 0;
            }
        }
    }
    &__content {
        padding: 20px;
        width: 100%;
        box-sizing: border-box;
        color: $c-black;
        form {
            text-align: center;
            select {
                appearance: none;
                background: url('../../../img/Icon/ic_dropdown.svg') right 7px no-repeat;
                background-size: 16px 16px;
                border-bottom: 1px solid $c-white;
                color: $c-white;
                border-left: 0;
                border-right: 0;
                border-top: 0;
                border-radius: 0;
                font: 700 16px/22px $f-primary;
                &.last {
                    margin-top: 20px;
                }
                &:focus {
                    outline: auto!important;
                    padding: 5px;
                }

                option {
                    color: #000;
                }
            }
        }
    }
}
.Circle {
    background: url('../../../img/circle.svg') center center no-repeat;
    width: 23px;
    height: 23px;
    margin-right: 8px;
    display: flex;
    flex-basis: 23px;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    position: relative;
    &:after {
        content: '';
        width: 1px;
        height: 30px;
        background: $c-white;
        position: absolute;
        left: 11px;
        top: 23px;
    }
    svg {
        height:14px;
        width: 14px;
        fill: $c-primary;
        &.language {
            width: 12px;
            height: 12px;
        }
    }
    &--second {
        margin-top: 20px;
        &:after {
            display: none;
        }
    }
}
.btn-change {
    //line-height: 30px;

    background: $c-white;
    border-radius: 6px;
    text-transform: uppercase;
    font: 700 14px/30px $f-primary;
    color: $c-primary;
    padding: 0 30px 0 20px;
    margin: 20px auto 0;
    border: 0;
    transition: $t-primary;
    svg {
        fill: $c-primary;
        vertical-align: sub;
        transition: $t-primary;
        width: 16px;
        height: 16px;
    }
    &:hover {
        background: $c-grey;
        color: $c-white;
        svg {
            fill: $c-white;
        }
    }
    &:active,
    &:focus {
        outline: auto;
    }
}

.SelectRegion,
.SelectLangue {
    width: 100%;
    display: flex;
    select {
        width: 100%
    }
}

.ChangeLocalisation--opened {
    right: -140px;
    visibility: visible;
    @media(max-width: 1460px){
        right: 0;
    }
    &.ChangeLocalisation--footer {
        bottom: 30px;
    }
}
footer {
    .TopContact__link {
        svg {
            fill:$c-medium-grey;
        }
    }
}

.FermerLocaliste {
    &:focus {
        outline: auto!important;
        height: 22px;
    }
}