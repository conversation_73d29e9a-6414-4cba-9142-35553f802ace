.ModernGallery {
	width: 100%;
	max-width: 600px;
	padding-bottom: 24px;

	.owl-carousel {
		display: block;
	}

	.owl-stage-outer {
		width: 100%;

		img {
			width: 100%;
			max-width: 100%;
		}
	}

	&__wrapper {
		position: relative;
	}
	
	&__carousel-wrapper {
		min-height: 350px;
		border: 1px solid #D8D8D8;
		@media (max-width: 767px) {
            min-height: 0;
        }
	}

	&__controls {
		position: absolute;
		width: auto;
		top: 102%;
		left: 50%;
		transform: translate(-50%, -50%);
		font-size: 0;
		z-index: 1;

		.CarouselControls {
			&__control-icon {
				fill: $c-light-grey;
				width: 32px;
				height: 32px;
			}
		}
	}

	&__control {
		display: inline-flex;
		justify-content: center;
		align-items: center;
		width: 48px;
		height: 48px;
		font-size: 0;
		margin: 0;
		background: $c-grey;
		transition: background-color 0.1s linear;

		&:hover {
			background-color: $c-primary;
		}
	}

	&__ribbon {
		z-index: 1;
	}
}