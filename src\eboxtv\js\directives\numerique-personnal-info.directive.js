/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { Tools } from '@common/ts/services/Tools.service';
import { QualifyService } from '@common/ts/services/Qualify.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';

export class NumeriquePersonalInfoDirective extends Directive {

	// INSTANCE DU SERVICE
	_$OrderService = OrderOnlineService.getInstance()

	qualificator = QualifyService.getInstance();
	Qualification = this.qualificator.getQualification();

	static selector = '[personal-info-numerique]';

	// VARIABLES POUR LE FONCTIONNEMENT DU VALIDATEUR
	$form;
	$valid = true;
	$btnSubmit;
	$inputs = this.toArray(document.forms['personalInfo'].elements);

	creditRequest;
	availability;
	DSLInstallFee = 49.95;
	shippingFee = 15;
	isLastAddressCanadian = false;
	isCreditRequestDone = false;
	isCreditRequestApproved = false;
	equipementsInternet = {};
	equipementsPhone = {};
	inputDebounce;

	validatedDeliveryAddress = {};
	validatedLastAddress = {};
	qualificationAddress = {};
	temporaryAddressSuggestions = [];
	lastErrorParentId = null;
	internetPlan = {};

	newRequest;
	isBuying;



	// transforme les tableaux d'elements html en array
	toArray(obj) {
		var array = [];
		for (var i = obj.length >>> 0; i--;) {
			array[i] = obj[i];
		}
		return array;
	}

	// LANCE LA VALIDATION DE TOUS LES CHAMPS DU FORMULAIRE
	validateForm() {
		this.$valid = true;
		this.lastErrorParentId = null;
		let formIndex = [];
		this.$inputs.forEach(input => {
			!formIndex.includes(input.name) ? formIndex.push(input.name) : null;
		});
		formIndex.forEach(index => {
			this.sendToValidator(document.forms['personalInfo'][index])
		});

		this.validateAvailabilities();

		if (this.lastErrorParentId) {
			jQuery('html, body').animate({
				scrollTop: jQuery(this.lastErrorParentId).offset().top - 100
			}, 1000);
		}
	}

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {

		// fin du test
		this.internetPlan = '';
		var internetLocal = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : '';
		this.internetPlan = internetLocal;
		//AFFICHER LE BON FORMULAIRE DE DISPONIBILITES SELON LA SELECTION DE FORFAIT ET LA QUALIFICATION
		if(this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'){
			// A AJOUTER UNE CONDITION SELON LA QUALIFICATION ADSL POUR AFFICHER LE BON FORMULAIRE
			document.querySelector('[name="availability_template_id"]').value = 1;
			document.querySelector('.cable').remove();

			if(parseInt(this.internetPlan.downloadSpeed) >= 25){
				document.querySelector('.adsl2').classList.remove('hidden');
				document.querySelector('.adsl1').remove();
			}
			else{
				document.querySelector('.adsl1').classList.remove('hidden');
				document.querySelector('.adsl2').remove();
			}


		}else{
			document.querySelector('.cable').classList.remove('hidden');
			document.querySelector('.adsl1').remove();
			document.querySelector('.adsl2').remove();
		}
		if(internetLocal.technology == 'c-cable'){
			this.DSLInstallFee = 29.95;
		}
		else if(this.internetPlan.cableTech == 'DSL'){
			this.DSLInstallFee = 49.95;
			if(parseInt(this.internetPlan.cableTech.downloadSpeed) < 25){
				this.DSLInstallFee = 0;
			}
		}



		jQuery('.full-screen-spinner-background').hide();
		this.hideSuggestions();
		let stepValidation = this._$OrderService.getStepValidation();

		/*if (!stepValidation.internet || !stepValidation.phone || !stepValidation.tv) {
			//if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			//if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
		} else {*/
			this.form = document.forms['personalInfo']
			this._$OrderService.setForm(this.form);
			this._$OrderService.initFormInformationPersonnel();
			this.setQualificationAddress();
			this.placeValues(this._$OrderService._retInfoClient());
			this.updateFullName();
			this.placeValues(this._$OrderService._retPersonalInfoToggle());
			this.availability = this._$OrderService._retAvailibilities();
			this.storageCheckedValues(this.availability.availability_days);
			this.form.dateFrom.value = this.availability.dateFrom;
			this.storageCheckedValues({ 'consent': this.availability.consent });
			this.placeValues(this._$OrderService._retShippingInfo());

			this.equipementsInternet = this._$OrderService._retInternetEquipement();
			this.equipementsPhone = this._$OrderService._retPhoneEquipement();

			this.creditRequest = this._$OrderService._retCreditInfo();
			this.placeValues(this.creditRequest);

			// Afficher le formulaire Equifax si le client veut/doit louer les équipements:
			jQuery('.creditSection').toggleClass('hidden', !this._$OrderService.mustRentEquipementNumerique());
			jQuery('.order-summary__navbutton').toggleClass('disabled', this._$OrderService.mustRentEquipementNumerique());
			jQuery('.eboxtv-navbutton').toggleClass('disabled', this._$OrderService.mustRentEquipementNumerique());
			if(this._$OrderService.mustRentEquipementNumerique()){
				jQuery('.btnifBuy').hide();
			}
			else{
				const resizeObserver = new ResizeObserver(entries => {
					if(!this._$OrderService.mustRentEquipementNumerique()){
						if(jQuery('window').width > 1240){
							jQuery('.btnifBuy').show();
						}
					}
				});
				
				  resizeObserver.observe(document.body);
			}
			

			// QUAND ON CHANGE UN CHAMP DANS LE FORMULAIRE ON LANCE LA VALIDATION DU INPUT MODIFIÉ
			this.form.addEventListener('change', function (evt) {
				this.optionsChange(evt.target);
				this.sendToValidator(evt.target);
			}.bind(this));

			// Bouton Valider
			jQuery('#submitBtn').on('click', (evt) => {
				this.sendFormNumerique(evt);
			});

			// Bouton Continuer

			jQuery('.order-summary__navbutton').add('.eboxtv-navbutton').on('click', (evt) => {
				if (this._$OrderService.mustRentEquipementNumerique()) {
					if (!this.isCreditRequestApproved) {
						evt.preventDefault();
					}
				} else {
					this.sendFormNumerique(evt);
				}
			});

			
			// Suggestion d'adresses
			jQuery('#lastAddress, #addDelivery')
				.on('input focus', this.onAddressInput.bind(this))
				.on('keydown', (event) => { if (event.key === 'Escape') { this.hideSuggestions() } });

			jQuery('#userPhone').on('input', this.phoneValidator.bind(this));
			jQuery('#userFirstName' ).on('input', this.validateNames.bind(this));
			jQuery('#userLastName' ).on('input', this.validateNames.bind(this));
			jQuery('#userMail' ).on('input', this.emailValidator.bind(this));



			jQuery('#addDeliverySuggestions, #lastAddressSuggestions').on('click', 'li', this.onSuggestionClick.bind(this));

			jQuery('.modifyOrderLink').on('click', () => { jQuery('#internet-trigger').trigger('click') });


			document.querySelector('#datetimepicker').addEventListener('blur', function (evt) {
				this.sendToValidator(evt.target);
			}.bind(this));

			// Vérifier le input haveInternet
			if (document.querySelectorAll('input[name="haveInternet"]:checked').length == 1) {
				this.optionsChange(document.querySelector('input[name="haveInternet"]:checked'));
			}

			jQuery('.birthDate-select').removeClass('error');
		//}
	}

	setQualificationAddress() {
		this.qualificationAddress = this._$OrderService._retQualificationAddresse();

		let dn = this.qualificationAddress.dn != undefined && this.qualificationAddress.dn.toString().trim() ? this.qualificationAddress.dn.toString().trim() + ' ' : '';
		let app = this.qualificationAddress.app ? this.qualificationAddress.app.toLowerCase() + ' ' : '';
		let stc = this.qualificationAddress.stc ? this.qualificationAddress.stc.toLowerCase() : '';
		let sdc = this.qualificationAddress.sdc ? ' ' + this.qualificationAddress.sdc : '';
		let sn = this.qualificationAddress.sn ? this.qualificationAddress.sn.charAt(0) + this.qualificationAddress.sn.slice(1).toLowerCase() : '';
		let mn = this.qualificationAddress.mn ? this.qualificationAddress.mn.charAt(0) + this.qualificationAddress.mn.slice(1).toLowerCase() + ', ' : '';
		let pc = this.qualificationAddress.pc ? this.qualificationAddress.pc : '';
		let zip = this.qualificationAddress.zip ? this.qualificationAddress.zip.slice(0, 3) + ' ' + this.qualificationAddress.zip.slice(3) : '';

		let street = (this.qualificationAddress.stc === 'ST' ? sn + ' ' + stc : stc + ' ' + sn) + sdc;

		jQuery('#addressStreet').text(dn + app + street);
		jQuery('#addressCityProvince').text(mn + pc);
		jQuery('#addressPostalCode').text(zip);
	}

	/**
	 * DETECTE LE TYPE DE INPUT MODIFIÉ ET LANCE LA VALIDATION DU CHAMP
	 * @param {INPUT} input QUI A ETE MODIFIÉ
	 */
	sendToValidator(input) {
		if (input) {
			if (input.length > 1) {
				input.tagName !== 'SELECT' ? this.validateRadio(input[0]) : this.validateSelect(input);
			}
			else {
				switch (input.type) {
					case 'text':
						this.validateText(input);
						break;
					case 'checkbox':
						this.validateCheckbox(input);
						break;
					case 'radio':
						//NE DEVRAIT JAMAIS SE RENDRE ICI
						this.validateRadio(input);
						break;
					default:
						//NE DEVRAIT JAMAIS SE RENDRE ICI
						this.validateSelect(input);
						break;
				}
			}
		}
	}

	/**
	 * VALIDE LES INPUT DE TYPE TEXTE ET LES ENVOIE A DES VALIDATEURS DIFFERENTS SI CERTAINS SPECIFIQUES SONT DETECTÉS
	 * @param {input} input
	 */
	validateText(input) {
		this._$OrderService._infoFormDataBuilder(input);
		if (input.required) {
			if (input.name === 'userPhone') {
				this.phoneValidator();
			} else if (input.name === 'userMail') {
				this.emailValidator(input)
			}
			else {
				// SI CE N EST PAS UN INPUT SPECIFIQUE ON AFFICHE OU RETIRE LE MESSAGE D ERREUR SI LE CHAMP EST VIDE
				const parent = input.parentElement;
				input.value.trim() === '' ? this.setError(parent) : this.removeError(parent);

				if (input.name === 'userFirstName' || input.name === 'userLastName') {
					this.validateNames(input);
					this.updateFullName();
				}
			}
		}
		// VALIDATION DES FRIEND CODE ICI, ENCORE UNE FOIS NE DEVRAIT PAS ETRE ICI (KEY UP EVENT OU QUELQUE CHOSE DU GENRE)
		else if (input.name === 'friendCode') {
			if (input.value.trim() === '') {
				parent = input.parentElement;
				this.removeError(parent);
			}
			else {
				this.friendCodeValidator(input)
			}
		}
	}

	updateFullName() {
		let fullName = document.getElementById('userFirstName').value + ' ' + document.getElementById('userLastName').value;
		document.getElementById('showUserFullName').innerText = fullName;
	}

	/**
	 * RECOIS UN INPUT DE TYPE CHECKBOX POUR LE VALIDER
	 * @param {INPUT} input INPUT A VALIDER
	 */
	validateCheckbox(input) {
		this._$OrderService._infoFormDataBuilder(input); // ENREGISTRE LES DONNEES DANS LE LOCALSTORAGE
		if (input.required) {
			const parent = input.parentElement;
			input.checked ? this.removeError(parent) : this.setError(parent);
		}
		this.validateAvailabilities();
	}

	/**
	 * RECOIS UN INPUT DE TYPE SELECT RADIO
	 * @param {input} input INPUT A VALIDER
	 */
	validateRadio(input) {
		this._$OrderService._infoFormDataBuilder(input); // ENREGISTRE LES DONNEES DANS LE LOCALSTORAGE
		let inputs = document.forms['personalInfo'][input.name];
		let radios = this.toArray(document.forms['personalInfo'][input.name])
		radios.forEach(input => {
			input.checked ? input.parentElement.classList.add('radio--red') : input.parentElement.classList.remove('radio--red')
		});

		if (inputs[0].required) {
			let parent = inputs[0].parentElement.parentElement;
			inputs.value.trim() !== '' ? this.removeError(parent) : this.setError(parent);
		}
	}

	/**
	 * VALIDER LES CHAMPS SELECT
	 * @param {input} input CHAMP INPUT RECU
	 */
	validateSelect(input) {
		this._$OrderService._infoFormDataBuilder(input);
		if (input.required) {
			//VALIDER LA DATE DE FETE
			if (input.name.includes('Birth')) {
				this.birthDayValidator(input);
			}
			else {
				const parent = input.parentElement;
				input.required && input.value === 'defaultValue' ? this.setError(parent) : this.removeError(parent);
			}
		}
	}

	/**
	 * VALIDATEUR CUSTOM POUR LE NUMERO DE TELEPHONE
	 * REMPLACE LA VALEUR DU CHAMPS POUR UN FORMAT SPECIFIQUE
	 * @param {input} input INPUT A VALIDER
	 */
	phoneValidator() {

		clearTimeout(this._inputDebounce);
		let input = document.getElementById('userPhone');


		var phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
		input.value = input.value.replace(/[^0-9 ()-]/, '');
		const parent = input.parentElement;
		if (input.value.trim() !== '' && phoneRegex.test(input.value)) {
			input.value = input.value.replace(phoneRegex, "($1) $2-$3");
			this.removeError(parent);
		} else {
			this.setError(parent);
		}

	}

	/**
	 * VALIDATEUR CUSTOM POUR LES COURRIEL
	 * @param {input} input INPUT A VALIDER
	 */
	emailValidator() {
		let input = document.getElementById('userMail');
		const parent = input.parentElement;
		const re = /\S+@\S+\.\S+/;
		input.value = input.value.replace(/[^A-Za-z0-9.@+_-]/, '');
		if (input.value.trim() !== '' && re.test(input.value)) {
			this.removeError(parent);
		}
		else {
			this.setError(parent);
		}
	}

	/**
	 * VALIDATEUR CUSTOM POUR LA DATE DE FETE
	 * @param {input} input INPUT A VALIDER
	 */
	birthDayValidator(input) {
		const parent = input.parentElement;
		const day = document.forms['personalInfo']['BirthDay'];
		const month = document.forms['personalInfo']['BirthMonth'];
		const year = document.forms['personalInfo']['BirthYear'];

		if (day.value == 'defaultValue' || month.value == 'defaultValue' || year.value == 'defaultValue') {
			parent.classList.add('error');
			this.setError(parent);
		} else {
			this.removeError(parent);
			let is18orMore = this.isDate18orMoreYearsOld(parseInt(year.value), parseInt(month.value), parseInt(day.value));
			if (is18orMore) {
				parent.classList.remove('error');
				jQuery('#isTooYoungText').hide();
			} else {
				this.$valid = false;
				this.lastErrorParentId = document.getElementById('isTooYoungText');
				jQuery('#isTooYoungText').show();
			}
		}
	}

	isDate18orMoreYearsOld(year, month, day) {
		return new Date(year + 18, month - 1, day) <= new Date();
	}

	/**
	 * VALIDE SI LE CODE D AMI EST VALIDE OU NON
	 * @param {Input} input INPUT A VALIDER
	 */
	friendCodeValidator(input) {
		this.validateCodeRequest(input)
			.then(resp => {
				const parent = input.parentElement;
				resp == 1 || resp == '1' ? this.removeError(parent) : this.setError(parent);
				resp == 1 || resp == '1' ? parent.classList.add('succes') : parent.classList.remove('succes');

			}, err => {
				console.error(err);
			});
	}

	/**
	 * REQUETE AJAX POUR VALIDER LE CODE DE REFERENCEMENT
	 * @param {INPUT} input
	 * @returns
	 */
	validateCodeRequest(input) {
		return new Promise((resolve, reject) => {
			const data = {
				code: input.value
			}

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validate_clientCode' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}

	validateAvailabilities() {
		this.availability = this._$OrderService._retAvailibilities();
		jQuery('#availabilities').toggleClass('error', this.availability.isEmpty);
		if (this.availability.isEmpty) {
			this.$valid = false;
		}
	}

	validateNames(input){

		if(!input.name){
			input = input.target;
		}
			input.value = input.value.replace(/[^A-Za-zàèìòùÀÈÌÒÙáéíóúýÁÉÍÓÚÝâêîôûÂÊÎÔÛãñõÃÑÕäëïöüÿÄËÏÖÜŸçÇßØøÅåÆæœ-\s]/gi, "");

	}

	/**
	 * AJOUTE L ERREUR AU CHAMP INPUT
	 * @param {parent} parent PARENT DU INPUT MODIFIER
	 */
	setError(parent) {
		parent.classList.add('error');
		this.$valid = false;
		this.lastErrorParentId = parent;
	}

	/**
	 * RETIRE L ERREUR AU CHAMP INPUT
	 * @param {parent} parent PARENT DU INPUT MODIFIER
	 */
	removeError(parent) {
		parent.classList.remove('error');
	}

	/**
	 * ENVOIE LE FORMULAIRE SI AUCUNE ERREUR N'A ETE DETECTEE
	 */
	sendFormNumerique(event) {
		jQuery('.full-screen-spinner-background').show();
		event.preventDefault();
		this.validateForm();
		jQuery('#resultDescription div').addClass('hidden');
		this._$OrderService._personalInfoFormStorage(document.forms['personalInfo']);
		if (this.$valid) {
			this._$OrderService.sendFormNumerique().then(resp => {
				jQuery('.full-screen-spinner-background').hide();
				if (resp && resp.RESPONSE.status === 'OK') {
					// Si credit_response est null, c'est qu'on n'a pas fait de demande equifax (achat seulement);
					let responseDivID = resp.INFO.credit_response ? resp.INFO.credit_response.action : '';
					let depositAmount = resp.INFO.credit_response ? resp.INFO.credit_response.total_deposit_amount || 0 : 0;

					this.isCreditRequestDone = true;
					this.isCreditRequestApproved = responseDivID !== 'MUST_BUY_EQUIPMENTS';

					jQuery('.order-summary__navbutton').toggleClass('disabled', !this.isCreditRequestApproved);
					jQuery('.eboxtv-navbutton').toggleClass('disabled', !this.isCreditRequestApproved);

					
					this._$OrderService.setStepValidation('personalInfo', this.isCreditRequestApproved);

					this._$OrderService.setCreditRequest(resp.INFO);
					jQuery('#depositAmount').text(depositAmount);
					jQuery('#' + responseDivID).removeClass('hidden');
					if(responseDivID !== ''){
						jQuery('#' + responseDivID + ' .order-tabs__footer').removeClass('hidden');
					}
					jQuery('#submitBtn').addClass('hidden');
					if (event.currentTarget.href) {
						//window.location.href = event.currentTarget.href;
                        window.location.href = '../confirmation-de-commande-paiement-numerique/';
                        
					}
				} else {
					jQuery('#technicalError').removeClass('hidden');
					if(resp.RESPONSE.statuscode){
						let span = document.createElement('SPAN');
						span.textContent = ' ( ' + resp.RESPONSE.statuscode + ' )';
						document.querySelectorAll('#technicalError p')[0].appendChild(span);
					}
				}
			});
		} else {
			jQuery('.full-screen-spinner-background').hide();
			jQuery('#invalidForm').removeClass('hidden');

		}
	}

	onSuggestionClick(event) {
		const { target } = event;
		this.hideSuggestions();
		let addressId = target.getAttribute('from');
		let newAddress = this.temporaryAddressSuggestions.find(ad => ad.id == target.id);

		jQuery('#' + addressId).val(target.innerHTML);

		if (addressId === 'lastAddress') {
			this.validatedLastAddress = newAddress;
			this._$OrderService._set_addresses(newAddress);
		} else {
			this.validatedDeliveryAddress = newAddress;
			this._$OrderService._set_addressesShipping(newAddress);
		}
	}

	onAddressInput(event) {
		const { target } = event;

		clearTimeout(this._inputDebounce);

		if (target.value.trim().length) {
			this._inputDebounce = setTimeout(this.searchAddress.bind(this, target), 500);
		} else {
			this.hideSuggestions();
			target.name === 'lastAddress' ? this._$OrderService._set_addresses({}) : this._$OrderService._set_addressesShipping({});
		}
	}

	onAddressBlur(event) {
		const { target } = event;
		this.hideSuggestions();
	}

	searchAddress(target) {
		let suggestionsDivID = '#' + target.name + 'Suggestions';
		jQuery(suggestionsDivID).empty().html('<div class="lds-ellipsis"><div></div><div></div><div></div><div></div></div>').show();
		this.callAddressAPI(target.value.trim())
			.then(resp => {
				this.temporaryAddressSuggestions = resp;
				let newHtml = '';
				if (resp.length) {
					let lastResult = ''
					newHtml = '<ul>';
					resp.forEach(adr => {
						let address = this.concatAddressFields(adr);
						if (address !== lastResult) {
							newHtml += '<li id="' + adr.id + '" from="' + target.name + '" class="suggestion">' + address + '</li>';
						}
						lastResult = address;
					});
					newHtml += '</ul>';
				} else {
					newHtml = '<p class="text">' + CoreTools.translate('Aucun résultat pour: ', 'No result for: ') + target.value.trim() + '</p>'
				}
				jQuery(suggestionsDivID).empty().html(newHtml);
			}, err => {
				console.error(err);
			});
	}

	concatAddressFields(address) {
		let dn = address.dn.toString().trim() ? address.dn.toString().trim() + ', ' : '';
		let stc = address.stc ? address.stc + ' ' : '';
		let sn = address.sn ? address.sn + ' ' : '';
		let mn = address.mn ? address.mn + ', ' : '';
		let pc = address.pc ? address.pc + ', ' : '';
		let zip = address.zip ? address.zip : '';

		return dn + stc + sn + mn + pc + zip;
	}

	hideSuggestions() {
		jQuery('#addDeliverySuggestions, #lastAddressSuggestions').hide();
	}

	callAddressAPI(search) {
		return new Promise((resolve, reject) => {
			const data = {
				filter: this.isLastAddressCanadian ? "canada" : "",
				search,
				checkprov: "QC",
				lng: Tools.lang,
				action: 'searchAddressAC'
			};
			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validateur' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}

	/**
	 * LOOP DANS LES VALEURS RETOURNÉES PAR LE LOCAL STORAGE ET PLACE LES VALUE DANS LE BON CHAMP
	 * @param {Object} obj
	 */
	placeValues(obj) {
		for (const [key, value] of Object.entries(obj)) {
			if (key === 'addDelivery' || key === 'lastAddress') {
				const adr = jQuery.isEmptyObject(value) ? '' : this.concatAddressFields(value);
				this.form[key].value = adr;
			} else {
				this.form[key].value = value;
				this.optionsChange(this.form[key]);
			}
		}
	}

	/**
	 * LOOP DANS DES VALEURS CHECKBOX POUR PLACER LES VALEURS : (PEUT SUREMENT ETRE FUSIONNÉ AVEC LA FUNCTION PRECEDENTE)
	 * @param {OBJECT} obj
	 */
	storageCheckedValues(obj) {
		for (const [key, value] of Object.entries(obj)) {
			if (this.form[key]) {
				this.form[key].checked = value;
			}
		}
	}

	/**
	 * RECOIS UN INPUT POUR MODIFIER LES CHAMPS REQUIS ET CEUX A CACHER
	 * @param {INPUT} input INPUT A VALIDER
	 */
	optionsChange(input) {
		if (input.length > 1) {
			if (input.tagName == 'SELECT') {
				if (input.name.includes('Birth')) {
					this.birthDayValidator(input);
				}
			}
			else {
				let radios = this.toArray(document.forms['personalInfo'][input[0].name])
				radios.forEach(radio => {
					radio.checked ? input = radio : '';
				});
			}
		}
		switch (input.name) {
			//TOGGLE APPARTEMENT SECTION
			case 'isAppartement':
				this.appartementSelectionToggle(input);
				break;
			//TOGGLE OLD INTERNET PROVIDER OPTIONS
			case 'haveInternet':
				this.haveInternetToggle(input);
				break;
			//TOGGLE LA SELECTION GOUPILLE ET DE LA TECHNOLOGIE AU CHANGEMENT DE OLD PROVIDER
			case 'internetProvider':
				this.changeInternetProvider(input);
				break;
			//TOGGLE LA DELIVERY SI AU CHANGEMENT DU BOUTON RADIO
			case 'delivery':
				this.deliveryToggle(input);
				break;
			//TOGGLE LA DELIVERYFORM SI AU CHANGEMENT DU BOUTON RADIO
			case 'sameDeliveryAddress':
				this.deliveryAddressChange(input);
				break;
			//TOGGLE LA DELIVERYAPP SI AU CHANGEMENT DU BOUTON RADIO
			case 'deliveryAppartement':
				this.deliveryAppToggle(input);
				break;
			//TOGGLE DU CONSENTEMENT DE CREDIT
			case 'creditConsent':
			case 'oldLivingAddress':
				this.manageCreditConsent();
				break;
			case 'oldTech':
				this._$OrderService.setInstallFee(input.value === 'cable_or_fiber' ? this.DSLInstallFee : this.DSLInstallFee);
				break;
		}
	}

	appartementSelectionToggle(input) {
		const parentIsAppartement = document.querySelector('#appNumber').parentElement;
		if (input.value === 'true') {
			document.forms['personalInfo']['appNumber'].required = true;
			parentIsAppartement.classList.remove('hidden')
		}
		else {
			document.forms['personalInfo']['appNumber'].required = false;
			parentIsAppartement.classList.add('hidden');
			this.resetInput('#appNumber');
		}
	}

	resetInput(selector) {
		let input = document.querySelector(selector)
		if (input) {
			input.value = '';
			this._$OrderService._infoFormDataBuilder(input);
		}
	}

	haveInternetToggle(input) {
		const oldProviderInfo = document.querySelector('#oldProviderInfo');
		if (input.value === 'true') {
			document.forms['personalInfo']['internetProvider'].required = true;
			oldProviderInfo.classList.remove('hidden');
			this._$OrderService.setInstallFee(this.DSLInstallFee);
		} else {
			let provider = document.forms['personalInfo']['internetProvider']
			provider.required = false;
			provider.value = 'defaultValue';
			this._$OrderService._infoFormDataBuilder(provider);
			this.changeInternetProvider(provider);

			let safetyPin = document.forms['personalInfo']['oldTech'][0];
			safetyPin.required = false;
			safetyPin.checked = true;
			this.validateRadio(safetyPin);

			this._$OrderService.setInstallFee(this.DSLInstallFee);
			oldProviderInfo.classList.add('hidden');
		}
	}

	changeInternetProvider(input) {
		const goupille = document.querySelector('#goupille');
		const techno = document.querySelector('#technologie');

		if (input.value === 'videotron' && this.Qualification.details.internetType === 'v-cable') {
			document.forms['personalInfo']['safetyPin'][0].required = true;
			goupille.classList.remove('hidden');
		} else {
			document.forms['personalInfo']['safetyPin'][0].required = false;
			document.forms['personalInfo']['safetyPin'].value = false;
			goupille.classList.add('hidden');
			this._$OrderService._infoFormDataBuilder(document.forms['personalInfo']['safetyPin'][0]);
		}

		if (input.value === 'bell') {
			jQuery('input:radio[name=oldTech][value=dsl_fttn]').trigger('click');
		}
		else if (input.value === 'videotron' || input.value === 'cablevision' || input.value === 'cogeco' || input.value === 'rogers') {
			jQuery('input:radio[name=oldTech][value=cable_or_fiber]').trigger('click');
		}
		else{
			jQuery("input:radio[name=oldTech][value=cable_or_fiber]").prop('checked', false);
			jQuery("input:radio[name=oldTech][value=dsl_fttn]").prop('checked', false);

			this.sendToValidator(this.form.elements['oldTech']);
		}

		if (input.value === 'defaultValue') {
			document.forms['personalInfo']['oldTech'][0].required = false;
			techno.classList.add('hidden');
		} else {
			document.forms['personalInfo']['oldTech'][0].required = true;
			techno.classList.remove('hidden');
		}
	}

	deliveryToggle(input) {
		const deliveryFormContainer = document.querySelector('#deliveryFormContainer')
		if (input.value === 'shipping') {
			document.forms['personalInfo']['sameDeliveryAddress'][0].required = true;
			deliveryFormContainer.classList.remove('hidden');
			this._$OrderService.setShippingFee(this.shippingFee);
		}
		else {
			document.forms['personalInfo']['sameDeliveryAddress'][0].required = false;
			deliveryFormContainer.classList.add('hidden');
			this._$OrderService.setShippingFee(0);
		}
	}

	deliveryAddressChange(input) {
		let isSameAddress = input.value === 'true'
		jQuery('#deliveryForm').toggleClass('hidden', isSameAddress);
		jQuery('#addDelivery, #radio16').prop('required', !isSameAddress);
	}

	deliveryAppToggle(input) {
		const deliveryApp = document.querySelector('#deliveryApp');
		if (input.value === 'true') {
			document.forms['personalInfo']['appNumber-delivery'].required = true;
			deliveryApp.classList.remove('hidden');
		}
		else {
			document.forms['personalInfo']['appNumber-delivery'].required = false;
			deliveryApp.classList.add('hidden');
			this.resetInput('#appNumber-delivery');
		}
	}

	manageCreditConsent() {
		let hasConsent = jQuery('input[name="creditConsent"]:checked').length === 1;
		let oldAddressType = jQuery('input[name="oldLivingAddress"]:checked').val();
		let wasOutsideCanada = oldAddressType === 'outsideCanada';
		this.isLastAddressCanadian = oldAddressType === 'inCanada';
		jQuery('#resultDescription div').addClass('hidden');

		jQuery('[name="oldLivingAddress"]').prop('required', hasConsent && this._$OrderService.mustRentEquipementNumerique());

		jQuery('.order-summary__navbutton').toggleClass('disabled', !hasConsent || (this._$OrderService.mustRentEquipementNumerique() && !this.isCreditRequestApproved));
		jQuery('.eboxtv-navbutton').toggleClass('disabled', !hasConsent || (this._$OrderService.mustRentEquipementNumerique() && !this.isCreditRequestApproved));

		
		jQuery('#buyEquipement').toggleClass('hidden', hasConsent || jQuery('input[name="creditConsent"]:checked').val() === undefined);
		jQuery('#equifaxForm').toggleClass('hidden', !hasConsent);
		jQuery('#doesNotPreQualify').toggleClass('hidden', !(hasConsent && wasOutsideCanada));
		jQuery('#submitBtn').toggleClass('hidden', !hasConsent || wasOutsideCanada || this.isCreditRequestDone);

		// Old living address
		this._$OrderService._set_addresses({});
		jQuery('#lastAddress').prop('required', this._$OrderService.mustRentEquipementNumerique() && hasConsent && (oldAddressType === 'ONQC' || oldAddressType === 'inCanada')).val('');
		jQuery('#lastAddressForm').toggleClass('hidden', oldAddressType === 'same' || wasOutsideCanada);
	}

}