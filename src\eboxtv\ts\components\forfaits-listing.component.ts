import { Component } from '@core/component';
import { TVOrderService, PriceCalcService } from '@eboxtv/js/services';
import { CoreTools } from '@core/helpers';
import { ChannelsService } from '@eboxtv/js/services/channels.service';
import {  OrderOnlineService} from "@eboxtv/js/services/orderOnline.service";
import './forfaits-listing.component.scss';
import { elementAt } from 'rxjs/operator/elementAt';
import { of } from 'rxjs/observable/of';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { LocalStorage } from '@common/ts/services/LocalStorage';


export class ForfaitsListingComponent extends Component {
	public static selector: string = 'forfaits-listing';
	public static template: string = require('./forfaits-listing.component.hbs');

	private $tvOrder: TVOrderService = TVOrderService.getInstance();
	private $priceCalc: PriceCalcService = PriceCalcService.getInstance();
	private $channels: ChannelsService = ChannelsService.getInstance();
	private $orderOnlineService = OrderOnlineService.getInstance();
	_$OrderService = OrderOnlineService.getInstance()


	public MonthlyPrices : any[] = [];
	public caaQuebecTotal : number = 0;
	public promoCaaPercentage = {
		internet: '0.10',
		phone: '0.10',
		tv: '0.10',
		blocEnregistrement: '0.10'
	}

	public attrs: {
		color?: string;
		type? : string;
		includeReceiver: boolean;
	};

	public monthlyBill : number = 0;
	public oneTimeBill : number = 0;

	public sousTotalNoTVMonth : number = 0
	public sousTotalNoTVUnique : number = 0

	public state: ForfaitsListingState = {
		type : this.attrs.type,
		items: [],
		color: this.attrs.color,
	};

	constructor(host: HTMLElement) {
		super(host, ['color', 'type']);

		if(this.state.type === 'chargeMensuel'){
			this.$orderOnlineService.initCart();
			this.subscribeToChargesChange();

		}
		else if(this.state.type === 'chargeUnique'){
			this.subscribeToChargesChangeUnique();

		}
		else{
			this.onInit();
		}

	}

	private onInit(): void {

		this.subscribeToChanges();
	}

	private subscribeToChanges(): void {
		this.$priceCalc.totalsChange$.subscribe(
			this.onPriceChange.bind(this)
		);
	}
	private subscribeToChargesChange(): void{
		this.$orderOnlineService.charges.subscribe(
				this.monthlyPricesChanges.bind(this)
		);
	}
	private subscribeToChargesChangeUnique(): void{
		this.$orderOnlineService.charges.subscribe(
				this.oneTimeChanges.bind(this)
		);
	}


	monthlyPricesChanges(charges : Charges){
		const step = LocalStorage.get('stepValidation', false)
		let promoMensuel = 0;

		const qualification: any = CookiesService.getCookies('eboxQualification', true);
		const lang = CoreTools.lang
		let lienLang = lang == 'fr' ? '' : 'en/';
		let residential = lang == 'fr' ? '/residentiel' : '/residential';
		let lien = '/' + lienLang + qualification.details.fullProvince + residential;

		var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";
		var langue = CookiesService.getCookies('wp-wpml_current_language');
		var lienTV = "";
		var lienTelephony = "";
		var lienInternet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

		if(langue == 'fr'){
			lienTV = "/" + province_complet + "/residentiel/television/";
			lienTelephony = "/" + province_complet + "/residentiel/telephonie/";
			lienInternet = "/" + province_complet + "/residentiel/forfaits-internet/";
		}else{
			lienTV = "/en/" + province_complet + "/residential/television/";
			lienTelephony = "/en/" + province_complet + "/residential/telephony/";
			lienInternet = "/en/" + province_complet + "/residential/internet-packages/";
		}

		this.monthlyBill = 0;
		const items: ChargesItem[] = [];
/**
* AJOUTS DES ELEMENTS INTERNET DANS LA FACTURE MENSUEL
*/
		if(charges.internetPlan.planPrice){
			let internetPrice = parseFloat(charges.internetPlan.planPrice);
			if(charges.internetEquipement.length > 0){
				charges.internetEquipement.forEach(equitement => {
					if(equitement.buyRent == 'rent'){
						internetPrice += parseFloat(equitement.rentPrice);
					}
				});
			}


			if(charges.internetPlan.technology == 'DSL' || charges.internetPlan.cableTech == 'DSL'){
				if(qualification.details.adslinfo){
					qualification.details.adslinfo.forEach((plan : any) => {
						if(plan.dsl_plan == charges.internetPlan.forfait){
							if(charges.internetPlan.downloadSpeed == '6'){
								if(plan.dryloop_price){
									internetPrice += parseFloat(plan.dryloop_price);
								}
								if(plan.dryloop_rural_price){
									internetPrice += parseFloat(plan.dryloop_rural_price);
								}
								if(plan.credit_dryloop_price){
									promoMensuel += parseFloat(plan.credit_dryloop_price);
									this.calcSousTotalMonthly(plan.credit_dryloop_price, 'substract');

								}
							}
						}
					});
				}
			}


			if(charges.promoInternet.length > 0){
				charges.promoInternet.forEach(promo => {
					if(promo.recurrent === 'oui'){
						internetPrice -= parseFloat(promo.prix);

						this.calcSousTotalMonthly(promo.prix, 'substract');
					}
				});
			}

			if(charges.promoInternetEquipement.length > 0){
				charges.promoInternetEquipement.forEach(promo => {
					if(promo.recurrent === 'oui'){
						internetPrice -= parseFloat(promo.prix);
						this.calcSousTotalMonthly(promo.prix, 'substract');

					}
				});
			}

			items.push({	
				type: '',
				title: CoreTools.translate('Internet', 'Internet'),
				count: CoreTools.translate('modifier', 'change'),
				cost: internetPrice,
				icon: '',
				link: lienInternet,
			});

			this.caaQuebecTotal = (internetPrice * parseFloat(this.promoCaaPercentage.internet));

			if(charges.promoCode && charges.promoCode.amount){
				/*if(charges.promoCode.codePromo.toUpperCase() == 'FIB1000'){
					items.push({	
						type: '',
						title: CoreTools.translate('Promotion Vendredi fou','Black Friday promotion'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'FIB500'){
					items.push({	
						type: '',
						title: CoreTools.translate('Promotion Vendredi fou','Black Friday promotion'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}*/

				// BENNYQC devient CHEPROMOQC
				if(charges.promoCode.codePromo.toUpperCase() == 'CHEPROMOQC'){
					items.push({	
						type: '',
						title: CoreTools.translate('Rabais partenaire Benny','Benny partner discount'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				// BENNYON devient CHEPROMOON
				if(charges.promoCode.codePromo.toUpperCase() == 'CHEPROMOON'){
					items.push({	
						type: '',
						title: CoreTools.translate('Rabais partenaire Benny','Benny partner discount'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'EBOXBF24'){
					items.push({	
						type: '',
						title: CoreTools.translate('Évènement Vendredi fou - 2e mois gratuit','Black Friday Event - 2nd month free'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'COLOC25'){
					items.push({	
						type: '',
						title: CoreTools.translate('1 mois d\'Internet gratuit','1 month of free Internet'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}
				// CODE PROMO CAMPUS25
				if(charges.promoCode.codePromo.toUpperCase() == 'CAMPUS25'){
					items.push({	
						type: '',
						title: CoreTools.translate('1 mois d\'Internet gratuit','1 month of free Internet'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}
				// CODE PROMO COMMICON
				if(charges.promoCode.codePromo.toUpperCase() == 'CCMTL25'){
					items.push({	
						type: '',
						title: CoreTools.translate('Comiccon 1 mois d\'Internet gratuit','Comiccon 1 month of free Internet'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				// Code promo SNH25 - Ajout du texte de la promo dans le sidebar 
				if(charges.promoCode.codePromo.toUpperCase() == 'SNH25'){
					items.push({	
						type: '',
						title: CoreTools.translate('Salon national de l\'habitation 2025','The national Home Show 2025'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'EBOXBW2024'){
					items.push({	
						type: '',
						title: CoreTools.translate('Promotion - 2e mois gratuit','Promotion - 2nd month free'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'PERKO5'){
					items.push({	
						type: '',
						title: CoreTools.translate('Offre Perkopolis','Perkopolis Offer'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}

				if(charges.promoCode.codePromo.toUpperCase() == 'VENNGO5'){
					items.push({	
						type: '',
						title: CoreTools.translate('Offre VennGo','VennGo Offer'),
						count: CoreTools.translate('modifier', 'change'),
						cost: 0 - parseFloat(charges.promoCode.amount),
						icon: ''
					});
				}
				this.calcSousTotalMonthly(charges.promoCode.amount, 'substract');
			}

			if(charges.promoFibre && charges.promoFibre.amount){
				items.push({	
					type: '',
					title: CoreTools.translate('Promo 1 mois gratuit - Superbowl','1 month free promo - Superbowl'),
					count: CoreTools.translate('modifier', 'change'),
					cost: 0 - parseFloat(charges.promoFibre.amount),
					icon: ''
				});
			}

			this.calcSousTotalMonthly(internetPrice, 'add');

		}

/**
 * AJOUTS DES ELEMENTS TELEPHONIE DANS LA FACTURE MENSUEL
 */
		if(charges.phonePlan.planPrice){
			let phonePrice = parseFloat(charges.phonePlan.planPrice)
			if(charges.phoneEquipement.length > 0){
				charges.phoneEquipement.forEach(equitement => {
					if(equitement.buyRent == 'rent'){
						phonePrice += parseFloat(equitement.price);
					}
				});
				}
			if(phonePrice > 0){
				
				
				phonePrice -= 5.00;
				//this.calcSousTotalMonthly(5.00, 'substract');
				items.push({
					type: '',
					title: CoreTools.translate('Téléphonie', 'Home Phone'),
					count: CoreTools.translate('modifier', 'change'),
					cost: phonePrice,
					icon: '',
					link : lienTelephony
				});
				this.calcSousTotalMonthly(phonePrice, 'add');
				this.caaQuebecTotal += (phonePrice * parseFloat(this.promoCaaPercentage.phone));
			}



		}

		let stepsArray: Array<any> = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvOptions.steps.details : [];
		const hasTV = CookiesService.getCookies('hasTV');
		let { total } = this.$priceCalc.totals;
		const totals  = this.$priceCalc.totals;
		const test = this.$priceCalc.totals;
		const optionsTVBundle = this._$OrderService._retForfaitOption();
		let prix = total;
		if(hasTV == 'true'){
			if(prix > 0){
				
				if(optionsTVBundle.a_la_carte){
					stepsArray.forEach(step => {
						if(step.size == parseInt(optionsTVBundle.nbChaines)){
							const chaines = LocalStorage.get('selectedChannels')
							const channelLength = chaines['a-la-carte'].length;
							if(channelLength <  parseInt(optionsTVBundle.nbChaines)){
								prix -= totals.channels['a-la-carte']  + totals.channels['others'] // si on veut seulement ajoiuter les bundle price, on ajoute : + totals.channels['others']
							}
							else{
								prix -= step.cost
							}

						}
					});
					
				}

				this.caaQuebecTotal += (prix * parseFloat(this.promoCaaPercentage.tv));


				if(parseInt(charges.decodeur.qty) > 1  && charges.decodeur.buyRent == 'rent'){
					if(charges.promoDecodeur.length > 0){
						charges.promoDecodeur.forEach(promo => {
							prix -= parseFloat(promo.prix)
						});
					}
				}

				var qtyDecodeur: number = +charges.decodeur.qty;
				if(parseInt(charges.decodeur.qty) > 0  && charges.decodeur.buyRent == 'rent'){
					prix += (qtyDecodeur - 1) * parseFloat(charges.decodeur.price);
				}
				else{
					prix += 0.00;
				}

				if(charges.eboxTVOptions.cloudSpace){
					prix += parseFloat(charges.eboxTVOptions.cloudSpace.price);
					this.caaQuebecTotal += (parseFloat(charges.eboxTVOptions.cloudSpace.price) * parseFloat(this.promoCaaPercentage.blocEnregistrement));
				}
				items.push({
					type: '',
					title: CoreTools.translate('Télévision', 'Television'),
					count: CoreTools.translate('modifier', 'change'),
					cost: prix,
					icon: '',
					link : lienTV
				});
				this.calcSousTotalMonthly(prix, 'add');

				// Promo Crave
				if(charges.promoCrave && charges.promoCrave.amount){
					items.push({	
						type: '',
						title: CoreTools.translate('Promotion Crave','Crave promotion'),
						count: CoreTools.translate('modifier', 'change'),
						cost: parseFloat(charges.promoCrave.amount),
						icon: '',
						promo : true
					});
					this.calcSousTotalMonthly(charges.promoCrave.amount, 'substract');
				}
			}
		}


		if(charges.promoDuo.length > 0){
			charges.promoDuo.forEach(promo => {
				if(promo.recurrent == 'oui'){
					promoMensuel += parseFloat(promo.prix)
					this.calcSousTotalMonthly(promo.prix, 'substract');
				}
			});
		}

		if(charges.promoInfo.length > 0){
			charges.promoInfo.forEach(promo => {
				if(promo.recurrent == 'oui'){
					promoMensuel += parseFloat(promo.prix);
					this.calcSousTotalMonthly(promo.prix, 'substract');
				}
			});
		}
		

		if(promoMensuel > 0){
			items.push({
				type: '',
				title: CoreTools.translate('Rabais mensuel', 'Monthly rebate'),
				count: '',
				cost: promoMensuel,
				icon: '',
				promo : true,
			});
		}


		// CAA QUEBEC
		var partner_discount = LocalStorage.get('partner_discount') || '';
		var partner_membership_number = LocalStorage.get('partner_membership_number') || '';
		if(partner_discount && partner_membership_number){
			items.push({	
				type: '',
				title: CoreTools.translate('Promotion CAA','CAA promotion'),
				count: '',
				cost: 0 - this.caaQuebecTotal,
				icon: ''
			});
			this.calcSousTotalMonthly(this.caaQuebecTotal, 'substract');
		}



/**
 * AJOUTS DU SOUS-TOTAL DANS LA FACTURE MENSUEL
 */
		items.push({
			type: '',
			title: CoreTools.translate('Sous-total', 'Subtotal'),
			count: CoreTools.translate('modifier', 'change'),
			cost: this.monthlyBill,
			icon: '',
			subtotal : true,
			sectionMonthly : CoreTools.translate('/mois', '/month')
		});

		total = parseFloat(total.toFixed(2));

		this.$orderOnlineService.cartMonthlyPrice.next(this.monthlyBill - total);

		this.setState({ items });
	}

/**
 *
 * @param charges
 * AFFICHE LES CHARGES UNIQUE
 */
	oneTimeChanges(charges : Charges){
		this.oneTimeBill = 0;
		let oneOffDiscount = 0;
		const items: ChargesItem[] = [];
		/**
		 * AJOUTE LES EQUIPEMENTS INTERNET
		 */
		if(charges.internetEquipement.length > 0){
			charges.internetEquipement.forEach(equitement => {
				let prix : any = equitement.buyPrice;
				if(equitement.rebate){
					prix = parseFloat(equitement.buyPrice) - parseFloat(equitement.rebate)
				}
				if(equitement.buyRent == 'buy'){
					let terme = ''
					let termeEn = ''
					if(equitement.type == 'modem'){
						terme = 'Modem',
						termeEn = 'Modem'
					}
					else{
						terme = 'Routeur',
						termeEn = 'Router'
					}
					items.push({
						type: '',
						title: CoreTools.translate(terme, termeEn),
						count: CoreTools.translate('modifier', 'change'),
						cost: prix,
						icon: ''
					});
					this.calcSousTotalOneTime(prix, 'add');
				}
			});

			if(charges.promoInternet.length > 0){
				charges.promoInternet.forEach(promo => {
					if(promo.recurrent === 'non'){
						oneOffDiscount += parseFloat(promo.prix);
						this.calcSousTotalOneTime(promo.prix, 'substract');
					}
				});
			}

			if(charges.promoInternetEquipement.length > 0){
				charges.promoInternetEquipement.forEach(promo => {
					if(promo.recurrent === 'non'){
						oneOffDiscount += parseFloat(promo.prix)
						this.calcSousTotalOneTime(promo.prix, 'substract');
					}
				});
			}

			if(charges.promoDuo.length > 0){
				charges.promoDuo.forEach(promo => {
					if(promo.recurrent == 'non'){
						oneOffDiscount += parseFloat(promo.prix)
						this.calcSousTotalOneTime(promo.prix, 'substract');
					}
				});
			}
		}

		// charges
		if(charges.internetPlan.downloadSpeed == '6'){
			let prix = 20;
			items.push({
				type: '',
				title: CoreTools.translate("Frais d'activation de la ligne", "Line activation fee"),
				count: CoreTools.translate('modifier', 'change'),
				cost: prix,
				icon: ''
			});
			this.calcSousTotalOneTime(prix, 'add');

		}


		/**
		 * AJOUTE LES EQUIPEMENTS TELEPHONIQUE
		 */
		if(charges.phoneEquipement.length > 0){
		charges.phoneEquipement.forEach(equitement => {
			if(equitement.buyRent == 'buy'){
				let prix : any = equitement.price
				if(equitement.discount){
					prix = parseFloat(equitement.price);
				}
				items.push({
					type: '',
					title: CoreTools.translate('Adaptateur VOIP', 'VOIP Adapter'),
					count: CoreTools.translate('modifier', 'change'),
					cost: parseFloat(prix),
					icon: ''
				});
				this.calcSousTotalOneTime(prix, 'add');
			}
		});
		}

		const hasTV = CookiesService.getCookies('hasTV')
		if(hasTV == 'true'){
			if(parseInt(charges.decodeur.qty) > 0  && charges.decodeur.buyRent == 'buy'){
				let prix = parseFloat(charges.decodeur.qty) * parseFloat(charges.decodeur.price);
				items.push({
					type: '',
					title: CoreTools.translate('Décodeur', 'Receiver'),
					count: parseInt(charges.decodeur.qty),
					cost: prix,
					icon: ''
				});
				this.calcSousTotalOneTime(prix, 'add');
			}
		}

		

		if(charges.order.installFee && charges.order.installFee > 0){
			items.push({
				type: '',
				title: CoreTools.translate("Frais d'installation", 'Installation costs'),
				count: 1,
				cost: charges.order.installFee,
				icon: ''
			});
			this.calcSousTotalOneTime(charges.order.installFee, 'add');

		}

		if(charges.order.shippingFee && charges.order.shippingFee > 0){
			items.push({
				type: '',
				title: CoreTools.translate("Frais de livraison", 'Shipping cost'),
				count: 1,
				cost: charges.order.shippingFee,
				icon: ''
			});
			this.calcSousTotalOneTime(charges.order.shippingFee, 'add');

		}

		if(charges.promoInfo.length > 0){
			charges.promoInfo.forEach(promo => {
				if(promo.recurrent == 'non'){
					oneOffDiscount += parseFloat(promo.prix)
					this.calcSousTotalOneTime(promo.prix, 'substract');
				}
			});
		}
		if(oneOffDiscount > 0){
			items.push({
				type: '',
				title: CoreTools.translate('Rabais uniques', 'One-off rebate'),
				count: '',
				cost: oneOffDiscount,
				icon: '',
				promo : true
			});
		}

		/**
		 * AJOUTE LE SOUSS-TOTAL UNIQUE
		 */
		items.push({
			type: '',
			title: CoreTools.translate('Sous-total', 'Subtotal'),
			count: CoreTools.translate('modifier', 'change'),
			cost: this.oneTimeBill,
			icon: '',
			subtotal : true
		});
		this.$orderOnlineService.cartUniquePrice.next(this.oneTimeBill);

		this.setState({ items });

	}


	calcSousTotalMonthly(add : any, plusOrMinus : 'add' | 'substract'){
		if(typeof(add) === 'string'){
			add = parseFloat(add);
		}
		if(plusOrMinus == 'add'){
			this.monthlyBill += add;
		}
		else{
			this.monthlyBill -= add;
		}
	}

	calcSousTotalOneTime(add : any, plusOrMinus : 'add' | 'substract'){
		if(typeof(add) === 'string'){
			add = parseFloat(add);
		}

		if(plusOrMinus == 'add'){
			this.oneTimeBill += add;
		}
		else{
			this.oneTimeBill -= add;
		}
	}

	private onPriceChange(totals: TotalsObject): void {
		this.update(totals);
	}

	private update(totals: TotalsObject = this.$priceCalc.totals): void {
		const items: ForfaitItem[] = this.buildItemsArray(totals);
		this.setState({ items });
	}

	private buildItemsArray(totals: TotalsObject) {
		const selectedBundle = this.$tvOrder.getSelectedBundle();
		const items: ForfaitItem[] = [];

		if (!selectedBundle || (selectedBundle && !selectedBundle.isFeatured)) {
			items.push({
				type: 'base',
				title: CoreTools.translate('La base', 'Basic channels'),
				count: CoreTools.translate('obligatoire', 'mandatory'),
				cost: totals.channels.base,
				icon: 'icon-ic_laptop'
			});

		}

		if (selectedBundle) {
			items.push({
				type: 'pre-selected',
				title: CoreTools.translate(selectedBundle.name, selectedBundle.nameEN),
				count: selectedBundle.isFeatured ? selectedBundle.channels.length + this.$channels.getLength('base') : selectedBundle.channels.length,
				cost: selectedBundle.cost,
				icon: 'icon-ic_laptop'
			});
		}
		return items;
	}
}

interface ForfaitsListingState {
	items: ForfaitItem[];
	color: string;
	type : string;
}

interface ForfaitItem {
	type: 'base'|'pre-selected'|'receiver'|'featured';
	title: string;
	count: number|'inclus'|'included';
	cost: number;
	icon: string;
}
interface InternetPlanType {
	planPrice: string;
	technology : string,
	cableTech : string,
	forfait : string,
	downloadSpeed : string
}

interface InternetEquipement{
	type : 'modem' | 'router',
	buyRent : 'buy' | 'rent' | 'own',
	buyPrice : string,
	rentPrice : string,
	sit : string,
	rebate : string
}
interface Charges {
	internetPlan : InternetPlanType,
	internetEquipement : InternetEquipement[],
	phonePlan : PhonePlan,
	phoneEquipement : PhoneEquipement[],
	decodeur : Decodeur,
	eboxTVOptions : TVOptions
	order : Order,
	promoDuo : Promo[],
	promoInfo : Promo[],
	promoInternet : Promo[],
	promoInternetEquipement : Promo[],
	promoDecodeur : Promo[],
	promoCode : any,
	promoCrave : any,
	promoCaa : any,
	promoFibre : any
}

interface ChargesItem {
	type: string;
	title: string;
	count: any;
	cost: number;
	icon: string;
	link? : string | false;
	subtotal? : boolean,
	sectionMonthly? : string,
	promo? : boolean
}
interface PhonePlan{
	planPrice : string
}

interface PhoneEquipement{
	buyRent : 'buy' | 'rent' | 'own',
	price : string,
	discount : string,
}

interface Decodeur {
	buyRent : 'buy' | 'rent' | 'own',
	price : string,
	qty : string
}


interface Order {
	installFee : number,
	shippingFee : number
}


interface Promo {
	prix : string,
	recurrent : 'oui' | 'non',
	titre_panier : 'string,'
}

interface TVOptions{
	cloudSpace : Decodeur
}