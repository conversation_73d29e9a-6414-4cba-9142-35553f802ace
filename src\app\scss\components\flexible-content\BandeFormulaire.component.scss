.BandeFormulaire,
#gform_wrapper_18,
#gform_wrapper_17,
#gform_wrapper_19,
#gform_wrapper_20,
#gform_wrapper_21,
#gform_wrapper_22 {
    //margin: 50px 0;
    padding: 100px 0;

    .gform_confirmation_message {
        font-size: 20px;
    }

    &__right {
        p {
            font: 900 40px/60px $f-primary;
            color: $c-medium-grey;
            text-transform: uppercase;
            @media (max-width: 767px) {
                font: 900 20px/28px $f-primary;
                margin-bottom: 40px;
            }
        }
        @media (max-width: 991px) {
            margin-bottom: 40px;
        }
    }
    &__iconset {
        margin-top: 45px;
        img {
            max-width: 100%;
            height: auto;
        }
        @media (max-width: 991px) {
            display: none;
        }
    }

    &__form-container {
        &--TV {
            li#field_6_27, li#field_6_20, li#field_6_14 {
                visibility: hidden;
                position: absolute;
                width: 1px;
                height: 1px;
            }
        }
    }

    &__tv-details {
        margin-bottom: 40px;
        padding: 15px 15px 0 0;

        .ForfaitsListing__item {
            margin-bottom: 0;
        }

        .order-summary {
            &__count-container {
                border-bottom: 1px solid currentColor;
                margin-bottom: 15px;
            }

            &__count-title {
                font-weight: 400;
            }
        }
    }

    &__ForfaitsListing {
        .ForfaitsListing {
            border-bottom: 1px solid $c-grey;
            margin-bottom: 10px;

            &__item {
                margin-bottom: 7px;
            }

            &__sep {
                background: none;
                border-top: 1px dashed $c-grey;
            }
        }
    }

    &__legal {
        font: 400 12px/16px $f-primary !important;
        text-transform: none !important;
        margin-top: 70px;
    }

    &__cost-container {
        text-align: right;
        font-size: 25px;
        line-height: 30px;
        font-weight: 700;
    }

    &__cost-counter {
        color: $c-primary;
        sup {
            top: auto;
            font-size: 62%;
            vertical-align: super;
            top: 2px;
        }
    }

    &__permonth {
        font-weight: 100;
    }



    .is-doodled--underlined:after {
        height: 9px;
        @media (max-width: 767px) {
            height: 6px;
        }
    }

    .gform_wrapper {
        margin: 0!important;
        ul {
            li.gfield {
                margin: 0 0 10px;
                @media (max-width: 767px) {
                    //margin: 0;
                }
                &:last-of-type {
                    margin: 0;
                }
            }
        }
    }

    .gfield_required {
        //display: none!important;
    }

    input[type="text"] {
        font: 600 16px $f-primary!important;
        color: $c-black;
        height: 35px;
        border-radius: 0;
        appearance: none;
        background: transparent;
    }
    textarea {
        font: 600 16px $f-primary!important;
        color: $c-black;
        padding: 0;
        border : 1px solid $c-medium-grey!important;
        border-radius: 6px;
        appearance: none;
        -webkit-appearance: none;
        background: transparent;
    }
    .gform_footer {
        padding: 0!important;
        display: inline-block;
        //margin: 0!important;
    }
    select {
        padding-left: 0!important;
    }
    input[type="submit"] {
        margin: 0!important;
        border-radius: 6px;
        border: 0;
        min-height: 40px;
        min-width: 150px;
        z-index: 2;
        width: auto;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        color: $c-white;
        font-weight: 700;
        padding: 0 20px;
        text-transform: uppercase;
        font-size: 16px !important;
        font-family: 'BrandonGrotesque', sans-serif;
        transition: all 0.2s $cubic;
        will-change: transform;
        position: relative;
        overflow: hidden;
        border: none!important;
        @media (max-width:767px) {
            margin: 0!important;
        }
        &:before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            background: $c-primary;;
            opacity: 1;
            transition: all 0.2s $cubic;
        }
        &:after {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            transform: translateX(-100%);
            background: $c-grey;
            transition: all 0.2s $cubic;
        }
        &:hover, &:focus {
            background: $c-grey;
            color: $c-white;
            box-shadow: 5px 5px 5px rgba($c-grey, 0.5);
            &:after {
                transform: translateX(0);

            }
            &:before {
                opacity: 0;
            }
        }
    }

    .material-input-container {
        padding-top: 0;
        @media (max-width:767px) {
            margin-bottom: 20px;
        }
    }

    .material-input-container input,
    .material-input-container textarea {
        border-bottom: 1px solid $c-medium-grey;
    }
    .material_input_focused input[type="text"]{
        border-bottom: 4px solid $c-black;
        @media (max-width:767px) {
            border-bottom: 2px solid $c-black;
        }
    }
    .gform_wrapper input:not([type=radio]):not([type=checkbox]):not([type=submit]):not([type=button]):not([type=image]):not([type=file]) {
        padding: 0;

    }

    .material-input-container {
        &.material_input_opened {
            label {
                transform: translateY(-120%) scale(0.80)!important;
                @media (max-width: 767px) {
                    transform: translateY(-80%) scale(0.80)!important;
                }
            }
        }
    }

    .material-input-container--textarea {
        &.material_input_opened {
            label {
                transform: translate(10px, -110%) scale(0.80)!important;
                background: url('../../../img/white-pattern.png') left top repeat;
                padding: 0 5px;
            }
        }
    }
    .ginput_container_textarea {
        margin-top: 35px!important;
    }

    input[type="submit"] {
        @media (max-width: 767px) {
            line-height: initial!important;
            min-width: 146px!important;
            min-height: 40px!important;
            width: auto!important;

        }
        @extend .Button


    }

    .gform_wrapper {
        .validation_error {
            font-weight: 400!important;
            display: none;
        }
    }

    .gform_wrapper .top_label li.gfield.gf_left_half {
        padding-right: 15px!important;
    }


    .gform_wrapper .top_label li.gfield.gf_right_half {
        padding-left: 15px!important;
        @media (max-width:640px) {
            padding-left: 0!important;
        }
    }

    .gform_wrapper li.gfield.gfield_error, .gform_wrapper li.gfield.gfield_error.gfield_contains_required.gfield_creditcard_warning {
        background: none;
        border-top: 0;
        border-bottom: 0;
        margin-bottom: 0!important;
        padding-bottom: 0;
        padding-top: 0;
    }
    .gform_wrapper li.gfield_error input:not([type=radio]):not([type=checkbox]):not([type=submit]):not([type=button]):not([type=image]):not([type=file]), .gform_wrapper li.gfield_error textarea {
        border-bottom: 2px solid $c-primary;
        border-top: 0;
        border-left: 0;
        border-right: 0;
    }

    .gfield_error {
        .validation_message {
            padding-top: 2px!important;
            font: 400 14px/19px $f-primary;
            color: #D0021B;
        }
    }
    label {
        font: 500 16px $f-primary!important;
        color: $c-medium-grey;
    }
    .gfield_label {
        font: 500 16px $f-primary!important;
        color: $c-medium-grey;
    }

    .gfield_select {
        appearance: none;
        background: url('../../../img/Icon/ic_dropdown-grey.svg') right center no-repeat;
        background-size: 20px 20px;
        border: 0;
        font: 500 16px $f-primary!important;
        color: $c-grey;
        height: 35px;
        border-radius: 0;
        border-bottom : 1px solid $c-grey!important;
        &:focus {
            outline: none;
        }
    }
    .select {
        margin-top: 40px!important;
        .gfield_label {
            display: none!important;
        }
    }

    .gform_wrapper .ginput_container span:not(.ginput_price) {
        @media (max-width:767px) {
            display: inline-block;
        }
    }

    @media (max-width:991px) {
        padding: 70px 0;
    }
    @media (max-width:767px) {
        padding: 40px 0 60px;
    }
}

#gform_wrapper_21,
#gform_wrapper_22 {
    padding-top: 0;
}



.gform_footer {
    display: inline-block;
    width: auto!important;
    margin-top: 60px!important;
    position: relative;
    z-index: 2;
    @media (max-width: 767px) {
        margin-top: 30px!important;
    }
    &:after {
        background: url('../../../img/Icon/ic_computer-mouse-red.svg') center center no-repeat;
        background-size: 100%;
        width: 59px;
        height: 59px;
        position: absolute;
        right: -51px;
        bottom: -51px;
        z-index: 1;
        content: "";
        @media (max-width: 991px ) {
            width: 32px;
            height: 32px;
            right: -28px;
            bottom: -28px;
        }
        @media (max-width: 835px ) {
            width: 40px;
            height: 40px;
            right: -35px;
            bottom: -35px;
        }
    }
}


.ginput_total {
    color: $c-primary;
}

#gform_16,
#gform_15,
#gform_17,
#gform_18,
#gform_19,
#gform_20 {
    li {
        input[type="text"] {
            height: 45px;
        }
    }
    #field_15_6,
    #field_16_6 {
        padding-top: 10px;
    }

    #field_15_8,
    #field_16_8 {
        margin: 30px 0 40px!important;
        input[type=checkbox] {
            margin: 0 5px 0 0;
        }
    }
}

#gform_wrapper_19,
#gform_wrapper_20 {
    padding: 0!important;
}