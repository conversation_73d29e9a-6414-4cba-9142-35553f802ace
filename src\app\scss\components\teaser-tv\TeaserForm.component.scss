.TeaserForm {
	padding: 142px 15px 102px;
	box-shadow: inset 0 -5px 30px 0 rgba(#000, 0.45);
	color: $c-white !important;

	@media (max-width: 991px) {
		padding: 100px 15px 60px;
	}
	@media (max-width: 600px) {
		padding: 60px 0 40px;
	}

	.validation_error {
		display: none;
	}

	&__col {
		&--title {
			padding: 0 0 0 110px;

			@at-root {
				@include applyAtRoot('html:lang('en-CA')') {
					padding-left: 85px;
				}
			}

			@media (max-width: #{map-get($breakpoints, BS_md) - 1}) {
				padding: 0 15px !important;
			}
		}
	}

	&__service-icons {
		max-width: 100%;

		@media (max-width: #{map-get($breakpoints, BS_md) - 1}) {
			display: none;
		}
	}

	&__title {
		margin: 0 0 41px;
		font: 900 40px/1.45 $f-primary;
		text-transform: uppercase;
		color: currentColor;

		@media (max-width: 1300px) {
			font-size: 32px;
		}
		@media (max-width: 991px) {
			font-size: 34px;
		}
		@media (max-width: 767px) {
			font-size: 26px;
		}
		@media (max-width: 600px) {
			font-size: 18px;
		}
	}

	&__form {
		input[type="text"], input[type="email"] {
			background: none;
			width: 100% !important;
			padding: 0 !important;
		}

		.material-input-container, .materialInputContainer {
			font: 700 20px/22px $f-primary !important;
			color: #FFF;

			input {
				border-radius: 0 !important;
				border-color: currentColor;
			}

			label {
				font: inherit;
			}
		}

		.materialInputContainer {
			position: relative;
			margin: 0 0 35px !important;
			padding: 0 !important;

			&:last-of-type {
				margin-bottom: 0;
			}
		}

		&--sent {
			visibility: hidden;
		}
	}

	&__form-footer {
		position: relative !important;
		display: inline-block !important;
		margin: 40px 0 0;
		padding: 0;
		width: auto !important;

		&:after {
			content: '';
			display: inline-block;
			position: absolute;
			left: 100%;
			top: 50%;
			margin-left: -5px;
			width: 90px;
			height: 96px;
			background: {
				image: url('../../../img/teaser-tv/mouse.svg');
				size: cover;
				position: center;
				repeat: no-repeat;
			};

			@media (max-width: 1024px) {
				display: none;
			}
		}
	}

	div.mce_inline_error {
		background: none !important;
		font-size: 12px;
		text-align: right;
		position: absolute;
		right: 0;
		padding: 0 !important;
	}

	#mc_embed_signup {
		position: relative;
	}

	#mce-success-response {
		position: absolute;
		display: flex;
		align-items: center;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: url('~@common/img/ebox-texture-background--red.jpg');
		padding-bottom: 70px;
		z-index: 1;
		box-sizing: content-box;
		font: 700 20px/1.4 $f-primary;

		@media (max-width: 991px) {
			padding-bottom: 0;
		}
		@media (max-width: 650px) {
			text-align: left;
		}
		@media (max-width: 600px) {
			font-size: 16px;
		}
	}

	.material-input-container.material-input-container.material-input-container.material-input-container {
		font: 700 20px/22px $f-primary !important;

		label {
			font: 700 20px/22px $f-primary !important;
		}

		input {
			background: none;
			width: 100%;
			border-color: currentColor;
			padding-left: 0;
		}
	}

	.gform_fields {
		list-style: none;
		padding: 0;
		margin: 0;
	}
	
	.gfield {
		position: relative;
		margin-bottom: 36px;

		.validation_message {
			position: absolute;
			display: block;
			width: 100%;
			padding-top: 4px;
			top: 100%;
			left: 0;
			text-align: right;
		}

		.ginput_container {
			.validation_message {
				position: relative;
				top: auto;
			}
		}
	}

	.gform_button.gform_button.gform_button {
		@extend .TeaserButton;
		margin-right: 0;
	}
	
	.gform_footer {
		position: relative;

		&:after {
			content: '';
			display: inline-block;
			position: absolute;
			left: 100%;
			top: 50%;
			margin-left: -5px;
			width: 90px;
			height: 96px;
			background: {
				image: url('../../../img/teaser-tv/mouse.svg');
				size: cover;
				position: center;
				repeat: no-repeat;
			};

			@media (max-width: 1024px) {
				display: none;
			}
		}
	}
}