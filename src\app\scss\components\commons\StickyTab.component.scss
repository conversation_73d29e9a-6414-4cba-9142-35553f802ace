.StickyTab {
	position: fixed;
	display: flex;
	align-items: center;
	bottom: 0;
	background: url('../../../img/red-pattern.png');
	color: #FFF;
	border-radius: 16px 16px 0 0;
	padding: 10px 16px 18px;
	cursor: pointer;
	transform: translateY(11px);
	transition: all 0.25s $cubic;
	
	&--hidden {
		transform: translateY(100%);
		transition: all 0.4s $cubicElastic;
	}

	&--hidden-force {
		opacity: 0 !important;
		position: fixed !important;
		pointer-events: none !important;
		transform: translateY(100%) !important;
		transition: all 0.4s $cubicElastic !important;
	}

	&__icon {
		display: inline-block;
		width: 30px;
		height: 30px;
		fill: currentColor;
		margin-right: 10px;
		transform-origin: center;
	}

	&__title {
		font: 700 16px/22px $f-primary;
	}

	&:hover {
		transform: translateY(0);

		.StickyTab {
			&__icon {
				animation: swing 0.75s $cubic;
			}
		}
	}
}

@keyframes swing {
	20% {
		transform: rotate3d(0, 0, 1, 15deg);
	}
	40% {
		transform: rotate3d(0, 0, 1, -10deg);
	}
	60% {
		transform: rotate3d(0, 0, 1, 5deg);
	}
	80% {
		transform: rotate3d(0, 0, 1, -5deg);
	}
	to {
		transform: rotate3d(0, 0, 1, 0deg);
	}
}