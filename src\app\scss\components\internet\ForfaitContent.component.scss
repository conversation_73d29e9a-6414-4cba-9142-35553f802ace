.ForfaitContent {
    padding-bottom: 100px;
    @media (max-width: 991px) {
        padding-bottom: 70px;
    }
    @media (max-width: 767px) {
        padding-bottom: 40px;
    }

    &__title {
        font: 700 35px/40px $f-primary;
        margin-bottom: 80px;

        @media (max-width: 767px) {
            margin-bottom: 20px;
            font: 700 26px/32px $f-primary;
        }
        @media (max-width: 450px) {
            margin-bottom: 20px;
            font: 700 24px/32px $f-primary;
        }
    }

    ul {
        li {
            a {
                color: $c-primary;
                transition: $t-primary;
                text-decoration: underline;
                &:hover {
                    color: $c-grey;
                }
            }
        }
    }

    &__legal {
        margin-top: 20px;
        p {
            line-height: 25px;
            color: $c-medium-grey;
        }
    }


    &__forfaitTitle {
        text-transform: uppercase;
        font: 900 45px/50px $f-primary;
        margin-bottom: 10px;
    }
    &--telephonie {
        .ForfaitContent__title {
            margin-bottom: 40px;
        }
    }
    .Wysiwyg {
        p {
            margin-bottom: 27px;
        }
    }
    .TwoCol__left {
        @media (max-width: 991px) {
            padding-bottom: 10px!important;
        }
    }

    &__internet-accessory-list {
        position: relative;
        display: flex;
        margin: 0;
        padding: 20px 6% 20px 0;
        list-style: none;
        justify-content: space-between;

        @media (max-width: 1199px) {
            padding-right: 0;
        }
        @media (min-width: 992px) and (max-width: 1199px) {
            display: block;
        }
        @media (max-width: 670px) {
            display: block;
        }
    }

    &__internet-accessory-item {
        position: relative;
        flex: 1 1 50%;
        text-align: right;
        padding: 0;

        @media (max-width: 750px) {
            flex: 1 1 auto;
        }

        &:first-child {
            text-align: left;
        }

        &:after {
            content: '';
            display: inline-block;
            position: absolute;
            right: 0;
            top: 0;
            width: 1px;
            height: 44px;
            background: currentColor;
            margin-right: 7%;
        }

        &:last-child {
            &:after {
                content: none;
            }
        }

        @media (max-width: 1199px) {
            display: block;
            text-align: left;
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0;
            }

            &:after {
                content: none;
            }
        }
    }
    &--gris {
        color: $c-white;
        background: url('../../../img/grey-pattern.png') left top repeat;
        .ForfaitContent__title {
            color: $c-white;
        }
        a {
            color: $c-white;
        }
        .Wysiwyg ul li {
            background: url('../../../img/ic_check-white.svg') left 2px no-repeat;
            background-size: 14px;
            a {
                color: $c-white;
                text-decoration: underline;
            }
        }
    }

    .Listing {
        &__recap--duo-promo {
            display: none !important;
        }
    }

    .SinglePrice {
        flex-direction: row-reverse;
    }

    &.has-tvduo {
        .SinglePrice {
            position: relative;
            background-image: url('../../../img/pattern02.png');

            &:before {
                content: '';
                position: absolute;
                display: block;
                top: 0;
                left: 0;
                width: 82px;
                height: 72px;
                margin: -4px 0 0 -4px;
                background: {
                    image: url('../../../img/promoduo-banner.svg');
                    repeat: no-repeat;
                    size: 100% 100%;
                    position: left top;
                };

                @include applyAtRoot('html:lang(en)') {
                    background-image: url('../../../img/promoduo-banner-en.svg');
                }
            }

            @media (max-width: 499px) {
                height: auto;
                flex-wrap: wrap-reverse;
            }

            .Listing {
                &__recap {
                    display: flex;
                    flex-direction: column;
                    flex: 1 0 auto;
                    padding: 0;

                    &--duo-promo {
                        border-radius: 12px 0 0 12px;
                        display: flex !important;
                        flex: 0 0 57%;
                        background: {
                            image: url('../../../img/half_box.png');
                            size: 100% 100%;
                            repeat: no-repeat;
                        };

                        @media (max-width: 499px) {
                            flex: 0 0 100%;
                            background-image: url('../../../img/half_box--vertical.png');
                            padding: 30px 15px 40px;
                            border-radius: 12px 12px 0 0;
                        }
                    }

                    .ButtonEffect {
                        min-width: 168px;
                        border-width: 2px !important;

                        @media (max-width: 499px) {
                            min-width: 121px;
                        }
                    }

                    &:not(.Listing__recap--duo-promo) {
                        .ButtonEffect {
                            @extend .ButtonEffect--transparent;
                        }

                        .Listing__forfait-title {
                            margin-bottom: 30px;
                        }

                        @media (max-width: 499px) {
                            padding: 11px 15px 30px;
                        }
                    }

                    @media (max-width: 499px) {
                        flex-direction: row;
                        justify-content: space-around;

                        .Listing__recap-wrapper {
                            flex: 0 0 auto;
                        }

                        .Listing__forfait-title.Listing__forfait-title.Listing__forfait-title {
                            flex: 0 0 105px;
                            line-height: 0.8;
                            margin-bottom: 0;
                        }
                    }
                }

                &__prix {
                    &:not(.Listing__prix--promoduo) {
                        margin-top: -10px;
                        font-size: 80px;
    
                        sup {
                            font-size: 24px;
                            top: 0;

                            + span {
                                right: -3px;
                            }
                        }

                        @media (max-width: 499px) {
                            margin-top: 0;
                            font-size: 50px;
                            line-height: 60px;
                            
                            sup {
                                + span {
                                    top: 25px;
                                }
                            }
                        }
                    }

                    sup {
                        top: 0;

                        + span {
                            font-size: 11px;
                            right: 7px;
                            top: 45px;
                        }
                    }

                    @media (max-width: 499px) {
                        font-size: 70px;
                        line-height: 80px;

                        sup {
                            + span {
                                top: 35px;
                            }
                        }
                    }
                }

                &__recap-wrapper {
                    text-align: left;
                }

                &__apartir {
                    @media (max-width: 499px) {
                        font: 400 12px/16px $f-primary;
                    }
                }
            }
        }
    }
}
