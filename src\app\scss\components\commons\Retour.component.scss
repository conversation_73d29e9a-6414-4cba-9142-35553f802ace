.Retour {
    margin-bottom: 60px;

    &__link {
        color: $c-primary;
        font: 700 16px/22px $f-primary;
        display: inline-flex;
        align-items: center;
        transition: $t-primary;
        text-decoration: underline;
        svg {
            margin-right: 5px;
            fill: $c-primary;
            width: 16px;
            height: 16px;
            vertical-align: middle;
            transition: $t-primary;
        }
        &:hover {
            text-decoration: underline;
            svg {
                fill: $c-grey;
            }
        }
        &:focus {
            text-decoration: underline;
        }
    }

    &--no-margin {
        margin: 0 !important;
    }

    &--no-padding {
        padding: 0 !important;
    }

    &--single {
        padding: 48px 0 0 0;
        margin-bottom: 20px;

        a {
            color: $c-black;
            &:hover {
                color: $c-primary;
            }
        }

        @media (max-width: 991px) {
            padding: 70px 0 0 0;
        }

        + * {
            padding-top: 48px !important;
        }
    }
    @media (max-width: 767px) {
        margin-bottom: 20px
    }
}

.RetourListing {
    @extend .Retour;

    &__link {
        @extend .Retour__link;
        &:hover,
        &:focus {
            text-decoration: underline;
        }
        text-decoration: underline;
    }

    &--single {
        @extend .Retour--single;
        &:hover,
        &:focus {
            text-decoration: underline;
        }
    }
}