import { isUserLoggedIn } from '@common/ts/helpers/isUserLoggedIn';
import { Qualification, QualifyService } from '@common/ts/services/Qualify.service';
import { Directive } from '@core/directive';

export class InternetPackageFilterDirective extends Directive {
	public static selector: string = '[data-internet-package-filter]';

	//#region Private properties
	private qualificator: QualifyService = QualifyService.getInstance();

	private qualification: Qualification = this.qualificator.getQualification();

	private packagesElements: NodeListOf<HTMLElement> = this.host.querySelectorAll(
		'[data-download-speed][data-capacity-count="1"][data-highest-price]'
	);

	private sortedPackageElements: SortedPackageElements = {};

	private userMaxDownload: number;

	private isUserLoggedIn: boolean = isUserLoggedIn();

	private debugEl: HTMLElement;
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLElement) {
		super(host);
		this.sortPackageElements();
		
		if (this.isUserLoggedIn) {
			this.appendDebug();
		}

		this.qualificator.qualificationChange$.subscribe({
			next: this.onQualificationChange.bind(this)
		});

		this.onQualificationChange(this.qualificator.getQualification());
	}
	//#endregion

	//#region Private methods
	private onQualificationChange(qualification: Qualification): void {
		this.qualification = qualification;
		this.userMaxDownload = qualification && qualification.isValid ? (qualification.details.maxDownload || Infinity) : Infinity;
		this.updateVisibility();

		if (this.isUserLoggedIn) {
			this.updateDebugContent();
		}
	}

	private sortPackageElements(): void {
		for (let i: number = 0; i < this.packagesElements.length; i++) {
			const packageElement: HTMLElement = this.packagesElements[i];
			const packagePrice: string = packageElement.getAttribute('data-highest-price');
			const packageSpeed: number = +(packageElement.getAttribute('data-download-speed')) || 0;

			if (packagePrice && packageSpeed) {
				if (!(packagePrice in this.sortedPackageElements)) {
					this.sortedPackageElements[packagePrice] = [];
				}

				this.sortedPackageElements[packagePrice].push({
					el: packageElement,
					speed: packageSpeed
				});
			}
		}
	}

	private updateVisibility(): void {
		for (const price in this.sortedPackageElements) {
			const sortedPackageElement: SortedPackageElement[] = this.sortedPackageElements[price];

			sortedPackageElement.forEach(packEl => packEl.el.style.display = '');

			if (sortedPackageElement.length) {
				const hiddenPackages: SortedPackageElement[] = sortedPackageElement.filter(packEl => packEl.speed > this.userMaxDownload);
				const samePriceEls: SortedPackageElement[] = sortedPackageElement.filter(packEl => packEl.speed <= this.userMaxDownload).sort((a, b) => {
					return a.speed > this.userMaxDownload ? -1 : (a.speed > b.speed ? 1 : -1);
				});


				for (let i: number = 0; i < hiddenPackages.length; i++) {
					if(hiddenPackages[i].el.classList.contains('commandeChoixInternet')){
						hiddenPackages[i].el.remove();
					}else{
						this.hideEl(hiddenPackages[i].el);
					}
				}

				for (let i: number = 0; i < samePriceEls.length; i++) {
					if (i < samePriceEls.length - 1) {
						if(samePriceEls[i].el.classList.contains('commandeChoixInternet')){
							samePriceEls[i].el.remove();
						}else{
							this.hideEl(samePriceEls[i].el);
						}
					}
				}
			}
		}
	}

	private hideEl(el: HTMLElement): void {
		if (this.isUserLoggedIn) {
			el.style.opacity = '0.5';
			el.style.filter = 'grayscale(100%)';
		} else {
			el.style.display = 'none';
		}
	}

	private appendDebug(): void {
		this.debugEl = document.createElement('div');

		this.debugEl.style.position = 'fixed';
		this.debugEl.style.bottom = '0';
		this.debugEl.style.left = '0';
		this.debugEl.style.padding = '8px 16px';
		this.debugEl.style.backgroundColor = '#FFF';
		this.debugEl.style.border = '1px solid #000';
		this.debugEl.style.font = '400 16px/24px "BrandonGrotesque"';
		this.debugEl.style.zIndex = '9999';

		document.body.appendChild(this.debugEl);
	}

	private updateDebugContent(): void {
		const { qualification } = this;

		if (qualification && !isNaN(qualification.details.maxDownload)) {
			this.debugEl.innerHTML = `maxDownload: ${ qualification.details.maxDownload } Mbps`;
		} else {
			this.debugEl.innerHTML = `maxDownload: N/A`;
		}
	}
	//#endregion
}

interface SortedPackageElements {
	[price: string]: SortedPackageElement[];
}

interface SortedPackageElement {
	el: HTMLElement;
	speed: number;
}