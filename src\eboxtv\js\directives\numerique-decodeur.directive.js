/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { PromotionsService } from "@eboxtv/js/services/promotions.service";
// import { Tools } from '@common/ts/services/Tools.service';
// import { CookiesService } from '@common/ts/services/Cookies.service';
// import { CoreTools } from '@core/helpers';
// import { LocalStorage } from '@common/ts/services/LocalStorage';
// import { min } from 'rxjs/operator/min';
export class NumeriqueDesoceurDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()
    $promotionsService = PromotionsService.getInstance();
    // $tvOrderService = TVOrderService.getInstance();
    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-numeriqueDecodeur]';
    sku_decodeur = ''
    price_decodeur = ''
    cardDecodeur = ''
    inputQty = document.getElementById('receiverCart');
    checkboxNone = document.getElementsByClassName('check-mg')[0];
    theTarget = document.getElementsByClassName('tv-items-cart--receiver')[0];
    selection = {
        buyRent : '',
        code : '',
        idRef : 1,
        price : 0,
        qty : "0",
        service : 'tv',
        type : 'eboxtv'
    };

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        //this.selection.code = document.getElementsByClassName('tv-items-cart--active')[0].getAttribute('data-sku');
        this._$OrderService.initDecodeur();
        var backDecodeur = this._$OrderService.retDecodeur();
        this.sku_decodeur = sku_decodeur;
        this.price_decodeur = price_decodeur;
        Object.keys(backDecodeur).length ? this.selection = backDecodeur : false;
        this.cardDecodeur = document.querySelectorAll('.tv-items-cart--receiver');

        this.initForm();
        this.cardDecodeur.forEach(el => {
            el.addEventListener('click', function(evt){
                var classlist = Array.from(evt.currentTarget.classList);
                if(!classlist.includes('tv-items-cart--active')){
                    this.toggleSelection(evt.currentTarget)
                }else{
                    this.deleteSelection(evt.currentTarget);
                }
            }.bind(this))
        });

        /*this.checkboxNone.addEventListener('change', function(evt){
            this.toggleSelection(this.theTarget);
        }.bind(this));*/

        if(this.inputQty != undefined && this.inputQty != null){
            this.inputQty.addEventListener('change', function(evt){
                this.changeQty(evt.target.value);

            }.bind(this))
        }


        this.selection.code = this.sku_decodeur;
        this.selection.price = this.price_decodeur;
	}

    initForm(){
        console.log(this.selection);
        this.cardDecodeur.forEach(el => {
            console.log(el.getAttribute('data-buyRent'));
            if(el.getAttribute('data-buyRent') == this.selection.buyRent){
                el.classList.add('tv-items-cart--active');
            }
            if(this.selection.buyRent == 'rent'){
                this.inputQty.value = this.selection.qty;
            }else if(this.selection.buyRent == 'none'){
                //this.checkboxNone.checked = true;
            }
        });
        this.changeQty(this.selection.qty);
    }

    toggleSelection(target){
        const buyRent = target.getAttribute('data-buyRent');
        this.cardDecodeur.forEach(el => {
            el.classList.remove('tv-items-cart--active');
        });
        target.classList.add('tv-items-cart--active');

        this.selection.buyRent = buyRent;
        if(buyRent == 'rent'){
            this.selection.qty = this.inputQty.value;
            this.changeQty(this.inputQty.value);
            this.selection.code = target.getAttribute('data-sku');
        }
        else{
            this.selection.buyRent = 'none';
            this.selection.qty = 0;
            this.displayPrice(0);
            this.selection.qty = 0;     
        }
        this.$promotionsService.promoDecodeur(this.selection.qty, this.selection.buyRent);
        this._$OrderService.saveDecodeur(this.selection);
    }

    changeQty(qty){
        if(this.selection.qty != 0 && this.selection.buyRent != 'none'){
            qty < 1 ? this.inputQty.value = 1 : false ;
            qty > 7 ? this.inputQty.value = 7 : false ;

            this.selection.buyRent = 'rent';
            this.displayPrice(qty);
            if(this.inputQty != undefined && this.inputQty != null){
                this.selection.qty = this.inputQty.value;
            }else{
                this.selection.qty = "0";
            }
            this._$OrderService.saveDecodeur(this.selection);
        }
    }


    displayPrice(qty){
        
        let prix =  qty > 0 ? this.price_decodeur * (qty - 1) : 0  ;
        prix = prix.toString();
        let prixSplit = prix.split(".");
        if(!(prixSplit[0] == undefined || prixSplit[0] == null || prixSplit[0] == 0)){
            document.getElementById('intDecodeur').innerHTML = prixSplit[0];
            if(!prixSplit[1]){
                prixSplit[1] = '00'
            }
            else if(prixSplit[1].length == 1){
                prixSplit[1] = prixSplit[1] + '0';
            }
            document.getElementById('decDecodeur').innerHTML = prixSplit[1];
        }

    }

    deleteSelection(target){
        this.cardDecodeur.forEach(el => {
            el.classList.remove('tv-items-cart--active');
        });

        this.selection = {"buyRent":"none","code":"","idRef":1,"price":0,"qty":"0","service":"tv","type":"eboxtv"};
         
        this._$OrderService.saveDecodeur(this.selection);
    }
}
