.TonForfait {
    padding-top: 0!important;
    .CardListing__title {
        text-transform: uppercase;
        font: 900 45px/50px $f-primary;
        @media (max-width: 991px) {
            margin: 0 0 60px 5px;
            font: 700 35px/40px $f-primary;
        }
        @media (max-width: 767px) {
            font: 900 26px/32px $f-primary;
        }
    }
    /*@media (max-width:991px)  {
        ul  {
            li {
                &:last-child {
                    display: none;
                }
            }
        }
    }*/
    &--telephonie {
        .CardListing__title {
            text-transform: none;
            font: 700 35px/40px $f-primary;
        }
    }
    &--gris {
        background: url('../../../img/grey-pattern.png') left top repeat;
        .CardListing__title {
            color: $c-white;
        }
    }
}