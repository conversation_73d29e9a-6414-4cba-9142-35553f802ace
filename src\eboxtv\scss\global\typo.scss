// GENERALS
h1, h2, h3, h4, h5, h6, p {
	margin: 0;
}

.page-title, h1, h2 {
	text-transform: uppercase;
}


// COLORS
.ebtv-color {
	&--black {
		color: $c-black;
	}

	&--red {
		color: $c-primary;
	}

	&--grey {
		color: $c-light-grey;
	}

	&--white {
		color: $c-white;
	}
}

// BASICS
.page-title {
	font: 900 70px/76px $ebtv-f-primary;
}

h1 {
	font: 900 50px/55px $ebtv-f-primary;
}

h2 {
	font: 900 45px/50px $ebtv-f-primary;
}

h3 {
	font: 700 35px/40px $ebtv-f-primary;
}

h4 {
	font: 700 25px/30px $ebtv-f-primary;
}

h5 {
	font: 700 20px/24px $ebtv-f-primary;
}

h6 {
	font: 700 18px/20px $ebtv-f-primary;
}

p {
	font: 400 16px/22px $ebtv-f-primary;
}

.italic {
	font-weight: 100;
	font-style: italic;
}

.bold {
	font-weight: 700;
}

.small {
	font: 400 12px/16px $ebtv-f-primary;
}

.highlight {
	font: 400 20px/30px $ebtv-f-secondary;
}


/******** MEDIA QUERIES ********/
// TABLETS typo changes
@media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
	.page-title {
		font: 900 42px/46px $ebtv-f-primary;
	}

	h1 {
		font: 900 30px/34px $ebtv-f-primary;
	}

	h2 {
		font: 900 26px/32px $ebtv-f-primary;
	}

	h3 {
		font: 700 26px/32px $ebtv-f-primary;
	}

	h4 {
		font: 700 22px/26px $ebtv-f-primary;
	}
}

// PHONES typo changes
@media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
	.page-title {
		font: 900 36px/40px $ebtv-f-primary;
	}

	h1 {
		font: 900 26px/32px $ebtv-f-primary;
	}

	h2 {
		font: 900 22px/24px $ebtv-f-primary;
	}

	h3 {
		font: 700 24px/26px $ebtv-f-primary;
	}
}