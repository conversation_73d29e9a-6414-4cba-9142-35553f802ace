.NavPrinc {
    margin: 0;
    padding: 0;
    font: 16px/22px $f-primary;
    &__item {
        display: inline-block;
        margin-right: 10px;
        position: relative;
        &:last-child {
            margin-right: 0;
        }
    }
    &__link {
        color: $c-black;
        min-width: 85px;
        display: inline-block;
        text-align: center;
        position: relative;
        padding: 4px;
        &:hover{
            color: $c-black;
            text-decoration: underline;
            &:after {
                width: 97px;

            }
        }
        img {
            width: 32px;
            height: 32px;
            margin: 0 auto;
        }
        &:active,
        &:focus {
            color: $c-black;
            text-decoration: underline;
        }
        /*&:after {
            content: "";
            background: url('../../../img/hover.svg') center left no-repeat;
            background-size: 97px 13px;
            width: 97px;
            height: 13px;
            position: absolute;
            display: block;
            left: 10px;
            top: 50%;
            margin-top: -5px;
            width: 0;

            transition: width .2s ease
        }*/
    }
    &__submenu-list {
        white-space: nowrap;
    }
    &__container {
        display: flex;
        align-items: center;
        height: 60px;
        position: inherit;
        //modif
        justify-content: space-between;

        .ButtonEffect--qualification {
            border: 2px solid $c-primary;
            color: $c-primary;
            &:after {
                background: $c-primary;
            }
            &:hover {
                color: $c-white;
            }
        }
    }
    &__sub-menu {
        position: absolute;
        background: #1F1F1F;
        left: 0;
        top: 0;
        width: 100%;
    }
    &__submenu {
        position: absolute;
        //width: 100vw;
        left: 50%;
        top: 60px;
        background: #1F1F1F;
        padding: 0;
        opacity: 0;
        //overflow: hidden;
        transform: translateX(-50%);
        transform-origin: top;
        transition: $t-primary;
        .arrow-up {
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            position: absolute;
        }

        &--opened {
            padding: 0;
            
            opacity: 1;
            overflow: visible;

        }
        .NavPrinc__item {
            //padding: 18px 0;
            position: relative;
            display: block;
            margin-right: 0;

        }
        .NavPrinc__link {
            color: #FFF;
            text-align: left;
            width: 100%;
            padding: 10px 20px;
            font: 700 16px / 35px "BrandonGrotesque", sans-serif;
            &:hover {
               background: #595959; 
               text-decoration: none;
            }
        }
        &-list {
            margin-left: 0;
        }
    }
}
.Logo {
    &__container {
        display: flex;
        align-items: center;
        height: 60px;
        a {
            display: flex;
        }
    }
}
.NavPrinc__submenu {
    .NavPrinc__link {


    
        &:after {
            background: none;
        }
    }
}
.Basket {
    &__container {
        display: none;
        align-items: center;
        justify-content: flex-end;
        height: 60px;
        a {
            display: flex;
            svg {
                width: 30px;
                height: 30px;
            }
        }
        @media (max-width: 1200px) {
            display: flex;
            position: absolute;
            right: 10px;
            top: 0;
        }

    }
}

.NavPrinc__item--subopen {
    .NavPrinc__link {
        //color: $c-white;
        &:after {
            width: 97px;
        }
    }
    .NavPrinc__submenu {
        opacity: 1;
    }
}

//.NavPrinc__sub-menu { display: none;}

.NavPrinc {
    ul {
        li {
            ul {
                li {
                    .NavPrinc__submenu {
                        display: none;
                        @media (max-width: 1200px) {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }
}