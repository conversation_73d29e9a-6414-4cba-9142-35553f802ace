import { Directive } from '@core/directive';
import { BSModalService } from '@common/js/services/BSModal/BSModal.service';

export class AlertOnLoadDirective extends Directive {
	static selector = '[alert-on-load]';

	_$BSModal = new BSModalService().config({
		template: document.getElementById('BSModalTemplate')
	});

	constructor(host, childAttrs = []) {
		super(host, [
			{name: 'disabled', type: 'eval', default: false},
			{name: 'alert-message', as:'content', required: true},
			{name: 'alert-title', as: 'title', required: true},
			...childAttrs
		]);
		this._init();
	}
	_init() {
		if (this.attrs.disabled === false) {
			this._showModal();
		}
	}

	_showModal() {
		const { title, content } = this.attrs;
		this._$BSModal.show({
			title,
			content
		});
	}
}