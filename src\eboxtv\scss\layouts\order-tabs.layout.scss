@import '../global/font-names.scss';

.order {
    position: relative;
    display: block;
    width: 100%;
    
    &--has-tabs {
        padding-top: 72px;
        @media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
            padding-top: 66px;
        }
        @media (max-width: #{ map-get($ebtvBreakpoints, mobileLandscape) }) {
            padding-top: 52px;
        }
        @media (max-width: #{ map-get($ebtvBreakpoints, bigMobile) }) {
            padding-top: 44px;
        }
    }

    &__main-title {
        margin-bottom: 60px;
    }


    &__wrapper {
        width: 100%;
        display: flex;
    }

    &__channels {
        flex: 1 1 auto;
    }

    &__tabs {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);

        @media (max-width: #{ map-get($ebtvBreakpoints, mobileLandscape) }) {
            left: 15px;
            transform: none;
        }
    }

    &__summary {
        position: relative;
    }

    &__content {
        padding: 100px 160px 145px;

        @media (max-width: 1700px) {
            padding: 100px 60px 145px;
        }
        @media (max-width: map-get($ebtvBreakpoints, smallDesktop)) {
            padding: 60px 60px 145px;
        }
        @media (max-width: map-get($ebtvBreakpoints, laptop)) {
            padding: 74px 30px 145px;
        }
        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            padding: 74px 30px 115px;
        }
        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            padding: 70px 15px 70px;
        }
    }
}



.order-tabs {
    display: table;
    margin: 0 auto;
    font-size: 0;

    &__tab-detail {
        display: block;
        font: 400 12px/14px $ebtv-f-primary;
    }

    &__tab {
        display: inline-block;
        cursor: pointer;
        margin-right: 15px;
        padding: 15px 30px;
        border-radius: 6px 6px 0 0;
        appearance: none;
        border: none;
        background: #595959;
        color: $c-white;
        vertical-align: bottom;
        font: 700 20px/24px $ebtv-f-primary;
        &:last-child {
            margin-right: 0;
        }

        &--left {
            text-align: left;
        }

        &--active {
            padding: 24px 56px;
            background-image: url('~@common/img/ebox-texture-background--black.jpg');
        }

        &--red {
            background-color: $c-primary;
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
            padding: 15px 20px;

            &--active {
                padding: 21px 34px;
            }
        }
        @media (max-width: #{ map-get($ebtvBreakpoints, mobileLandscape) }) {
            padding: 10px 12px;

            &--active {
                padding: 14px 20px;
            }
        }
        @media (max-width: #{ map-get($ebtvBreakpoints, bigMobile) }) {
            padding: 7px 10px;
            font-size: 14px !important;
            margin-right: 5px;
            &--active {
                padding: 10px 15px;
            }
        }
    }

    &__footer {
        display: none;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            display: block;
            padding: 70px 0 0;
        }
    }

    &__navbutton {
        display: inline-block;
    }
}



.order-tab-container {
    min-height: 100vh;
    &__content {
        &--hidden {
            display: none;
        }
    }
}