/* global jQuery */
import { TweenLite } from 'gsap/TweenLite';
import { Power2 } from 'gsap/EasePack';

import { Directive } from '@core/directive';
import { Tools } from '@common/ts/services/Tools.service';
import { overlayProvider } from '../services/overlay.service';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service.ts';

export class ResumeControllerDirective extends Directive {
	static selector = '[resume-controller]';


	// READ-ONLY
	SHOW_DURATION = 0.4;
	HIDE_DURATION = 0.25;


	// PRIVATE PROPERTIES //
	_isOpened	 	 = this.attrs.isOpened;
	_mediaMatchValue = this.attrs.mediaQuery ? window.matchMedia(this.attrs.mediaQuery).matches : false;
	
	/* Elements */
	_$triggerElement = jQuery(this.attrs.triggerSelector);
	_$bodyElement	 = jQuery('.ebtv-resume__container', this.host);
	_$headerElement  = jQuery('.ebtv-resume-head', this.host);
	_$closerElement  = this.attrs.closerSelector ? jQuery(this.attrs.closerSelector) : null;

	/* Services */
	_$viewChangeDetector = ViewChangeDetector.getInstance();
	_$overlay = overlayProvider;
	

	// GETTERS AND SETTERS //
	get mediaMatch() {
		return this._mediaMatchValue;
	}
	set mediaMatch(val) {
		if (val !== this._mediaMatchValue) {
			this._mediaMatchValue = val;
			this._onMediaMatchChange(val);
		}
	}


	// INIT //
	constructor(host) {
		super(host, [
			{name: 'trigger-selector', required: true},
			{name: 'is-opened', type: 'eval', default: false},
			'closer-selector',
			'media-query'
		]);

		this._init();
	}
	_init() {
		this._bindEvents();
		this._initSubscriptions();
		this._updateStatus();
	}


	// PUBLIC METHODS //
	open(noTrans = false) {
		this._$overlay.show({
			requestingElement: this.host,
			transparent: !this.mediaMatch,
			injectInParent: this.mediaMatch ? this.host : document.body
		});
		
		if (this.mediaMatch)
			this._openBody(noTrans);
		else
			this._openFull(noTrans);
		
		Tools.blockScroll();
		this.$host
			.addClass('ebtv-resume--opened')
			.trigger('opening');
	}

	close(noTrans = false, ignoreOverlay = false) {
		if (!ignoreOverlay) {
			this._$overlay.hide({
				requestingElement: this.host
			});
		}

		if (this.mediaMatch)
			this._closeBody(noTrans);
		else
			this._closeFull(noTrans);

		Tools.unBlockScroll();
		this.$host
			.removeClass('ebtv-resume--opened')
			.trigger('closing');		
	}


	// PRIVATE METHODS //
	_bindEvents() {
		this._$triggerElement.on('click', this._onTriggerClick.bind(this));

		if (this.attrs.mediaQuery)
			jQuery(window).on('resize', this._onResize.bind(this));
			
		if (this._$closerElement && this._$closerElement.length)
			this._$closerElement.on('click', this.close.bind(this, false));
	}

	_initSubscriptions() {
		this._subscribeToViewChange();
		this._subscribeToOverlayChange();
	}

	_subscribeToViewChange() {
		this._$viewChangeDetector.viewChange$.subscribe(
			this._onViewChange.bind(this)
		);
	}

	_subscribeToOverlayChange() {
		this._$overlay.overlayChange$.subscribe(
			this._onOverlayChange.bind(this)
		);
	}

	_onOverlayChange(evt) {
		if (evt === 'click')
			this.close();
	}

	_onResize() {
		this.mediaMatch = window.matchMedia(this.attrs.mediaQuery).matches;
		this._updateStatus(true);
	}

	_onViewChange(changer) {
		this._updateStatus(true);
	}

	_updateStatus(ignoreOverlay = false) {
		if (this._isOpened)
			this.open(true, ignoreOverlay);
		else
			this.close(true, ignoreOverlay);
	}

	_onTriggerClick() {
		if (this._isOpened)
			this.close();
		else
			this.open();
	}

	_onMediaMatchChange(val = this._mediaMatchValue) {
		if (val)
			this._setToMobile();
		else
			this._setToDesktop();
	}

	_setToMobile() {
		this._closeFull(true);
		this._closeBody(true);
		Tools.unBlockScroll();
	}

	_setToDesktop() {
		this._openBody(true);
		this._closeFull(true);
		Tools.unBlockScroll();
	}

	_openFull(noTrans = false) {
		const speed = noTrans ? 0 : this.SHOW_DURATION;

		TweenLite.to(this.host, speed, {
			y: 0,
			ease: Power2.easeOut
		});
		this._isOpened = true;
	}

	_closeFull(noTrans = false) {
		const speed = noTrans ? 0 : this.HIDE_DURATION;
		const parentPaddingTop = parseInt(getComputedStyle(this.host).getPropertyValue('padding-top')) * 2;
		const headerHeight = this._$headerElement[0].clientHeight;
		
		TweenLite.to(this.host, speed, {
			y: this.host.clientHeight - (parentPaddingTop + headerHeight),
			ease: Power2.easeInOut
		});
		this._isOpened = false;
	}
	
	_openBody(noTrans = false) {
		const speed = noTrans ? 0 : this.SHOW_DURATION;

		this._hideSummaryTab();
		TweenLite.to(this._$bodyElement, speed, {
			x: '0%',
			ease: Power2.easeOut
		});
		this._isOpened = true;
	}

	_closeBody(noTrans = false) {
		const speed = noTrans ? 0 : this.HIDE_DURATION;
		
		this._showSummaryTab();
		TweenLite.to(this._$bodyElement, speed, {
			x: '100%',
			ease: Power2.easeIn
		});
		this._isOpened = false;
	}

	_hideSummaryTab() {
		const orderSummaryElement = document.querySelector('.order-summary');
		
		if (orderSummaryElement)
			orderSummaryElement.classList.add('order-summary--tab-closed');
	}

	_showSummaryTab() {
		const orderSummaryElement = document.querySelector('.order-summary');

		if (orderSummaryElement)
			orderSummaryElement.classList.remove('order-summary--tab-closed');
	}
}