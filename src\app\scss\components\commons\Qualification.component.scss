.Qualification {
    display: flex;
    background: url('../../../img/ebox-texture-background--red.jpg') center center repeat;
    width: 100%;
    padding: 15px 0;
    transition: $t-primary;
    transform: translateY(-100%);

    &__wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &__address {
        display: flex;
        align-items: center;
        margin: 0;
        color: $c-white;
        font: 700 16px/30px $f-primary;
        flex-grow: 1;
        white-space: nowrap;
        // overflow: hidden;
        padding-right: 10px;
        @media (max-width: 500px) {
            max-width: 300px;
        }
        .icon {
            width: 32px;
            height: 32px;
            fill: $c-white;
            vertical-align: bottom;
            margin-right: 5px;
            flex-shrink: 0;
        }
        @media (max-width: 767px) {
            font: 700 10px/16px $f-primary;

            .icon {
                width: 22px;
                height: 22px;
            }
        }
    }

    &__status {
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &--closed {
        height: 0;
        padding: 0;
    }

    &__button {
        // flex-shrink: 0;
        .icon {
            height: 25px;
            width: 25px;
            fill: white;
            @media(max-width: 767px){
                height: 18px !important;
            }
        }

        &-icon {
            display: none;
            width: 20px;
            height: 20px;
            fill: #FFF;
        }
    }

    @media (max-width: 767px) {
        padding: 9px 0;

        .container {
            width: 100%;
        }

        &__button {
            border: none;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            background: none;

            &:hover, &:focus {
                background: none;
                box-shadow: none;
                outline: none;
            }

            &:before, &:after {
                content: none;
            }

            &-title {
                display: none;
            }

            &-icon {
                display: inline-block;
            }
        }
    }
}

.VirageNum__packages-wrapper,
.VirageNum__forfait-telephonie {
    .Qualification {
        margin-bottom: 60px;
        border-radius: 8px;
        background: $c-primary;
        width: auto;
        max-width: fit-content;
        margin: 0 auto 60px;
        padding: 4px 0;
        .container {
            width: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        &__address {
            font: 12px/20px $f-primary;
            strong {
                font-weight: 400;
            }
            .icon {
                width: 24px;
                height: 24px;
            }
        }
        .Qualification__button {
            display: flex;
            margin-left: 10px;
        }
        .Qualification__button-icon {
            display: inline-block;
        }

        .icon-ic-edit-info2022 {
            fill: $c-white;
            width: 18px;
            height: 18px;

        }
    }
}

.VirageNum__forfait-telephonie {
    .Qualification {
        margin: 30px auto -20px;
    }
}

.titleSearchForm {
    text-align: center;
    max-width: 670px;
    font: 900 35px/40px $f-primary;
    color: $c-white;
    margin: 0 auto 44px;
}

.voirToute {
    font: 900 22px/26px $f-primary;
    padding: 10px;
    color: $c-white;
    margin: 36px auto 0;
    text-align: center;
    text-decoration: underline;
    display: inline-block;
    border: 3px solid #BE2323;
    border-radius: 10px;
    &:focus {
        outline: 2px solid blue;
        outline-offset: 2px;
        border-radius: 10px;
        border: 3px solid #FFF;
        color: #FFF;
    }
    &:hover {
        text-decoration: underline;
        color: #FFFFFF;
        border-radius: 10px;
        border: 3px solid #FFF;
    }
}

.besoinAide {
    font: 900 35px/40px $f-primary;
    color: $c-black;
    margin: 0 0 26px 0;
    text-align: center;
    text-transform: uppercase;
}

.besoinAideText {
    font: 700 16px/22px $f-primary;
    color: $c-black;
    max-width: 424px;
    text-align: center;
    margin: 0 auto 90px;
    a {
        text-decoration: underline;
    }
}

 
.HeaderTop {
        .Qualification {
            display: none!important;
        }
    }
