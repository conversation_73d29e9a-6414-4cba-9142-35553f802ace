.HomeDescription {
	width: 100%;
	padding: 70px 0;
	background-image: url('../../../img/background_middle.png') !important;
	background-repeat: no-repeat;
	background-position: 0 30%;
	background-size: contain;

	@media (max-width: 767px) {
		background-size: cover;
		background-position: center;
		padding: 40px 0 0;
		background-image: none !important;
	}
	
	&__row {
		display: flex;
		align-items: center;

		@media (max-width: 767px) {
			position: relative;
			margin: 0px;
			flex-direction: column !important;
			justify-content: center !important;
			padding: 70px 15px !important;

			&:before {
				content: '';
				display: block;
				position: absolute;
				background: #C7C7C7;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: calc(100% - 30px);
				height: 1px;
			}
		}
		
		.HomeDescription {
			&__imagecontent {
				flex: 0 0 auto;
			}

			&__textcontent {
				flex-grow: 1;
			}
		}

		&:nth-child(odd) {
			padding: 0 8.5vw;
			justify-content: flex-start;

			.HomeDescription {
				&__devices-image {
					margin-right: 7.5vw;
					width: 529px;
					height: auto;
					box-sizing: content-box;

					@media (min-width: 2160px) {
						margin-right: 6vw;
					}

					@media (max-width: 1399px) {
						width: 400px;
						margin-right: 44px;
					}
					@media (max-width: 1024px) {
						width: 240px;
					}
					@media (max-width: 767px) {
						margin: 0;
					}
				}
			}
		}
		&:nth-child(even) {
			padding: 0 8.5vw;
			justify-content: flex-start;
			flex-direction: row-reverse;

			.HomeDescription {
				&__devices-image {
					margin-left: 7.5vw;
					width: 269px;
					height: auto;

					@media (min-width: 2160px) {
						margin-left: 6vw;
					}

					@media (max-width: 1399px) {
						width: 200px;
						margin-left: 44px;
					}
					@media (max-width: 1024px) {
						width: 137px;
					}
					@media (max-width: 767px) {
						margin: 0;
					}
					@media (max-width: 600px) {
						width: auto;
					}
				}
			}
		}

		&:last-child {
			padding-right: 5vw;
		}

		&--space-ends.HomeDescription__row--space-ends {
			padding: 20px 8.5vw 70px;

			@media (min-width: 1921px) {
				padding: 4vw 8.5vw 3vw;
			}
		}
	}

.container {
	@media (min-width: 768px) and (max-width: 767px) {
		max-width: 361px;
	}
}

&__device-arrow {
	display: inline-block;
	position: absolute !important;
	width: 75px;
	height: 75px;
	margin-top: -37px;
	animation: splash 0.8s infinite steps(3);

	@media (max-width: 1550px) {
		width: 60px;
		height: 60px;
		margin-top: -30px;
	}

	@media (max-width: 1399px) {
		display: none;
	}
}

&__imagecontent {
	position: relative;

	@media (max-width: 767px) {
		text-align: center;
		margin-bottom: 30px;
	}
}

&__row {
	&:nth-child(1) {
		.HomeDescription {
			&__device-arrow {
				top: 35%;
				right: 35px;
				transform: rotateX(180deg);
				animation: splashRotatedX 0.8s infinite steps(3);
				animation-delay: 0s;
			}
		}
	}

	&:nth-child(2) {
		.HomeDescription {
			&__device-arrow {
				top: 30%;
				left: 35px;
				transform: rotateY(-180deg);
				animation: splashRotatedY 0.8s infinite steps(3);
				animation-delay: 0.2s;
			}
		}
	}

	&:nth-child(3) {
		.HomeDescription {
			&__device-arrow {
				top: 10%;
				right: 35px;
				animation: splashNormal 0.8s infinite steps(3);
				animation-delay: 0.4s;
			}
		}
	}
}

&__devices-image {
	width: auto;
	max-width: 100%;

	&--mobile {
		display: none;
	}
	@media (max-width: 767px) {
		max-width: 440px;
		margin: 0px auto;
		max-width: 270px;
	}
	@media (max-width: 600px) {
		&--mobile {
			display: block;
		}

		&--no-mobile {
			display: none;
		}
	}
}

&__lastimg > img {
	position: relative;
	width: auto;
	max-width: 100%;
	top: -100px;

	@media (max-width: 767px) {
		top: auto;
	}
}

&__textcontent {
	max-width: 960px;

	@media (max-width: 767px) {
		padding: 0 !important;
	}
}

&__textcontent.text-right {
	@media (max-width: 767px) {
		text-align: center;
	}
}

&__textcontent.topsection {
	padding: 0 0 40px;

	@media (max-width: 900px) {
		padding: 0;
	}
}

&__textcontent > h4 {

	font: 700 35px/40px $f-primary !important;
	color: #FFFFFF;
	float: left;
	width: 100%;
	@media (max-width: 1024px) {
		font: 700 26px/32px $f-primary !important;
		margin-bottom: 0;
	}
	@media (max-width: 767px) {
		font: 700 24px/26px $f-primary !important;
		text-align: center;
	}
}

&__textcontent > span {
	font: 400 20px/30px Gloria Hallelujah !important;
	color: #C7C7C7;
	float: left;
	width: 100%;
	margin-top: 20px;
	@media (max-width: 1024px) {
		font: 400 16px/20px Gloria Hallelujah !important;
		margin-bottom: 0;
	}
	@media (max-width: 767px) {
		font: 400 14px/20px Gloria Hallelujah !important;
		text-align: center;
		margin-bottom: 40px;
		padding: 0 !important;
	}
}
	&__button {
		margin: 20px 0 0 0;
		
		@media (max-width: 767px) {
			float: none;
		}
	}
	&__button.text-left {
		float: left;
		@media (max-width: 767px) {
			float: none;
		}
	}

;
	&__textcontent.lastcontent {
		@media (max-width: 767px) {
			text-align: center;
		}
	}
	&__content-middle &__imagecontent {
		float: right;
		@media (max-width: 767px) {
			float: left !important;
		}
	}

}

@keyframes splashRotatedX {
	0% {
		transform: rotateX(180deg) scale(1);
	}

	100% {
		transform: rotateX(180deg) scale(1.1);
	}
}

@keyframes splashRotatedY {
	0% {
		transform: rotateY(180deg) scale(1);
	}

	100% {
		transform: rotateY(180deg) scale(1.1);
	}
}

@keyframes splashNormal {
	0% {
		transform: scale(1);
	}

	100% {
		transform: scale(1.1);
	}
}