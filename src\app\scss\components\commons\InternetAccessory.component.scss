.InternetAccessory {
	display: inline-flex;
	align-items: center;
	color: $c-medium-grey;
	text-align: left;

	&__pricing-list, &__icon {
		display: inline-block;
	}

	&__icon {
		width: 50px;
		min-width: 50px;
		height: 50px;
		fill: currentColor;
		margin-right: 15px;
		@media (max-width: 380px) {
			width: 30px;
			min-width: 30px;
			height: 30px;
		}
	}

	&__pricing-list {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	&__pricing-item {
		font: 400 14px/20px $f-primary;

		@media (max-width: 380px) {
			font: 400 12px/16px $f-primary;
		}
	}

	&__pricing-title {
		font-weight: 700;
	}
}