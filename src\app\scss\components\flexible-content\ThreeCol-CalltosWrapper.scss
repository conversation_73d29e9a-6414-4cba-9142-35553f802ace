.ThreeCol-CalltosWrapper {
    width: 100%;
    display: inline-block;
    position: relative;
    padding: 100px 0 90px;
    ul {
        margin: 0;
    }
    &__block {
        width: 357px;
        float: left;
        border-right: 1px solid $c-light-grey;
        margin: 0 40px 0 0;
        padding: 0 40px 0 0;
        &:first-of-type {
            padding-left: 0;
        }
        &:last-of-type {
            width: 346px;
            margin-right: 0;
            border: none;
            @media (max-width: 1199px) {
                width: 297px;
            }
            @media (max-width: 991px) {
                width: 100%;
                margin: 0;
                padding: 0;
            }
        }
        @media (max-width: 1199px) {
            width: 295px;
            margin: 0 26px 0 0;
            padding: 0 26px 0 0;
        }
        @media (max-width: 991px) {
            width: 100%;
            margin: 0 0 70px 0;
            padding: 0 0 70px;
            border-right: none;
            border-bottom: 1px solid $c-light-grey;
        }
        @media (max-width: 767px) {
            margin: 0 0 40px 0;
            padding: 0 0 40px;
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0;
        .col-md-12 {
            padding: 0 22px
        }
    }
    @media (max-width: 767px) {
        padding: 70px 0 50px;
        .col-md-12 {
            padding: 0 15px
        }
    }
}
