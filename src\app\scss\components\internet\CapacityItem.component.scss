
.CapacityItem {
    &__wrapper {
        width: 140px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        padding: 3px;
        box-sizing: border-box;
        @media (max-width: 991px) {
            width: 100%;
        }
    }
    svg.icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
        fill: $c-medium-grey;
        @media (max-width: 500px) {
            margin-bottom: 5px;
        }
    }
    &--bordered {
        border-right: 1px solid $c-medium-grey;

    }
    &--has-hover {
        //cursor: pointer;
        @media (hover: hover) {
            &:hover {
                .CapacityItem__border {
                    stroke-dashoffset: 0;
                    visibility: visible;
                    transition: all 0.5s ease-out, visibility 0s;
                    opacity: 1;
                    overflow: visible;
                }
                .CapacityItem__content-status {
                    opacity: 1;
                    transition-delay: 0.4s;
                    transform: scale(1);

                }
            }
        }
    }
    &--unique {
        width: 340px;
        .CapacityItem__wrapper {

            @media (max-width: 991px) {
                width: 100%;
            }
        }
    }

    &--twochild {
        width: 220px;
        .CapacityItem__wrapper {
            @media (max-width: 991px) {
            }
        }
    }


    @media (max-width: 991px) {
        &:nth-child(1) {
            flex: 0 0 50%;
            min-height: 60px;
            background: url('../../../img/black-pattern.png') center center repeat;
            color: $c-white;
        }
        &:nth-child(2) {
            flex: 0 0 50%;
            min-height: 60px;
            background: url('../../../img/black-pattern.png') center center repeat;
            border-right: 0px!important;
            color: $c-white;
        }
    }
    &__status {
        position: absolute;
        width: 140px;
        height: 132px;
        left: 50%;
        transform: translateX(-50%);

        @media (max-width: 500px) {
            width: 88px;
            height: 80px;
        }
    }
    &__content-status {
        width: 30px;
        height: 30px;
        background: $c-light-grey;
        position: absolute;
        right: 6px;
        top: -1px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom-left-radius: 12px;
        border-top-right-radius: 12px;
        opacity: 0;
        transition: all 0.1s linear;
        transition-delay: 0s;
        transform: scale(0.5);
        svg {
            width: 20px!important;
            margin-bottom: 0!important;
            height: 20px!important;
            fill: $c-white!important;
        }
        .icon-ic_check {
            visibility: hidden;
            opacity: 0;
            display: none;
        }

        @media (max-width: 500px) {
            width: 17px;
            height: 17px;
            border-bottom-left-radius: 6px;
            border-top-right-radius: 6px;
            svg {
                width: 10px!important;
                height: 10px!important;
            }

        }
    }
    &__title {
        font: 400 14px/20px $f-primary;
        color: $c-grey;
        margin-bottom: 5px;
        span {
            font: 700 20px/20px $f-primary;
            color: $c-grey;
            &:lang(en) {
                font: 700 17px/20px $f-primary;
            }
        }
    }
    &__title-exp {
        font: 400 12px/16px $f-primary;
        color: $c-medium-grey;
        text-align: center;

        @media (max-width: 500px) {
            //display: none;
        }
    }
    $border-rect-length: 512.90;
    &__border {
        stroke: $c-light-grey;
        position: absolute;
        display: block;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: visible;
        stroke-dasharray: $border-rect-length;
        stroke-dashoffset: $border-rect-length;
        stroke-linecap: round;
        transition: stroke-dashoffset 0.3s ease-out, opacity 0.5s ease-out, visibility 0s linear 0.3s;
        visibility: hidden;
        transform: rotate(90deg);
        opacity: 0;
    }
    &__content-svg {
        transition: all 0.5s $cubic;
    }
    &__radio {
        position: fixed;
        width: 1px;
        height: 1px;
        visibility: hidden;
        &:checked {
            + .CapacityItem__wrapper {
                .CapacityItem__content-status {
                    background: $c-primary;
                    opacity: 1;
                    transform: scale(1);
                    transition-delay: 0s!important;

                    .icon-ic_check {
                        visibility: visible;
                        opacity: 1;
                        display: block;
                    }
                    .icon-ic_add {
                        visibility: hidden;
                        opacity: 0;
                        display: none;
                    }
                }
                .CapacityItem__content-svg {
                    fill: $c-primary!important;
                }
                .CapacityItem__border {
                    opacity: 1;
                    stroke: $c-primary;
                    stroke-dashoffset: 0;
                    visibility: visible;
                    overflow: visible;
                    @media (max-width: 500px) {
                        display: none;
                    }

                }

            }
        }
    }
    &__content-download,
    &__content-upload {
        .CapacityItem__wrapper {
            @media (max-width:991px) {
                flex-direction: column;
                width: 100%;
                svg {
                    width: 40px!important;
                    height: 40px!important;
                    margin-bottom: 0!important;
                    margin-right: 10px;
                    flex-shrink: 0;
                }
                p {
                    color: $c-white;
                    margin-bottom: 0;
                    span {
                        color: $c-white;
                    }
                }
                .CapacityItem__title-exp {
                    //display: none;
                }
            }
        }
    }
    &__content-small,
    &__content-medium,
    &__content-illimite {
        @media (max-width: 991px) {
            flex: 0 0 33%;
            height: 120px;
            margin-top: 15px;
            margin-bottom: 15px;
        }
    }
    &__content-illimite {
        @media (max-width: 991px) {
            border-right: 0px !important
        }
    }
}
body:not(.single-internet) {
    .CapacityItem__content-status {
        @media (min-width: 768px) and (max-width: 991px) {
            width: 20px;
            height: 20px;
            border-bottom-left-radius: 6px;
            border-top-right-radius: 6px;
            right: 6px;
            top: 0;
            svg {
                width: 12px!important;
                height: 12px!important;
            }
        }
    }
    .CapacityItem__title-exp {
        @media (min-width: 768px) and (max-width: 991px) {
           // display: none;
        }
    }
    .CapacityItem__status {
        @media (min-width: 768px) and (max-width: 991px) {
            width: 110px;
            height: 102px;
        }
    }
}

.single-internet {
    @media (max-width:991px) {
        .CapacityItem__wrapper {
            height: auto;
        }
        .WrapperCapacity {
            height: 170px;
            align-items: center;
        }
    }
    @media (max-width: 500px) {
        .WrapperCapacity {
            height: auto;
        }
    }
}

.WrapperCapacity {
    .CardItem {
        width: 140px;
        height: auto;
        padding: 0;
        box-shadow: none;
        border-radius: 0;
        &:last-of-type {
            border-right: 1px solid $c-light-grey;
        }
        p {
            display: flex;
            flex-direction: column;
        }
        &__title {
            font: 400 12px/16px $f-primary;
            color: #626262;
            order: 2;
        }
        &__subTitle {
            font: 700 20px/20px $f-primary;
            color: #343434;
            margin-bottom: 5px;
            text-transform: none;
        }
        &__icon {
            width: 50px;
            height: 50px;
        }
        svg {
            fill: $c-light-grey;
        }
    }
}

.CapacityItem__content-illimite {
    .CapacityItem__wrapper {
        padding: 3px 14px;
    }
}