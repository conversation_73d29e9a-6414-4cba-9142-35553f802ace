.PageTitle {
    width: 100%;
    //background: url('../../../img/bg-pagetitle.png') center top no-repeat;
    background: $c-black;
    min-height: 280px;
    background-size: 1920px 280px;
    display: flex;
    align-items: center;

    &--top-padded {
        padding-top: 79px;
    }

    &__container {
        //margin-top: -30px;
        @media (max-width: 767px) {
            width: 100%;
            margin-top: 0;
        }
    }
    &__title {
        color: $c-white;
        font: 900 50px/55px $f-primary;
        text-transform: uppercase;

        @media (max-width: 1024px) {
            font: 900 40px/45px $f-primary;
            //margin-top: 60px;
        }
        @media (max-width: 767px) {
            font: 900 22px/25px $f-primary;
        }
    }
    @media (min-width: 1200px) {
        background-size: 100% 280px;
    }
    @media (max-width: 1024px) {
        min-height: 210px;
        background-size: cover;
    }
    @media (max-width: 767px) {
        min-height: 90px;
        background-size: 800px 80px;
        background-size: auto;
        padding-top: 30px;
        padding-bottom: 30px;
    }
    &__subtitle {
        color: $c-white;
        margin-top: 10px;
        font: 400 16px/22px $f-primary;
        max-width: 570px;
        a {
            text-decoration: underline;
        }
    }
    &--inverted {
        background: none;
        .PageTitle__title {
            color: $c-black;
        }
        .PageTitle__subtitle {
            color: $c-black;
        }
    }
    &--aide {
        min-height: 425px;
        @media(max-width: 767px){
            min-height: 325px;
        }
        .PageTitle__subtitle {
            max-width: 455px;
            @media(max-width: 767px){
                margin-bottom: 40px;
            }
            @media(max-width: 599px){
                max-width: 380px;
            }
            @media(max-width: 399px){
                margin-bottom: 60px;
            }
        }

        .PageTitle__img {
            position: absolute;
            bottom: -128px;
            right: -85px;
            @media(max-width: 1300px){
                img {
                    height: 300px;
                }
            }
            @media(max-width: 991px){
                right: -60px;
                bottom: -133px;
                img {
                    height: 200px;
                }
            }
            @media(max-width: 767px){
                right: 15px;
                bottom: -112px;
                img {
                    height: 140px;
                }
            }
            @media(max-width: 599px){
                bottom: -100px;
                img {
                    height: 110px;
                }
            }
            @media(max-width: 399px){
                bottom: -112px;
            }
        }

        .PageTitle__img--en {
            bottom: -143px !important;
            @media(max-width: 767px){
                bottom: -122px !important;
            }
            @media(max-width: 599px){
                bottom: -112px !important;
            }
            @media(max-width: 399px){
                bottom: -122px !important;
            }
        }
    }
}

.single-internet {
    .PageTitle__subtitle {
        @media (max-width: 767px) {
            display: none;
        }
    }
}

// Page À propos FR - EN 
.page-id-51, 
.page-id-7257,
.page-id-114096,
.page-id-114099,
.page-id-118682,
.page-id-118863 {
    .PageTitle {
        display: none;
    }
}