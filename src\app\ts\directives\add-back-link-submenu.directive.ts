import { MenuCloserDirective } from './menu-closer.directive';

export class AddBackLink {
    static selector: string = '.MobileNavPrinc__sub-menu';

    $host: JQuery = jQuery(this.host);

    private activeClassName: string = this.host.getAttribute('active-class') || '';

    private readonly subMenuOpenedClassName: string = 'sub-opened';
    private langAttribute: string = document.documentElement.lang;
    private $backLink: JQuery;


    constructor(private host: HTMLElement) {
        this.onInit();
    }

    private onInit(): void {
        this.createBackLink();
        this.addBackLink();
        this.bindEvents();
    }

    private createBackLink(): void {
        const currentMenuItemText: string = this.host.parentElement.firstChild.textContent;
        const isFrench: boolean = this.langAttribute.indexOf('fr') !== -1;
        const backWord: string = isFrench ? 'Retour' : 'Back';
        const template: string = `
            <li class="back-link">
                <a href="#"><svg class="icon icon-ic_arrow-left"><use xlink:href="#icon-ic_arrow-left"></use></svg>${ currentMenuItemText }</a>
            </li>
        `;
        this.$backLink = jQuery(template);
    }

    private addBackLink(): void {
        this.$host.prepend(this.$backLink);
    }

    private bindEvents(): void {
        this.$backLink.on('click', this.closeSubMenu.bind(this));
    }

    private closeSubMenu(): void {
       MenuCloserDirective.handleSubMenus();
    }

}
