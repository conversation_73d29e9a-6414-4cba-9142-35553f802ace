.TwoCol-LeftImgRightText {
    padding: 100px 0 100px;
    width: 100%;
    display: inline-block;
    position: relative;
    &__title {
        margin: 0 0 80px;
    }
    &__leftImg {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 15px;
        overflow: hidden;
        opacity: 0;
        transform: translateX(-100%);
        transition: all 0.7s $cubic;
        will-change: opacity, transform;
        &--shown {
            opacity: 1;
            transform: translateX(0);
        }
        img {
            max-width: 100%;
            height: auto;

            @media (max-width: 991px) {
                height: auto;
                max-height: 100%;
                width: auto;
                max-width: 100%;
                top: auto!important;
                left: 50%;
            }
        }
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 22px;
            margin: 0 0 30px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
    }
    &__rightTxt {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 15px 0 45px;
        h2, h3, h4, h5, h6 {
            margin: 0 0 10px;
        }
        h5 {
            margin: 30px 0 10px;
        }
        p {
            margin: 0 0 30px;
            &:last-of-type {
                margin: 0;
            }
            img {
                margin: 0 5px 0 0;
                float: left;
            }
            a[href^=tel] {
              color: $c-grey;
            }
            &.small-height {
                line-height: 14px;
                span.small {
                    margin: 0 0 10px 0;
                    display: block;
                }
            }
        }
        .Button, .ButtonEffect {
            margin: 40px 40px 0 0;
            float: left;
            &--border {
                height: 60px;
                margin: 40px 0 0!important;
                float: left;
                line-height: 58px;
                &:hover {
                    border-color: $c-medium-grey;
                }
                @media (max-width: 1199px) {
                    margin: 15px 0 0 0!important;
                }
                @media (max-width: 991px) {
                    border-width: 2px;
                    height: 40px;
                    line-height: 38px;
                    margin: 40px 0 0 0!important;
                    padding: 0 13px;
                }
                @media (max-width: 356px) {
                    margin: 10px 0 0 0!important;
                }
            }
            @media (max-width: 767px) {
                margin: 40px 10px 0 0;
                padding: 0 13px;
            }
        }
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 19px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    @media (max-width: 767px) {
        padding: 40px 0;
    }
}

body.page-id-5923 {
    .TwoCol-LeftImgRightText {
        p {
            line-height: 26px;
            margin: 0 0 5px;
            a {
                color: $c-grey;
                &:hover {
                    color: $c-primary;
                }
            }
            img {
                margin: 0 5px 5px 0;
            }
        }
        .twoCol-LeftImgRightText__terminus {
            position: absolute;
            right: -17px;
            top: 85px;
            transition: $t-primary;
            span {
                font: 300 16px/20px $f-secondary;
                color: $c-medium-grey;
                text-align: center;
                @media (max-width: 1100px) {
                    font-size: 12px;
                }
            }
            p {
                position: relative;
                opacity: 1;
                transform: translateX(0);
                transition: $t-primary;
                transition-delay: 1s;
                &:before {
                    width: 60px;
                    height: 60px;
                    content: "";
                    position: absolute;
                    left: -64px;
                    top: -23px;
                    background: url('../../../img/Icon/drawing-arrow02-grey.svg') center center no-repeat;
                    background-size: 100%;
                    transform: rotate(0deg);
                    @media (max-width: 1200px) {
                        width: 40px;
                        height: 40px;
                        transform: scaleX(-1) rotate(105deg);
                        top: -43px;
                        left: 39px;
                    }
                    @media (max-width: 1100px) {
                        width: 30px;
                        height: 30px;
                        transform: scaleX(-1) rotate(-1000deg);
                        top: -33px;
                        left: 33px;
                    }
                }
            }
            @media (max-width: 1400px) {
                top: 108px;
                right: -25px;
            }
            @media (max-width: 1100px) {
                top: 100px;
                right: 10px;
            }
            @media (max-width: 991px) {
                top: 100px;
                right: 300px;
            }
            @media (max-width: 767px) {
                display: none;
            }
        }
    }
}

body.page-id-8265, body.page-id-10827 {
    .TwoCol-LeftImgRightText {
        &__leftImg {
            img {
                position: relative;
                top: 50%;
                transform: translateY(-50%);
            }
        }
        &__rightTxt {
            h2, h3, h4 {
                margin: 0 0 60px;
                @media (max-width: 1024px) {
                    margin: 0 0 10px;
                }
            }
            h5 {
                margin: 0 0 10px;
            }
            h5 + ul {
                margin: 0 0 40px;
                @media (max-width: 1024px) {
                    margin: 0 0 30px;
                }
            }
            .col2 {
                float: left;
                margin: 0 55px 0 0;
                @media (max-width: 767px) {
                    float: none;
                    margin: 0;
                }
                &:last-child {
                    margin: 0;
                }
            }
            .Wysiwyg {
                display: inline-block;
                transform: translateY(-50%);
                top: 50%;
                position: relative;
                @media (max-width: 991px) {
                    transform: none;
                    top: auto;
                }
            }
        }
    }
}


.marginbottom30 {
    margin-bottom: 30px;
    display: inline-block;
}