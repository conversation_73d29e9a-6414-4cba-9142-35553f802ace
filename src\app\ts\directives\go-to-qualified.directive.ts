import { QualifyService, Qualification } from '@common/ts/services/Qualify.service';
import { Directive } from '@core/directive';
import { Subscription } from 'rxjs/Subscription';


export class GoToQualified extends Directive {
	public static selector: string = '[data-go-to-qualified]';

	private qualifyChangeSub: Subscription;
	
	private $qualifier: QualifyService = QualifyService.getInstance();

	private qualification: Qualification = this.$qualifier.getQualification();

	private originalHref: string = this.attrs.href;

	constructor(host: HTMLElement) {
		super(host, ['href']);
		this.onInit();
	}

	private onInit(): void {
		this.updateHostHref();
		this.subscribeToQualifyChange();
	}

	private subscribeToQualifyChange(): void {
		this.qualifyChangeSub = this.$qualifier.qualificationChange$.subscribe(
			this.onQualificationChange.bind(this)
		);
	}

	private onQualificationChange(currentQualification: Qualification): void {
		this.qualification = currentQualification;
		this.updateHostHref();
	}

	private updateHostHref(): void {
		if (this.qualification) {
			const { details } = this.qualification;

			if (details && details.internetType) {
				this.attrs.href = this.originalHref
					.replace(/\/$|\/?([a-z]+-cable)\/?$/g, '/' + details.internetType);
			}
		}
	}
}