.TeaserHeader {
	padding: 100px 60px 230px;
	overflow: hidden;
	box-shadow: inset 0 5px 30px 0 rgba(0, 0, 0, 0.45);
	@media (max-width: 1299px) {
		padding: 70px 15px 180px;
	}
	@media (max-width: 991px) {
		padding: 70px 15px 150px;
	}
	@media (max-width: 600px) {
		padding: 30px 15px 80px;
	}
	@media (max-width: 400px) {
		padding: 30px 15px 70px;
	}
	
	&__title {
		margin: 0;
		text-align: center;
		font: 900 80px/1.4 $f-primary;
		text-transform: uppercase;

		@media (max-width: 1299px) {
			font-size: 60px;
		}
		@media (max-width: 991px) {
			font-size: 50px;
		}
		@media (max-width: 550px) {
			font-size: 40px;
		}
		@media (max-width: 375px) {
			font-size: 20px;
		}
	}

	&__title-row {
		&:last-of-type {
			margin-bottom: 0;

			.is-doodled--splashed {
				&:before {
					display: none;
				}
			}

			$arrowDelay: 0.5;
			.Teaser<PERSON>eader__title-arrow {
				opacity: 0;
				transition: all 0.15s $cubic;

				&--left {
					transform: translate(-15px, 10px);
					transition-delay: #{$arrowDelay + 0}s;
				}
				&--right {
					transform: translate(15px, 10px);
					transition-delay: #{$arrowDelay + 0.15}s;
				}

				@media (max-width: 600px) {
					display: none;
				}
			}


			&.TeaserHeader__title-row {
				&--landed {
					.is-doodled--splashed {
						&:before {
							display: block;
						}
					}

					.TeaserHeader__title-arrow {
						opacity: 1;
						transform: translate(0, 0);
					}
				}
			}
		}
	}

	&__title-arrow {
		position: absolute;
		fill: $c-white;

		&--left {
			width: 31px;
			height: 27px;
			right: 105%;
			bottom: -10px;
		}

		&--right {
			width: 55px;
			height: 32px;
			right: -65px;
			top: 50%;
		}
	}
}