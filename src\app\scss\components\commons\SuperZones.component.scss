#superzones { width:923px; max-width: 100%; height: 497px; margin: 0; padding: 0; background: url('../../../img/superzones-map.png') right top no-repeat; position:relative;}
#superzones img {display: none}

.popup-superzone {
    p {
        font: 18px/25px $f-primary;
        color: $c-white;
        @media (max-width: 767px) {
            padding: 0 10px;
            font: 16px/22px $f-primary;
        }
    }
}

/* QUEBEC */
p.t-quebec { position: absolute; top:78px; left: 608px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-quebec { width: 30px; height: 30px; position:absolute; left: 667px; top:80px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-quebec:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-quebec a { display: block; width: 100%; height: 100%;}

/* MONTREAL */
p.t-montreal { position: absolute; top:159px; left: 668px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-montreal { width: 30px; height: 30px; position:absolute; left: 638px; top:145px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat;background-size: 30px; z-index: 3;cursor: pointer;}
.r-montreal:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-montreal a { display: block; width: 100%; height: 100%;}

/* SHERBROOKE */
p.t-sherbrooke { position: absolute; top:136px; left: 715px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-sherbrooke { width: 30px; height: 30px; position:absolute; left: 683px; top:132px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-sherbrooke:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-sherbrooke a { display: block; width: 100%; height: 100%;}

/* TROIS-RIVIÈRES */
p.t-troisrivieres { position: absolute; top:100px; left: 532px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-troisrivieres { width: 30px; height: 30px; position:absolute; left: 632px; top:98px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-troisrivieres:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-troisrivieres a { display: block; width: 100%; height: 100%;}

/* ST-JÉROME */
p.t-stjerome { position: absolute; top:137px; left: 520px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-stjerome { width: 30px; height: 30px; position:absolute; left: 618px; top:135px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-stjerome:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-stjerome a { display: block; width: 100%; height: 100%;}

/* OTTAWA */
p.t-ottawa { position: absolute; top:193px; left: 503px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-ottawa { width: 30px; height: 30px; position:absolute; left: 557px; top:190px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-ottawa:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-ottawa a { display: block; width: 100%; height: 100%;}

/* BURLINGTON */
p.t-burlington { position: absolute; top:368px; left: 356px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-burlington { width: 30px; height: 30px; position:absolute; left: 433px; top:365px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-burlington:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-burlington a { display: block; width: 100%; height: 100%;}

/* BARRIE */
p.t-barrie { position: absolute; top:318px; left: 378px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-barrie { width: 30px; height: 30px; position:absolute; left: 422px; top:315px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-barrie:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-barrie a { display: block; width: 100%; height: 100%;}

/* TORONTO */
p.t-toronto { position: absolute; top:343px; left: 476px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-toronto { width: 30px; height: 30px; position:absolute; left: 445px; top:340px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-toronto:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-toronto a { display: block; width: 100%; height: 100%;}

/* HAMILTON */
p.t-hamilton { position: absolute; top:376px; left: 468px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-hamilton { width: 30px; height: 30px; position:absolute; left: 440px; top:369px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-hamilton:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-hamilton a { display: block; width: 100%; height: 100%;}

/* LONDON */
p.t-london { position: absolute; top:401px; left: 417px; z-index: 3; font-family: $f-primary; font-weight: 600; font-size: 18px; margin: 0; color: #000;}
.r-london { width: 30px; height: 30px; position:absolute; left: 385px; top:397px; background: url('../../../img/ic_pin-ebox-white.svg') left top no-repeat; background-size: 30px; z-index: 3;cursor: pointer;}
.r-london:hover { background: url('../../../img/ic_pin-ebox-white.svg') left bottom no-repeat;background-size: 30px;}
.r-london a { display: block; width: 100%; height: 100%;}

@media(max-width: 991px) {
    /* QUEBEC */
    p.t-quebec { left: 405px;}
    .r-quebec { left: 463px;}

    /* MONTREAL */
    p.t-montreal {left: 453px;}
    .r-montreal {left: 423px;}

    /* SHERBROOKE */
    p.t-sherbrooke {left: 503px;}
    .r-sherbrooke {left: 468px;}

    /* TROIS-RIVIÈRES */
    p.t-troisrivieres {left: 335px;}
    .r-troisrivieres {left: 432px;}

    /* ST-JÉROME */
    p.t-stjerome {left: 305px;}
    .r-stjerome {left: 403px;}

    /* OTTAWA */
    p.t-ottawa {top: 185px; left: 308px;}
    .r-ottawa {top: 183px;left: 365px;}

    /* BURLINGTON */
    p.t-burlington {left: 152px;}
    .r-burlington {left: 230px;}

    /* BARRIE */
    p.t-barrie {left: 173px;}
    .r-barrie {left: 215px;}

    /* TORONTO */
    p.t-toronto {top: 346px;left: 272px;}
    .r-toronto {top: 342px;left: 241px;}

    /* HAMILTON */
    p.t-hamilton {top: 370px;left: 268px;}
    .r-hamilton {top: 368px;left: 238px;}

    /* LONDON */
    p.t-london {left: 210px;}
    .r-london {left: 180px;}
}

@media(max-width: 767px) {
    #superzones {width: 300px;height: auto;overflow:hidden;background: none;margin:0 auto;}
    #superzones img {display: block;width: 330px!important;max-width: none!important;left:-20px;position:relative;margin:0 auto;display: block}
    /* QUEBEC */
    p.t-quebec {top:16px; left: 185px;font-size: 10px;}
    .r-quebec { width: 15px; height: 15px; left: 217px; top:22px; background-size: 15px;}
    .r-quebec:hover {background-size: 15px;}

    /* MONTREAL */
    p.t-montreal {top:50px; left: 221px; font-size: 10px;}
    .r-montreal {width: 15px; height: 15px; left: 207px; top:47px; background-size: 15px;}
    .r-montreal:hover {background-size: 15px;}

    /* SHERBROOKE */
    p.t-sherbrooke {top:40px; left: 243px;font-size: 10px;}
    .r-sherbrooke {width: 15px; height: 15px; left: 224px; top:42px; background-size: 15px;}
    .r-sherbrooke:hover {background-size: 15px;}

    /* TROIS-RIVIÈRES */
    p.t-troisrivieres {top:27px; left: 151px;font-size: 10px;}
    .r-troisrivieres { width: 15px; height: 15px; left: 205px; top:32px; background-size: 15px;}
    .r-troisrivieres:hover {background-size: 15px;}

    /* ST-JÉROME */
    p.t-stjerome { top:40px; left: 146px; font-size: 10px;}
    .r-stjerome { width: 15px; height: 15px; left: 201px; top:45px; background-size: 15px;}
    .r-stjerome:hover {background-size: 15px;}

    /* OTTAWA */
    p.t-ottawa { top:57px; left: 152px; font-size: 10px;}
    .r-ottawa { width: 15px; height: 15px; left: 183px; top:62px; background-size: 15px;}
    .r-ottawa:hover {background-size: 15px;}

    /* BURLINGTON */
    p.t-burlington { top:120px; left: 89px; font-size: 10px;}
    .r-burlington { width: 15px; height: 15px; left: 132px; top:127px; background-size: 15px;}
    .r-burlington:hover {background-size: 15px;}

    /* BARRIE */
    p.t-barrie { top:105px; left: 104px; font-size: 10px;}
    .r-barrie { width: 15px; height: 15px; left: 130px; top:110px; background-size: 15px;}
    .r-barrie:hover {background-size: 15px;}

    /* TORONTO */
    p.t-toronto { top:112px; left: 157px; font-size: 10px;}
    .r-toronto { width: 15px; height: 15px; left: 140px; top:116px; background-size: 15px;}
    .r-toronto:hover {background-size: 15px;}

    /* HAMILTON */
    p.t-hamilton { top:125px; left: 153px; font-size: 10px;}
    .r-hamilton { width: 15px; height: 15px; left: 137px; top:128px; background-size: 15px;}
    .r-hamilton:hover {background-size: 15px;}

    /* LONDON */
    p.t-london { top:135px; left: 82px; font-size: 10px;}
    .r-london { width: 15px; height: 15px; left: 115px; top:138px; background-size: 15px;}
    .r-london:hover {background-size: 15px;}
}
