import { Directive } from '@core/directive';

export class FormResetDirective extends Directive {
	public static selector: string = '[data-form-reset]';

	public attrs: FormResetAttrs;

	private $resetEls: JQuery;

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'data-form-reset', as: 'selector'}
		]);
		this.init();
	}

	private init(): void {
		this.setResetEls();
		this.bindEvents();
	}

	private setResetEls(): void {
		const { selector } = this.attrs;
		const $parentForm = this.$host.closest('form') as JQuery<HTMLFormElement>;

		if (selector) {
			if ($parentForm) {
				this.$resetEls = $parentForm.find(selector);
			} else {
				this.$resetEls = jQuery(selector);
			}
		} else {
			this.$resetEls = $parentForm;
		}
	}

	private bindEvents(): void {
		this.host.addEventListener('click', this.onHostClick.bind(this));
	}

	private onHostClick(evt: JQuery.Event): void {
		evt.preventDefault();

		if (this.$resetEls.length) {
			this.$resetEls.each(this.resetElValue.bind(this))
		}
	}

	private resetElValue(_index: number, el: HTMLFormElement | HTMLInputElement): void {
		const $el: JQuery = jQuery(el);

		if (el instanceof HTMLFormElement) {
			el.reset();
		} else {
			$el.val('');
			$el.trigger('input');
		}
	}
}

export interface FormResetAttrs {
	selector: string;
}
