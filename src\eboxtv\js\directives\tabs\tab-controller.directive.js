import { Directive } from '@core/directive';
import { TabFor } from './tab-for';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service';

export class TabControllerDirective extends Directive {
	static selector = '[data-tab-controller]';

	$tabs;
	tabFors = [];

	$changeDetector = ViewChangeDetector.getInstance();

	openedTab;

	constructor(host) {
		super(host, ['data-tab-active-class']);
		this._init();
	}
	
	_init() {
		this._cacheDOM();
		this._initTabForInstances();
		this._bindEvents();
		this._updateActiveFromQueryParams();	
	}

	_cacheDOM() {
		const $nestedTabFors = this.$host.find('[data-tab-controller] [data-tab-for]');
		this.$tabs = this.$host.find('[data-tab-for]').not($nestedTabFors);
	}

	_bindEvents() {
		const _this = this;

		this.$tabs.on('click', function(evt) {
			evt.preventDefault();

			for (let i = 0; i < _this.tabFors.length; i++) {
				const currentTab = _this.tabFors[i];
	
				if (currentTab.host === this && !currentTab.isOpened) {
					currentTab.open();
				} else if (currentTab.host !== this && currentTab.isOpened) {
					currentTab.close();
				}
			}
	
			_this.$changeDetector.emitChange(this);
		});
	}

	_initTabForInstances() {
		this.$tabs.each((i, el) => {
			const childID = el.getAttribute('data-tab-for');

			if (childID) {
				this.tabFors.push(new TabFor(
					el,
					this.host.querySelector(childID),
					this.attrs.dataTabActiveClass
				));
			}
		});
	}

	_updateActiveFromQueryParams() {
		if (window.URLSearchParams) {
			const params = new URLSearchParams(location.search);

			if (params.has('tab')) {
				const activeTabID = params.get('tab');

				for (let i = 0; i < this.tabFors.length; i++) {
					const tabFor = this.tabFors[i];

					if (tabFor.host.getAttribute('data-tab-for') === `#${ activeTabID }`) {
						tabFor.host.click();
						break;
					}
				}
			}
		}
	}
}