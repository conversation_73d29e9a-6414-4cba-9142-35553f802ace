import { Directive } from '@core/directive';
import { TVOrderService } from '@eboxtv/js/services';
import './show-on-plain-package.scss';

export class ShowOnPlainPackage extends Directive {
	public static selector: string = '[show-on-plain-package]';

	private tvOrder: TVOrderService = TVOrderService.getInstance();

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'show-on-plain-package', as: 'displayType', default: ''}
		]);
		this.onInit();
	}

	private onInit(): void {
		this.subscribeToChange();
	}

	private subscribeToChange(): void {
		this.tvOrder.selectedBundlesChange$.subscribe(
			this.updateFromOrderStatus.bind(this)
		);
	}

	private updateFromOrderStatus(): void {
		const currentBundle: any = this.tvOrder.getSelectedBundle();

		if (currentBundle && currentBundle.isFeatured) {
			this.host.style.display = 'none';
		} else {
			this.host.style.display = this.attrs.displayType;
		}
	}
}