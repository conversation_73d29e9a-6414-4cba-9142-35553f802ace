.BusinessPrivateNetwork {
	display: flex;
	justify-content: center;
	align-items: center;
	background: {
		image: url('../../../img/bg-reseau-prive.jpg');
		size: cover;
		position: center;
	};

	&__container {
		text-align: center;
		color: #FFF;
		max-width: 970px;

		> * {
			opacity: 0;
			transform: translateX(-100%);
			transition: all 0.5s $cubic;
		}

		@media (max-width: 1199px) {
			padding: 0 60px;
			width: 80%;
		}
		@media (max-width: 1024px) {
			padding: 0 60px;
			width: 75%;
		}
		@media (max-width: 767px) {
			padding: 0;
		}
		@media (max-width: 600px) {
			width: 100%;
		}
	}

	&__title {
		font: 900 60px/1.16 $f-primary;
		margin-bottom: 44px;

		.is-doodled {
			&--underlined {
				&:after {
					height: 15%;
					top: 90%;
				}
			}
		}

		@media (max-width: 1199px) {
			font-size: 40px;
			margin-bottom: 20px;
		}
		@media (max-width: 1024px) {
			font-size: 32px;
			margin-bottom: 40px;
		}
		@media (max-width: 600px) {
			font-size: 25px;
		}
	}

	&__subtitle {
		font: 400 30px/1 $f-primary;
		width: 75%;
		margin: 0 auto 40px;

		@media (max-width: 1199px) {
			font-size: 20px;
			margin-bottom: 20px;
		}
		@media (max-width: 1024px) {
			display: none;
		}
	}

	@include mediaScreenRatio(2) {
		background-image: url('../../../img/<EMAIL>');

		@media (max-width: 960px) {
			background-image: url('../../../img/bg-reseau-prive.jpg');
		}
	}

	@include mediaScreenRatio(1.5) {
		background-image: url('../../../img/bg-reseau-prive@1,5x.jpg');

		@media (max-width: 1280px) {
			background-image: url('../../../img/bg-reseau-prive.jpg');
		}
	}

	@media (max-width: 960px) {
		background-image: url('../../../img/bg-reseau-prive@0,5x.jpg');
	}

	@media (max-width: 1024px) {
		padding: 80px 30px;
	}

	@at-root {
		.owl-item.active {
			.BusinessPrivateNetwork {
				&__container {
					> * {
						opacity: 1;
						transform: translateX(0);
						
						@for $i from 1 through 3 {
							&:nth-child(#{ $i }) {
								transition-delay: #{ (($i - 1) * 0.15s) + 0.2s };
							}
						}
					}
				}
			}
		}
	}
}