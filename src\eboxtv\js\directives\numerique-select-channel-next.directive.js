/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { LocalStorage } from '@common/ts/services/LocalStorage';
export class NumeriqueSelectChannelNext extends Directive {

	_$OrderService = OrderOnlineService.getInstance()

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[NumeriqueSelectChannelNext]';

    optionForfait = {}



	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        this.optionForfait = LocalStorage.get('forfaitOption');
        this.host.addEventListener('click', function(evt){
            this.clickNext(evt.currentTarget);
        }.bind(this))
	}


    clickNext(target){
        this.optionForfait.a_la_carte = true;
        if(this.optionForfait.a_la_carte = true){
            const chaines = LocalStorage.get('selectedChannels')
            for (const [key, value] of Object.entries(chaines)) {
                if(key == 'a-la-carte') {
                    const channelLength = value.length;
                    if(channelLength <  parseInt(this.optionForfait.nbChaines)){
                        console.log('veuillez selectionner plus de chaines');
                    }
                    else{
                        console.log('le nombre de chaine est sufisant');
                    }
                }
              }
        }
    }

}
