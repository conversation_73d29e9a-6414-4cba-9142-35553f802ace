/* global jQuery */
import { Directive } from '@core/directive';
import { TweenLite } from 'gsap/TweenLite';
import { Power2 } from 'gsap/EasePack';

import { overlayProvider } from '../services/overlay.service';
import { Tools } from '@common/ts/services/Tools.service';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service';

export class SidePanelDirective extends Directive {
	static selector = '[side-panel]';

	// PRIVATES PROPERTIES //
	_overlay 		 = overlayProvider;
	_mediaMatchValue = this.attrs.mediaQuery ? window.matchMedia(this.attrs.mediaQuery).matches : true;
	_isShownValue 	 = this.attrs.isOpened;
	_$triggerElement = jQuery(this.attrs.triggerSelector);
	_$closerElement  = jQuery(this.attrs.closerSelector);
	_$changeDetector = ViewChangeDetector.getInstance();


	// GETTERS AND SETTERS //
	get isShown() {
		return this._isShownValue;
	}
	set isShown(val) {
		if (val !== this._isShownValue) {
			this._isShownValue = val;
			this._update(val);
		}
	}

	get mediaMatch() {
		return this._mediaMatchValue;
	}
	set mediaMatch(val) {
		if (val !== this._mediaMatchValue) {
			this._mediaMatchValue = val;
			this._onMediaMatchChange(val);
		}
	}


	// INIT //
	constructor(host) {
		super(host, [
			{ name: 'trigger-selector', required: true },
			{ name: 'is-opened', type: 'eval', default: false },
			'closer-selector',
			'opened-class-name',
			'media-query'
		]);

		this._init();
	}
	_init() {
		this._bindEvents();
		this._subscribeToOverlayChange();
		if (this.mediaMatch)
			this._update(this._isShownValue, true);
	}

	/**
	 * @private
	 * @return {void}
	 */
	_bindEvents() {
		this._$triggerElement.on('click', this._onTriggerClick.bind(this));
		
		if (this.attrs.mediaQuery) {
			this._$changeDetector.viewChange$.subscribe(
				this._onResize.bind(this)
			);
		}

		if (this._$closerElement.length)
			this._$closerElement.on('click', this._onCloserClick.bind(this));
	}

	_subscribeToOverlayChange() {
		this._overlay.overlayChange$.subscribe(
			this._onOverlayChange.bind(this)
		);
	}

	/**
	 * @private
		* @return {void}
	 */
	_onTriggerClick() {
		this._toggleIsShown();
	}

	_onCloserClick() {
		this.isShown = false;
	}

	_onOverlayChange(evt) {
		if (evt === 'click' && this.mediaMatch)
			this._hide();
	}

	/**
	 * @private
	 * @return {void}
	 */
	_toggleIsShown() {
		this.isShown = !this.isShown;
	}

	/**
	 * @private
	 * @return {void}
	 */
	_update(val = this._isShownValue, noTrans = false) {
		if (val)
			this._show(noTrans);
		else
			this._hide(noTrans);
	}

	/**
	 * @private
	 * @return {void}
	 */
	_show(noTrans = false) {
		const speed = noTrans ? 0 : this._overlay.SHOW_DURATION;

		this._overlay.show({
			requestingElement: this.host,
			blockScroll: true
		});
		
		TweenLite.to(this.host, speed, {
			ease: Power2.easeOut,
			x: '0%',
			onComplete: () => {
				this.$host.addClass('is-full-opened');
			}
		});

		this._addClass();
		this._isShownValue = true;
	}

	/**
	 * @private
	 * @return {void}
	 */
	_hide(noTrans = false) {
		const speed = noTrans ? 0 : this._overlay.HIDE_DURATION;

		this._overlay.hide({
			requestingElement: this.host
		});

		TweenLite.to(this.host, speed, {
			ease: Power2.easeOut,
			x: this.host.clientWidth - 20,
			onComplete: () => {
				Tools.unBlockScroll();
				this.$host.removeClass('is-full-opened');
			}
		});

		this._removeClass();
		this._isShownValue = false;
	}

	_addClass() {
		if (this.attrs.openedClassName)
			this.$host.addClass(this.attrs.openedClassName);
	}

	_removeClass() {
		if (this.attrs.openedClassName)
			this.$host.removeClass(this.attrs.openedClassName);
	}

	/**
	 * @private
	 * @return {void}
	 */
	_onResize() {
		this.mediaMatch = window.matchMedia(this.attrs.mediaQuery).matches;
	}

	/**
	 * @private
	 * @return {void}
	 */
	_onMediaMatchChange(val) {
		this._adaptToMedia(val);
	}

	_adaptToMedia(val = this.mediaMatch) {
		if (!val) {
			this._reset();
		}
	}

	/**
	* @private
	* @return {void}
	*/
	_reset() {
		this._overlay.hide({
			time: 0,
			requestingElement: this.host
		});
		Tools.unBlockScroll();
		TweenLite.set(this.host, {
			clearProps: 'all'
		});
		this._removeClass();
		this._isShownValue = false;
	}
}