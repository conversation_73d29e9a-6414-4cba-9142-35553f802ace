.OneMonthFree {
	&__title {
		@media (min-width: 768px) {
			position: absolute;
			padding: 0;
			top: 50%;
			right: 50%;
			transform: translateY(-50%);
			text-align: right;
			padding-right: 80px;
			box-sizing: content-box;
		}
	}

	&__title-part {
		&--underlined {
			position: relative;
		}
	}

	.FreeInternet {
		&__title {
			text-align: right;
			color: #343434;

			@media (max-width: 767px) {
				text-align: center;
			}
			
			&:after {
				display: block;
				margin-left: auto;
				background-color: #626262;
			}
			
			&-part {
				&--main-top {
					margin-right: 16px;
				}		
			}
		}

		&__btn {
			&:hover {
				color: #FFF;
			}

			&:after {
				background: $c-grey;
			}
		}
		
		&__sub-title {
			text-transform: none;
			color: #626262;
		}

		&__promo-duration {
			&:before {
				background-image: url('../../../img/drawn-circle--black.svg');
			}

			&:after {
				background-image: url('../../../img/round-arrow--black.svg');
				right: 90%;
				left: auto;
			}
		}

		&__title-underline {
			path {
				stroke: #626262;
			}
		}

		&__disclaimer {
			color: #626262;
			padding: 0 0 0 30px;

			a {
				color: currentColor;
				text-decoration: underline;

				&:hover {
					color: $c-primary;
				}
			}
		}
	}

	.television {
		background-image: url('../../../img/tele.png');

		@include applyAtRoot('body:lang(en-CA)') {
			background-image: url('../../../img/tele-en.png');
		}
	}

	.mobile {
		top: auto;
		background-image: url('../../../img/ebox-phone-app.png');
		background-size: contain;
		
		@include applyAtRoot('body:lang(en-CA)') {
			background-image: url('../../../img/ebox-phone-app--en.png');
		}
	}

	.gamepad {
		position: absolute;
		width: 181px;
		height: 115px;
		bottom: 169px;
		right: 72px;
		z-index: 1;
		opacity: 0.0001;
		transform: translateX(100%);
		transition: 0.5s $cubic 0.6s;
		transition-property: opacity, transform;
		background: {
			image: url('../../../img/gamepad.png');
			size: contain;
			repeat: no-repeat;
		};
	}

	.decodeur {
		@media (min-width: 1661px) and (max-width: 1750px) {
			top: 540px;
			right: 430px;
		}
		@media (min-width: 1501px) and (max-width: 1660px) {
			top: 490px;
			right: 380px;
		}
	}

	.mobile-device {
		background-image: url('../../../img/tv-devices-group.png');
		
		@include applyAtRoot('body:lang(en-CA)') {
			background-image: url('../../../img/tv-devices-group--en.png');
		}
	}

	@media (min-width: 1661px) {
		.television {
			width: 671px;
			height: 468px;
			top: 84px;
		}
	
		.mobile {
			width: 115px;
			height: 181px;
			bottom: 132px;
			right: 756px;
		}
	}
	@media (max-width: 1660px) {
		.mobile {
			width: 103px;
			height: 162px;
			right: 678px;
			bottom: 200px;
		}
	
		.gamepad {
			width: 165px;
			height: 110px;
			bottom: 219px;
			right: 77px;
		}
	}
	@media (max-width: 1500px) {
		.mobile {
			bottom: 100px;
		}
	
		.gamepad {
			bottom: 119px;
		}
	}
	@media (max-width: 1500px) {
		.mobile {
			width: 68px;
			height: 107px;
			right: 628px;
			bottom: 175px;
		}
	
		.gamepad {
			width: 125px;
			height: 80px;
			bottom: 185px;
			right: 161px;
		}
	}
	@media (min-width: 1200px) and (max-width: 1350px) {
		background-position: top -140px right -17vw;
		
		.television, .decodeur, .mobile, .gamepad {
			margin-right: -4vw;
		}
	}
	@media (max-width: 1199px) {
		.mobile {
			width: 48px;
			height: 77px;
			right: 426px;
			bottom: 105px;
		}
	
		.gamepad {
			width: 85px;
			height: 50px;
			bottom: 118px;
			right: 111px;
		}
	}
	@media (max-width: 835px) {
		.mobile {
			right: 346px;
		}
	
		.gamepad {
			right: 30px;
		}
	}
	@media (max-width: 767px) {
		display: flex;
		flex-direction: column-reverse;
		background: {
			position: center top -300px;
			size: 100% 390px;
		};

		.gamepad {
			display: none;
		}

		.mobile-device {
			width: 169px;
			height: 106px;
			background-size: contain;
			margin: 0px auto 40px;
		}

		.FreeInternet {
			&__title {
				&:after {
					display: none;
				}
			}

			&__disclaimer {
				padding: 0 15px;
				max-width: 400px;
				margin: 20px auto;
			}
		}
	}
}