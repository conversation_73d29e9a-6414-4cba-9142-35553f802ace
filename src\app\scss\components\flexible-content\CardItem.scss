.CardItem {
    width: 337px;
    height: 219px;
    padding: 30px 0 0 0;
    text-align: center;
    box-shadow: 0 2px 6px 0 rgba(#000, 0.5);
    border-radius: 12px;
    background: $c-white;
    position: relative;
    &__link {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: inline-block;
        z-index: 1;
    }
    &__icon {
        fill: $c-primary;
        width: 60px;
        height: 60px;
        margin: 0 0 10px;
        @media (max-width: 767px) {
            width: 25px;
            height: 25px;
            margin: 0;
        }
    }
    &__title {
        font: 300 20px/30px $f-secondary;
        color: $c-primary;
        display: block;
        @media (max-width: 767px) {
            font-size: 12px;
            line-height: 10px;
        }
    }
    &__subTitle {
        font: 700 16px/30px $f-primary;
        display: block;
        text-transform: uppercase;
        @media (max-width: 767px) {
            font-size: 10px;
            line-height: 22px;
        }
    }
    @media (max-width: 1199px) {
        width: 291px;
    }
    @media (max-width: 991px) {
        width: 340px;
    }
    @media (max-width: 767px) {
        width: auto;
        height: 100px;
        padding: 15px 0 0 0;
    }
    &--custom {
        width: 327px;
        height: 190px;
        box-shadow: none;
        //transition: $t-primary;
        display: inline-block;
        overflow: visible;
        background: transparent;
        a {
            .CardItem__subTitle {
                color: $c-grey;
            }
        }
        &:hover {
            box-shadow: 0 2px 6px 0 rgba(#000, 0.5);
            background: $c-white;
        }
        .CardItem__icon {
            @media (max-width: 991px) {
                width: 50px;
                height: 50px;
            }
            @media (max-width: 767px) {
                width: 35px;
                height: 35px;
            }
        }
        .CardItem__title {
            @media (max-width: 991px) {
                font-size: 16px;
                line-height: 24px;
            }
            @media (max-width: 767px) {
                font-size: 14px;
                line-height: 17px;
            }
        }
        .CardItem__subTitle {
            @media (max-width: 991px) {
                font-size: 20px;
                line-height: 20px;
            }
            @media (max-width: 767px) {
                font-size: 12px;
                line-height: 22px;
            }
        }
        @media (max-width: 1199px) {
            width: 260px;
        }
        @media (max-width: 991px) {
            width: 190px;
            height: 120px;
            padding: 0;
        }
        @media (max-width: 767px) {
            width: auto;
            height: 100px;
            padding: 4px;
        }
    }
}

body.page-id-5921, body.page-id-7551 {
    .CardListing {
        .CardItem {
            height: 152px;
            padding: 40px 0 0 0;
            &__icon {
                display: none;
            }
        }
    }
}
