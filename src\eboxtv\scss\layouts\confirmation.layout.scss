.wrapper-white {
    padding: 100px 0;
    background: url('~@common/img/back-body-tv.jpg') center center repeat;
    width: 100%;
    display: inline-block;
    .icon {
        fill: $c-primary;
        margin-bottom: 35px;
    }
    h1 {
        font-family: 'BrandonGrotesque', sans-serif;
        text-transform: uppercase;
        font-weight: 900;
        @media (max-width: 991px) {
            font-size: 30px;
            line-height: 34px;
        }
        @media (max-width: 767px) {
            font-size: 26px;
            line-height: 32px;
        }

    }
    p {
        font-size: 20px;
        line-height: 30px;
        margin-bottom: 25px;
        font-family: 'BrandonGrotesque', sans-serif;
    }
    @media (max-width: 991px) {
        padding: 65px 0;
    }
}

.page-template-ebox-tv-confirmation {
    .wrapper-white {
        .icon {
            width:80px;
            height: 90px;
            @media (max-width: 991px) {
                margin-bottom: 10px;
            }

        }
        h1 {
            margin-bottom: 10px;
            @media (max-width: 767px) {
                margin-bottom: 18px;
            }
        }
        p  {
            font-family: $ebtv-f-secondary;
            font-size: 20px;
            line-height: 30px;
        }
        .general-btn {
            margin-top: 70px;
            @media (max-width: 991px) {
                margin-top: 35px;
            }
        }
    }
}

   // GENERIC BUTTON
   .general-btn {
    border-radius: 6px;
    border: 0;
    min-height: 60px;
    display: inline-block;
    color: $c-primary;
    font-weight: 700;
    padding: 0 30px;
    text-transform: uppercase;
    font-size: 16px!important;
    font-family: 'BrandonGrotesque', sans-serif;
    transition: all 0.2s $cubic;
    will-change: transform;
    position: relative;
    overflow: hidden;
    @media (max-width: 767px) {
        padding: 0 20px;
        line-height: 18px;
    }
    &:before {
        width: 100%;
        height: 100%;
        content: "";
        position: absolute;
        z-index: -1;
        left: 0;
        top: 0;
        background: $c-white;
        opacity: 1;
        transition: all 0.2s $cubic;
    }
    &:after {
        width: 100%;
        height: 100%;
        content: "";
        position: absolute;
        z-index: -1;
        left: 0;
        top: 0;
        transform: translateX(-100%);
        background: $c-grey;
        transition: all 0.2s $cubic;


    }
    &:hover,
    &:focus {
        background: $c-grey;
        color: $c-white;
        box-shadow: 5px 5px 5px rgba($c-grey, 0.5);
        &:after {
            transform: translateX(0);

        }
        &:before {
            transition-delay: 0.2s;
            opacity: 0;
        }

    }
    &--inv-red {
        color: $c-white;
        line-height: 60px;
        &:before {
            background: $c-primary;
            color: $c-white;
        }
    }
}