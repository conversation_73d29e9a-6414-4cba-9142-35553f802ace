export class MenuItemHasChildren {
    static selector: string = '[class*="--parent"]';
    private static readonly SUBOPEN_CLASS: string = 'sub-opened';

    private _isOpenedValue: boolean = false;

    $host: JQuery = jQuery(this.host);
    $hostLink: JQuery = jQuery('> a', this.$host);
    $mainParent: JQuery = jQuery('.Menu');

    get isOpened(): boolean {
        return this._isOpenedValue;
    }
    set isOpened(val: boolean) {
        if (val !== this._isOpenedValue) {
            this._isOpenedValue = val;

            if (val)
                this.open();
            else
                this.close();
        }
    }

    // ON INIT //
    constructor(private host: HTMLElement) {
        this.onInit();
    }

    public open(): void {
        this.$host.add(this.$mainParent).addClass(MenuItemHasChildren.SUBOPEN_CLASS);
    }

    public close(): void {
        this.$host.add(this.$mainParent).removeClass(MenuItemHasChildren.SUBOPEN_CLASS);
    }

    private onInit(): void {
        this.bindEvents();
    }

    private bindEvents(): void {
        this.$hostLink.on('click', this.onHostClick.bind(this));
    }

    private onHostClick(evt: MouseEvent): void {
        evt.preventDefault();
        this.toggleOpen();
    }

    private toggleOpen(): void {
        this.isOpened = !this.isOpened;
    }
}