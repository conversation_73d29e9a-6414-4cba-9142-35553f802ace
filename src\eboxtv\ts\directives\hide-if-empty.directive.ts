import { Directive } from '@core/directive.js';
import { TVOrderService } from '../../js/services';

export class HideIfEmptyDirective extends Directive {
	static selector: string = '[hide-if-empty]';

	public attrs: {
		channelsType: 'base'|'a-la-carte'|'others'|'premium'|'pre-selected';
	};

	private $tvOrder: TVOrderService = TVOrderService.getInstance();
	private isHiddenValue: boolean = false;

	public get isHidden(): boolean {
		return this.isHiddenValue;
	}
	public set isHidden(val: boolean) {
		if (val !== this.isHiddenValue) {
			this.isHiddenValue = val;

			if (val)
				this.hide();
			else
				this.show();
		}
	}

	constructor(host: HTMLElement) {
		super(host, [{
			name: 'hide-if-empty',
			as: 'channelsType',
			required: true,
			accept: ['base', 'a-la-carte', 'others', 'premium', 'pre-selected']
		}]);
		this.onInit();
	}

	private onInit(): void {
		this.subscribeToChange();
		this.onOrderChange();
	}

	private subscribeToChange(): void {
		this.$tvOrder.selectedChannelsChange$.subscribe(
			this.onOrderChange.bind(this)
		);
	}

	private onOrderChange(selectedChannels: any = this.$tvOrder.selectedChannels): void {
		const { channelsType } = this.attrs;
		let   checkedSet: Set<number>;

		if (channelsType !== 'pre-selected')
			checkedSet = selectedChannels[this.attrs.channelsType];
		else
			checkedSet = this.$tvOrder.selectedBundles;

		this.isHidden = checkedSet.size < 1;
	}

	public hide(): void {
		this.$host.hide();
	}

	public show(): void {
		this.$host.show();
	}
}

export interface ChannelType {
	base: Set<number>;
	['a-la-carte']: Set<number>;
	others: Set<number>;
	premium: Set<number>;
}