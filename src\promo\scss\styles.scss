// BOOTSTRAP IMPORTS
@import '~@bootstrap/variables';
@import '~@bootstrap/_mixins';
@import '~@bootstrap/normalize';
@import '~@bootstrap/grid';
@import '~@bootstrap/type';
@import '~@bootstrap/mixins/clearfix';
@import '~@bootstrap/responsive-utilities';
@import '~@bootstrap/responsive-embed';
@import '~@bootstrap/scaffolding';
@import '~@bootstrap/_modals.scss';

@import '~@app/scss/bases/fonts.base.scss';
@import '~@common/scss/helpers/_helpers.scss';

// HELPERS
@import './helpers/_variables';
@import './helpers/mixins';

// BASE
@import './base/base';
// @import './base/fonts';
@import './base/typo';
@import './base/doodles';

// COMPONENTS
@import './components/buttons';
@import './components/RedonneHero';
@import './components/RedonneImplication';
@import './components/RedonneDetails';
@import './components/RedonneBanner';
@import './components/RedonneAbout';

// LAYOUTS
@import './layouts/header.layout';
@import './layouts/footer.layout';
@import './layouts/hero.layout';
@import './layouts/deal.layout';
@import './layouts/contact.layout';
@import './layouts/cform.layout';
@import './layouts/callus.layout';
@import './layouts/infos.layout';
@import './layouts/infoblock.layout';