.HeaderTop {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 20;

    &__top {
        position: relative;
        background: $c-black;
        padding: 0;
        color: $c-white;
        z-index: 2;
    }
    &__bottom {
        position:relative;
        z-index:1;
        margin-top: -2px;
        background: #FFF;
        min-height: 60px;
        width: 100%;
        display: flex;
        align-items: center;
        box-shadow: 0px 5px 20px 0px rgba(0,0,0,0.2);
        svg {
            fill: $c-white;
            height: 36px;
            width: 132px;
        }
        .container {
            @media (max-width: 1200px) {
                width: 100%;
            }
            .NavPrinc__container {
                @media(max-width: 1200px){
                    padding-left: 0;
                    position: relative;
                    right: 15px;
                    justify-content: flex-end;
                }

                .NavPrinc {
                    @media(max-width: 1200px){
                        display: none;
                    }
                }
            }
            .Footer__menu-nav__link {
                white-space: nowrap;
                color: #000;
                margin-left: 20px;
                font-weight: bold;
                padding: 4px;
                @media (max-width: 580px) {
                    display: none;
                }
            }
        }
    }
}

.TopType {
    margin: 0;
    padding: 0;
    &__item {
        display: inline-block;
        padding: 8px 20px;
        position: relative;
        z-index: 3;
        @media (max-width: 767px) {
            padding: 8px;
        }

        &:after {
            width: 100%;
            height: 200px;
            left: 0;
            transform: translateY(-120%);
            top: -164px;
            content: "";
            background: url('../../../img/back-type.svg') center bottom no-repeat;
            position: absolute;
            z-index: 0;
            transition: $t-primary
        }
        &:hover {
            &:after {
                transform: translateY(0);
            }
        }
        &--active {
            &:after {
                transform: translateY(0);
            }
        }
    }
    &__link {
        color: $c-white;
        position: absolute;
        text-decoration: none;
        z-index:3;
        position: relative;
        height: 100%;
        display: inline-block;
        transition: $t-primary;
        font-size: 12px;
    }

}

.TopContact {
    margin: 0;
    padding: 5px 0;
    li {
        display: inline;
    }
    &--link {
        font: 700 12px/16px $f-primary;
        color: $c-white;
        &:after {
            content: "|";
            margin: 0 2px;
        }
        &:first-of-type {
            margin-right: 20px;
            &:after {
                display: none;
                
            }
        }
    }
    &--link-a {
        font: 700 12px/16px $f-primary;
        color: $c-white;
        padding: 4px;
    }
    &__item {
        display: inline-block;
        padding: 8px 0;
        position: relative;
        z-index: 3;
        &--last {
            padding-right: 0;
        }
        svg {
            width: 16px;
            height: 16px;
        }

    }
    &__link {
        font: 700 12px/16px $f-primary;
        //min-width: 97px;
        color: $c-white;
        display: inline-block;
        position: relative;
        text-align: center;
        transition: $t-primary;
        padding: 4px;
        svg {
            width: 16px;
            height: 16px;
            fill: $c-white;
            vertical-align: sub;
            fill: $c-light-grey;
            transition: $t-primary;
            display: none;
        }
        &:hover,
        &:focus {
            text-decoration: underline!important;
            svg {
                fill: $c-white;
            }
            color: $c-white;
            &:after {
                width: 97px;

            }
        }
        &:after {
            /*content: "";
            background: url('../../../img/hover.svg') center left no-repeat;
            background-size: 60px 13px;
            width: 60px;
            height: 13px;
            position: absolute;
            display: block;
            left: 0;
            top: 3px;
            width: 0;
            transition: width .2s ease*/
        }
        &--footer {
            svg {
                fill: $c-medium-grey;
            }
        }
    }
}
.TypeContainer {
    @media(max-width: 1200px){
        width: 100% !important;
    }
    @media (max-width: 767px) {
        padding: 0!important;
    }
}

.Basket {
    position: relative;
    svg {
        transition: $t-primary;
    }
    &__num {
        width: 15px;
        height: 15px;
        background: $c-primary;
        position: absolute;
        right: -5px;
        top: 0;
        z-index: 2;
        border-radius: 15px;
        color: $c-white;
        font: 700 10px/15px $f-primary;
        text-align: center;

    }
    &:hover {
        svg {
            fill: $c-medium-grey;
        }
    }
}

// bouton Créer mon forfait
.creerForfaitBtn{
        .ButtonEffect--qualification {
            padding: 8px 14px;
            min-width: 100%;
            border-radius: 6px;
            text-transform: uppercase;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
                &--hide {
                    display: none
                }
                @media(min-width: 1024px){
                    font-size: 10px !important;
                }     
        &:hover {
            color: $c-white;
            border: 2px solid #BE2323;
            background: #BE2323;
        }
        &:focus {
            background-color: #BE2323;
            color: #FFF;
        }
    }

}

.SubMenu__pourquoiebox {
    position: absolute;
    left: 40px;
    transform: translateX(-50%);
    top: 28px;
    opacity: 0;
    transition: all 0.2s $cubic;
    visibility: hidden;
    
    ul {
        margin: 0;
        padding: 0;
        width: 300px;
        background: $c-black;
        li {
            width: 100%;
            display: block;
            text-align: left;
            
            a {
                width: 100%;
                padding: 10px 20px;
                color: $c-white;
                display: inline-block;
                line-height: 35px;
                font: 700 16px/35px $f-primary;
                transition: all 0.2s $cubic;
                &:hover {
                    background: $c-medium-grey;
                }
                
                
            }

        }
    }
    @media (max-width: 450px) {
        left: 0;
        transform: translateX(0);
    }
}

.arrow-up {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 5px 5px 5px;
    border-color: transparent transparent $c-black transparent;
    margin: 0 auto;
  }

.WhyEboxMenu--opened {
    .SubMenu__pourquoiebox {
        opacity: 1;
        visibility: visible;
    }
}

.skipToMainContent {
    top: -100px;
    position: absolute;
    padding: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: $c-primary;
    color: #FFF!important;
    transition: all 0.2s $cubic;
    z-index: 150;
    border: 2px solid #FFF;
    font: 700 12px/1.4 "BrandonGrotesque", sans-serif !important;
    text-transform: uppercase;
    border-radius: 4px;
    &:focus {
        display: flex;
        top: 0;
    }
}