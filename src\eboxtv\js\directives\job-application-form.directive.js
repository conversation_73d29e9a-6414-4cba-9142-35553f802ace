import { Directive } from '@core/directive';
import { Tools } from '@common/ts/services/Tools.service';

/**
 * Simple Directive to keep count of the selected channel-ids stored
 * into TVOrderService.selectedChannels and display it in the UI.
 * 
 * IMPORTANT! See 'NOTE FOR DEPENDING DIRECTIVES' at begining of TVOrderService file.
 * {@link '../services/TVOrder.service.js'}
 */
export class JobApplicationDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[data-job-application]';


	form = document.getElementById("formCandidate");

	


	// ON INIT //
	constructor(host, childAttrs = []) {
		super(host, [

		]);
		this._init();
	}
	_init() {
		// const formtitle = document.getElementById("titleCandidate");
		const lang = Tools.lang;

		let uploadField = document.getElementById("cv");
		let cvPlaceholder = document.getElementById("cvPlaceholder");
		let cvError = document.getElementById("cvError");
		cvError.style.display = 'none';
		uploadField.onchange = function () {
		  if (this.files[0] && this.files[0].size > 10485760) {
			const error =
			  lang === "fr"
				? "Le maximum de 10Mo pour le cv à été dépassé!"
				: "The maximum file size of 10 Mo for résumé was exceeded!";
			cvError.innerHTML = error;
			cvError.style.display = 'inline';
			this.value = "";
		  } else {
			cvError.style.display = 'none';
			cvPlaceholder.innerHTML = this.files[0].name;
		  }

		};

		let uploadFields = document.getElementById("attachments[]");
		const attPlaceholder = document.getElementById("attPlaceholder");
		let attachmentsError = document.getElementById('attachmentsError');
		attachmentsError.style.display = 'none'
		uploadFields.onchange = function () {
		  let totalSize = 0;
	  
		  if (this.files) {
			for (let i = 0; i <= this.files.length - 1; i++) {
			  totalSize += this.files[i].size;
			}
		  }
	  
		  if (totalSize > 10485760) {
			const error =
			  lang === "fr"
				? "Le maximum de 10Mo pour les pièces jointes à été dépassé!"
				: "The maximum file size of 10 Mo for the attachments was exceeded!";
				attachmentsError.innerHTML = error;
				attachmentsError.style.display = 'inline';
			this.value = "";
		  } else {
			if (this.files.length === 1) {
			  attPlaceholder.innerHTML = this.files[0].name;
			} else {
			  const enText = "files selected";
			  const frText = "fichiers sélectionnées";
			  attachmentsError.style.display = 'none';

			  attPlaceholder.innerHTML = `${this.files.length} ${
				lang === "fr" ? frText : enText
			  }`;
			}
		  }
		};

		this.form.addEventListener("submit", function (event) {
			event.preventDefault();
			this.sendData();
		  }.bind(this));
	}

	async sendData() {
		const lang = Tools.lang;

		await this.getToken().then((tokenResponse) => {
		  const token = JSON.parse(tokenResponse);
		  if(this.form.elements['attachments[]'].files.length <= 0){
			this.form.elements['attachments[]'].disabled = true;
		  }
		  const data = new FormData(this.form);
		  this.form.elements['attachments[]'].disabled = false;


		  const url = "https://mwsserver.com/cvirtuose/Api/V1/Candidate";
		  this.makeRequest("POST", url, token, data)
			.then((resp) => {
				resp = JSON.parse(resp);
			  const responseFr =
				" Merci pour votre candidature!";
			  const responseEn =
				"Your application is sent. You will receive the details by email.";
			//   formtitle.innerHTML = lang === "fr" ? responseFr : responseEn;

			let errorLabel = document.getElementById('error');
			if(resp.success === true){
				this.form.remove();
				const responseFr = "Merci pour votre candidature!";
			  	const responseEn = "Thank you for your application!";
				let h3 = document.createElement('h3');
				h3.textContent = lang === "fr" ? responseFr : responseEn;
				document.getElementById('formContainer').append(h3);
			}
			else{
				const msg = lang == 'fr'? "Une erreur c'est produite : ": 'An error occured : ';
				errorLabel.innerHTML = msg + resp.errorDescription;
			}

			})
			.catch((error) => {
				const errorFr = "Une erreur s'est produite. Votre application n'a pas été envoyé. Veuillez rafraîchir la page et essayer de nouveau.";
			  	const errorEn = "An error occured. Your application was not sent. Please try to refresh the page and try again.";
			  	this.form.remove();
			  	let p = document.createElement('p');
			  	p.textContent = lang === "fr" ? errorFr : errorEn;
			  	document.getElementById('formContainer').append(p);
			});
		});
	  }

	  async getToken() {
		// Ce url est register dans functions.php
		const url = candidateData.candidateToken;
		let response = await this.makeRequest("POST", url);
		return response;
	  }


	  /**
 * Fonction pour faire des requete http vers un API
 * @param {*} method Methode http (ex: GET, POST)
 * @param {*} url Url pour la requete
 * @param {*} tokenObj Un token qui sera ajoute a la requete
 * @param {*} data Les donnees a envoyer vers l'url
 * @returns Reponse object | Error object
 */
	makeRequest(method, url, tokenObj = null, data = null) {
	return new Promise(function (resolve, reject) {
	  let xhr = new XMLHttpRequest();
	  xhr.open(method, url);
  
	  if (tokenObj) {
		xhr.setRequestHeader("Token", tokenObj.result);
	  }
  
	  xhr.onload = function () {
		if (this.status >= 200 && this.status < 300) {
		  resolve(xhr.response);
		} else {
		  reject({
			status: this.status,
			statusText: xhr.statusText,
		  });
		}
	  };
	  xhr.onerror = function () {
		reject({
		  status: this.status,
		  statusText: xhr.statusText,
		});
	  };
  
	  if (data) {
		xhr.send(data);
	  } else {
		xhr.send();
	  }
	});
  }
  
}