.header {
	&__wrapper {
		display: flex;
		width: 100%;
		max-width: $gridWidth + 60px;
		padding: 23px 30px;
		margin: 0 auto;
		justify-content: space-between;
		align-items: center;

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			max-width: $gridWidth + 14px;
			padding: 16px 7px;
		}
	}

	&__logo {
		width: 201px;
		height: 51px;

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			width: 152px;
			height: 38px;
		}
	}

	&__tel-link {
		color: $c-primary-dark;
		font: 900 25px $f-primary;
		transition: color 0.075s linear;
		&:hover {
			color: $c-white;
			text-decoration: none;
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			font-size: 22px;
		}
	}
}