/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";
import { PriceCalcService } from "@eboxtv/js/services/PriceCalc.service";
import { CookiesService } from '@common/ts/services/Cookies.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';
export class PaiementCommandeDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()
	_$tvService = TVOrderService.getInstance();
	_$priceCalc = PriceCalcService.getInstance();
	_$priceCalc = PriceCalcService.getInstance();

	//SELECTEUR DE LA DIRECTIVE
	static selector = '[data-commande-paiement]';

	sectionMonthly = document.getElementById('monthlyCharges');
	sectionOneTime = document.getElementById('oneTimeCharges');

	internetEquipement = {};
	phoneEquipement = {};

	internetPlan = {};
	phonePlan = {};

	order = {};

	billInfo = {};
	decodeur = {};
	discount = [];

	// VAR FOR TAXES
	province = document.forms['info'].elements['province'].value;
	TPS = 0.05;
	TVQ = 0.09975;
	TVH = 0.13;


	oneTimeTPS = document.getElementById('oneTimeTPS');
	oneTimeTVQ = document.getElementById('oneTimeTVQ');
	oneTimeTVH = document.getElementById('oneTimeTVH');


	monthlySousTotal = 0;
	oneTimeSousTotal = 0;

	monthlyTotal = 0;
	oneTimeTotal = 0;

	total = 0

	sousTotalOneTimeSection = document.getElementById('OneTimeSousTotal');
	sousTotalMonthlySection = document.getElementById('monthlySousTotal');

	totalSection = document.getElementById('total');

	//var promo
	promoDuo = {};
	promoInternet = {};
	promoInfo = {};

	totalCaa = 0;
	totalCaaInternet = 0;
	totalCaaPhone = 0;
	totalCaaTV = 0;
	promoCaaPercentage = {};

	isADSL = false;

	taxe_exempt = 0;

	partner_discount = '';
	partner_membership_number = '';

	checkout = {};
	cancel_url = '';
	success_url = '';
	instance = this;

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {

		this.partner_discount = LocalStorage.get('partner_discount') || '';
		this.partner_membership_number = LocalStorage.get('partner_membership_number') || '';

		if (this.partner_membership_number != '') {
			this.promoCaaPercentage = {
				internet: '0.10',
				phone: '0.10',
				tv: '0.10',
				blocEnregistrement: '0.10'
			}
		}else{
			this.promoCaaPercentage = {
				internet: '0.00',
				phone: '0.00',
				tv: '0.00',
				blocEnregistrement: '0.00'
			}
		}

		jQuery('.full-screen-spinner-background').hide();
		this._$OrderService.initOrderConfirmation();

		this.billInfo = this._$OrderService._retBillInfo();
		this.internetEquipement = this._$OrderService._retInternetEquipement();
		this.phoneEquipement = this._$OrderService._retPhoneEquipement();

		this.internetPlan = this._$OrderService._retInternetOrder();
		this.phonePlan = this._$OrderService._retPhoneOrder();

		this.order = this._$OrderService._retOrder();
		this.infoNuagique = this._$OrderService._reteboxTVOptions();

		//promo
		this.promoDuo = this._$OrderService.retPromoDuo()
		this.promoCode = this._$OrderService.retPromoCode()
		this.promoCrave = this._$OrderService.retPromoCrave()
		this.promoInfo = this._$OrderService.retPromoInfo()
		this.promoFibre = LocalStorage.get('promoFibre') || {};
		this.promoInternet = this._$OrderService.retInternetPromo()
		this.decodeur = this._$OrderService.retDecodeur();
		this.promoDecodeur = this._$OrderService.retPromoDecodeur();
		this.equipementInternetPromo = this._$OrderService.retEquipementInternetPromotion();

		this.instance = this;
		this.displayCharges();
	}

	displayCharges() {
		this.addFees();
		//console.log('avant internet');
		this.addInternetPlan();
		//console.log('avant phone');

		this.addTV();

		this.addPhonePlan();
		//console.log('avant tv');

		this.addDeposits();
		this.addEcoFrais();

		//console.log('avant discount');

		this.addDiscount();

		this.displaySousTotaux();
		this.calcTaxes();

		this.loadTicketMoneris();

		const url = window.location.href;
		if(url.includes('beta')){
			this.addprixEbox();
		}
	}

	loadTicketMoneris(){
		var lang = CoreTools.lang;
		var that = this;
		//console.log(lang);
		if(lang == 'fr'){
			lang = 'fr_FR';
		}else{
			lang = 'en_US';
		}

		jQuery.post(
			'/wp-content/themes/ebox2018/modules/components/validate_pay_order_moneris/controler_ajax.php',
			{
				payment_amount: this.billInfo.expected_amount_total,
				code_client: this.billInfo.code_client,
				lang: lang
			}, function(response){
				that.startCheckout(response, that);
			}, 'json'
		).error(function(response){
			console.log('error');
			console.log(response);
		});
	}

	startCheckout(response, instance){
		console.log('start checkout');
		console.log(response);

		this.checkout = new monerisCheckout();
		const url = window.location.href;
		if(url.includes('beta2') || url.includes('beta3')){
			var env = 'qa';
		} else {
			var env = 'prod';
		}
		console.log('env', env);
		this.checkout.setMode(env);

		this.checkout.setCheckoutDiv("monerisCheckout");
		
		this.checkout.setCallback("payment_complete", 
			function(response){
				var _$OrderService = OrderOnlineService.getInstance();
				var billInfo = _$OrderService._retBillInfo();
				console.log('payment receipt');
				console.log(response);
				var objResp = JSON.parse(response);
				if (objResp.response_code == '001') {
					jQuery.post(
						'/wp-content/themes/ebox2018/modules/components/validate_pay_order_moneris/controler_ajax.php',
						{
							action: 'payment_complete',
							response: response,
							order_id: billInfo.order_id,
							code_client: billInfo.code_client
						}, function(response){
							console.log('payment complete ebox/moneris');

							// si la transaction a échoué, on ne continue pas
							if(response.RESPONSE.status == 'ERROR'){
								console.log('error');
								console.log(response);
								return;
							}

							// si la transaction a été refusée, on ajoute un paramètre à l'url pour indiquer que la transaction a été refusée
							if(response.INFO.payment_response == 'declined'){
								/*var url = window.location.href;
								if(url.indexOf("?") > 0) {
								url = url.substring(0, url.indexOf("?"));
								} 
								url += "?declined=true";
								window.location.replace(url);*/
								instance.loadTicketMoneris();
								jQuery('#erreurPaiementMoneris').show();
								jQuery('html, body').animate({
									scrollTop: jQuery('#erreurPaiementMoneris').offset().top
								}, 1000);
								console.log('load ticket moneris');
							}else{
								// si la transaction a été acceptée, on redirige vers la page de succès
								let newUrl = '/';
								newUrl += CoreTools.lang == 'fr' ? '' : 'en/'
								LocalStorage.get('qualificationAddresse').pc.toLowerCase() == 'on' ? 'ontario/' : 'quebec/';
								newUrl += CoreTools.lang == 'fr' ? 'residentiel' : 'residential';
								newUrl += CoreTools.lang == 'fr' ? '/confirmation-de-commande' : '/order-confirmation';

								localStorage.clear();
								
								CookiesService.setCookies({
									name: 'OrderFinished',
									value: '1'
								});
								
								CookiesService.setCookies({
									name: 'PaymentResponseObject',
									value: JSON.stringify(response)
								});

								setTimeout(() => {
									window.location.href = newUrl;
								}, 1000);
							}
						}, 'json'
					);
			
				}
			}
		);

		this.checkout.setCallback("page_loaded", 
			function(response){
				console.log('page loaded');
				console.log(response);
			}
		);

		this.checkout.setCallback("cancel_transaction", 
			function(response){
				console.log('cancel transaction');
				console.log(response);
				document.location.href=cancel_url;
			}
		);

		this.checkout.setCallback("error_event", 
			function(response){
				console.log('error event');
				console.log(response);
			}
		);

		this.checkout.setCallback("payment_receipt", 
			function(response){
				var _$OrderService = OrderOnlineService.getInstance();
				var billInfo = _$OrderService._retBillInfo();
				console.log('payment receipt');
				console.log(response);
				var objResp = JSON.parse(response);
				if (objResp.response_code == '001') {
					jQuery.post(
						'/wp-content/themes/ebox2018/modules/components/validate_pay_order_moneris/controler_ajax.php',
						{
							action: 'payment_complete',
							response: response,
							order_id: billInfo.order_id,
							code_client: billInfo.code_client
						}, function(response){
							console.log('payment complete ebox/moneris');
							console.log(response);
						}, 'json'
					);
			
				}
			}
		);

		if(response.INFO.ticket){
			console.log(response.INFO.ticket);
			this.checkout.startCheckout(response.INFO.ticket);
		}
		
	}

	addFees() {
		if (parseFloat(this.order.installFee) > 0) {
			//this.addCharges(this.sectionOneTime, CoreTools.translate("Frais d'installation", "Installation costs"), this.order.installFee, '');
			this.addCharges(this.sectionOneTime, CoreTools.translate("Frais d'installation", "Installation costs"), "0.00", '');
		}
		if (parseFloat(this.order.shippingFee) > 0) {
			//this.addCharges(this.sectionOneTime, CoreTools.translate("Frais de livraison", "Shipping cost"), this.order.shippingFee, '');
			this.addCharges(this.sectionOneTime, CoreTools.translate("Frais de livraison", "Shipping cost"), "0.00", '');
		}

	}


	addInternetPlan() {
		this.isADSL = this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'? true : false;
		const qualif = CookiesService.getCookies('eboxQualification', true).details;
		const adslInfo = qualif.adslinfo ? qualif.adslinfo : false;



		this.addCharges(this.sectionMonthly, this.internetPlan.title, this.internetPlan.planPrice, '');
		this.totalCaaInternet += this.internetPlan.planPrice * this.promoCaaPercentage.internet;
		//if (this.billInfo.broadband_fund.broadband_fund_required) {
		//	this.addCharges(this.sectionMonthly, CoreTools.translate('Fonds pour la large bande', 'Broadband fund'), this.billInfo.broadband_fund.broadband_fund_qty);
		//}
		if(this.isADSL){
			adslInfo.forEach(plan => {
				if(plan.dsl_plan == this.internetPlan.forfait){
					var adslSelected = plan;
					if(this.internetPlan.downloadSpeed == 6){
						this.addCharges(this.sectionOneTime, CoreTools.translate("Frais d'activation de la ligne",'Line activation fee'), 0);
					
						if(adslSelected.dryloop_price){
							this.addCharges(this.sectionMonthly, CoreTools.translate('Frais de ligne sèche','Dryloop'), adslSelected.dryloop_price);

						}
						if(adslSelected.dryloop_rural){
							this.addCharges(this.sectionMonthly, CoreTools.translate('Frais de ligne sèche rurale','Rural dryloop'), adslSelected.dryloop_rural_price);
						}
					}
				}
			});
		}

		if(this.promoCode && this.promoCode.amount){
			/*if(this.promoCode.codePromo.toUpperCase() == 'FIB500'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Promotion Vendredi fou','Black Friday promotion'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			if(this.promoCode.codePromo.toUpperCase() == 'FIB1000'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Promotion Vendredi fou','Black Friday promotion'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}*/
			if(this.promoCode.codePromo.toUpperCase() == 'EBOXBW2024'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Promotion - 2e mois gratuit','Promotion - 2nd month free'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			if(this.promoCode.codePromo.toUpperCase() == 'EBOXBF24'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Évènement Vendredi fou - 2e mois gratuit','Black Friday Event - 2nd month free'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			if(this.promoCode.codePromo.toUpperCase() == 'PERKO5'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Offre Perkopolis','Perkopolis Offer'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}

			if(this.promoCode.codePromo.toUpperCase() == 'VENNGO5'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Offre VennGo','VennGo Offer'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}

			// BENNYQC devient CHEPROMOQC
			if(this.promoCode.codePromo.toUpperCase() == 'CHEPROMOQC'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais partenaire Benny','Benny partner discount'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}

			// BENNYON devient CHEPROMOON
			if(this.promoCode.codePromo.toUpperCase() == 'CHEPROMOON'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais partenaire Benny','Benny partner discount'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			// Code promo SNH25 - Ajout du texte de la promo dans la page de paiement
			if(this.promoCode.codePromo.toUpperCase() == 'SNH25'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Salon national de l\'habitation 2025','The national Home Show 2025'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			if(this.promoCode.codePromo.toUpperCase() == 'COLOC25'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('1 mois d\'Internet gratuit','1 month of free Internet'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			// CODE PROMO CAMPUS25
			if(this.promoCode.codePromo.toUpperCase() == 'CAMPUS25'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('1 mois d\'Internet gratuit','1 month of free Internet'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
			// CODE PROMO COMMICON
			if(this.promoCode.codePromo.toUpperCase() == 'CCMTL25'){
				this.addCharges(this.sectionMonthly, CoreTools.translate('Comiccon 1 mois d\'Internet gratuit','Comiccon 1 month of free Internet'),parseFloat(this.promoCode.amount).toFixed(2), 'discount');
			}
		}

		if(this.promoFibre && this.promoFibre.amount){
			this.addCharges(this.sectionMonthly, CoreTools.translate('Promo 1 mois gratuit - Superbowl','1 month free promo - Superbowl'),parseFloat(this.promoFibre.amount).toFixed(2), 'discount');
		}

		this.internetEquipement.forEach(equipement => {
			let parent = '';
			let price = '';
			let term = ''
			if(equipement.type == 'modem'){
				term = 'Modem';
			}
			else{
				term = CoreTools.translate('Routeur', 'Router');
			}
			if (equipement.buyRent == 'rent') {
				parent = this.sectionMonthly;
				price = equipement.rentPrice;
			}
			else if (equipement.buyRent == 'buy') {
				parent = this.sectionOneTime;
				price = equipement.buyPrice;
				if (equipement.sit) {
					price = price - equipement.rebate;
				}

			}

			if (equipement.buyRent != 'own') {
				this.addCharges(parent, term, price, '');
			}
		});

		this.addCharges(this.sectionMonthly, 'Modem', '0.00', '');
		this.addCharges(this.sectionMonthly, CoreTools.translate('Routeur', 'Router'), '0.00', '');
	}

	addPhonePlan() {
		if (this.phonePlan.type != 'none' && this.internetPlan.forfait) {
			var phoneType = this.phonePlan.type;
			if(this.phonePlan.type == 'Ligne standard'){
				phoneType = CoreTools.translate('Ligne standard', 'Stardard plan');
			}
			if(this.phonePlan.type == 'Interurbains'){
				phoneType = CoreTools.translate('Interurbains', 'Line with long distance');
			}
			
			this.addCharges(this.sectionMonthly, phoneType, this.phonePlan.planPrice, '');
			this.totalCaaPhone += this.phonePlan.planPrice * this.promoCaaPercentage.phone;

			if (this.billInfo.taxe_911.price) {
				this.addCharges(this.sectionMonthly, CoreTools.translate('Taxe 911', '911 taxe'), this.billInfo.taxe_911.price, '')
			}
			if(this.phoneEquipement && Object.keys(this.phoneEquipement).length !== 0){
				this.phoneEquipement.forEach(equipement => {
					let parent = '';
					let price = '';
					if (equipement.buyRent == 'rent') {
						parent = this.sectionMonthly;
						price = equipement.price;
					}
					else if (equipement.buyRent == 'buy') {
						parent = this.sectionOneTime;
						price = equipement.price;
						if (equipement.sit) {
							price = price;
						}
					}
	
					if (equipement.buyRent != 'own') {
						price = "0.00";
						this.addCharges(parent, CoreTools.translate('Adaptateur VOIP', 'VOIP Adapter'), price, '');
					}
				});
			}

			
		}

	}

	addTV() {
		const hasTV = CookiesService.getCookies('hasTV');
		if (hasTV == 'true') {
			let baseChannel = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvChannels.base : [];
			let tvRegion = baseChannel[0].area_name;
			const theme = this._$tvService.getSelectedBundle();
			if (theme) {
				if(theme.isFeatured == false ){
					const text = CoreTools.translate('Télévision : Service de base ('+ tvRegion+')','Television : Basic service ('+ tvRegion +')' );
					this.addCharges(this.sectionMonthly, text, this._$priceCalc.totals.channels['base'], '');
					//this.totalCaa += this._$priceCalc.totals.channels['base'] * this.promoCaaPercentage.tv;
				}
				this.addCharges(this.sectionMonthly, theme.name, theme.cost, '');
				this.totalCaaTV += theme.cost * this.promoCaaPercentage.tv;
			}
			else{
				const text = CoreTools.translate('Télévision : Service de base ('+ tvRegion+')','Television : Basic service ('+ tvRegion +')' );
				this.addCharges(this.sectionMonthly, text, this._$priceCalc.totals.channels['base'], '');
				this.totalCaaTV += this._$priceCalc.totals.channels['base'] * this.promoCaaPercentage.tv;
			}

			const listeChannels = this._$tvService.selectedChannels;
			let textStandAlone = '';
			
			let prixStandAlone = this._$priceCalc.totals.channels['a-la-carte'];
			const bundle = LocalStorage.get('selectedBundles');
			if(bundle == '113570' || bundle == '113581'){

				let totalSelectedChannelSize = this._$tvService.getTotalSelectedChannelSize('a-la-carte');
				if (totalSelectedChannelSize >= 10) {
					prixStandAlone -= 12.95; 
				}

			}

			let firstPrenium = true;
			for (const property in listeChannels) {
				if (property === 'a-la-carte') {
					textStandAlone += 'À la carte : ';
					listeChannels[property].forEach(element => {
						const channel = this._$tvService.getChannelById(element)
						textStandAlone += ' + ' + channel.post_title + '( '+channel.extraCost+' ) ';
					});
					if(this._$priceCalc.totals.channels['a-la-carte'] > 0){
						this.addCharges(this.sectionMonthly, textStandAlone, prixStandAlone, '');
						this.totalCaaTV += prixStandAlone * this.promoCaaPercentage.tv;
					}
				}
				if(this._$priceCalc.totals.channels['others'] > 0){
					if (property === 'others') {
						listeChannels[property].forEach(element => {
							const channel = this._$tvService.getChannelById(element)
					// 		textStandAlone += ' + ' + channel.post_title + ' ';
							this.addCharges(this.sectionMonthly, channel.post_title, channel.cost, '');
							this.totalCaaTV += channel.cost * this.promoCaaPercentage.tv;
						});
					}
				}

				if (property === 'premium') {
					let prix = 0;
					let text = 'Premium : ';
					listeChannels[property].forEach(element => {
						const channel = this._$tvService.getChannelById(element);
						if(!firstPrenium){
							text += ' + ';
						}
						firstPrenium = false;

						if(channel.post_title.includes('Crave')){
							text += 'Crave' + ' ';
						}
						else{
							text +=  channel.post_title + ' ';

						}

						prix += channel.cost;
					});

					if(prix > 0){
						this.addCharges(this.sectionMonthly, text, prix, '');
						this.totalCaaTV += prix * this.promoCaaPercentage.tv;
					}
				}
			}
			if (this.decodeur.qty) {
				let parent = '';
				if (this.decodeur.buyRent === 'rent') {
					parent = this.sectionMonthly;
				}
				else if (this.decodeur.buyRent === 'buy') {
					parent = this.sectionOneTime;
				}
				if(this.decodeur.buyRent !== 'own' && this.decodeur.qty){
					this.addCharges(parent, CoreTools.translate('Récepteurs ', 'Receivers ') + (this.decodeur.qty) + " " + CoreTools.translate('(1 récepteur inclus)', '(1 receiver included)'), parseFloat(this.decodeur.qty - 1) * parseFloat(this.decodeur.price), '');
				}
			}

			if (this.infoNuagique.cloudSpace && this.infoNuagique.cloudSpace.price > 0) {
				this.addCharges(this.sectionMonthly, this.infoNuagique.cloudSpace.title, this.infoNuagique.cloudSpace.price, '');
				this.totalCaaTV += this.infoNuagique.cloudSpace.price * this.promoCaaPercentage.blocEnregistrement;
			}
		}

	}

	addDeposits() {
		if (this.billInfo.credit_response && this.billInfo.credit_response.action === 'DEPOSIT_FEE_REQUIRED' && this.billInfo.credit_response.total_deposit_amount > 0) {
			this.addCharges(this.sectionOneTime, CoreTools.translate('Dépôt', 'Deposit'), this.billInfo.credit_response.total_deposit_amount, '');
			let amount = typeof(this.billInfo.credit_response.total_deposit_amount) == 'string' ? parseFloat(this.billInfo.credit_response.total_deposit_amount) : this.billInfo.credit_response.total_deposit_amount;
			this.taxe_exempt +=  amount;
		}
	}

	addEcoFrais(){
		let frais = 0;
		if (this.billInfo.eco_fees.net.qty > 0 && this.billInfo.eco_fees.net.qty) {
			frais += this.billInfo.eco_fees.net.price * this.billInfo.eco_fees.net.qty;
		}
		const hasTV = CookiesService.getCookies('hasTV');

		if(hasTV == 'true'){
			if (this.billInfo.eco_fees.tv.qty > 0 && this.billInfo.eco_fees.tv.qty) {
				frais += this.billInfo.eco_fees.tv.price * this.billInfo.eco_fees.tv.qty;
			}
		}

		if(frais > 0){
			this.addCharges(this.sectionOneTime, CoreTools.translate('Écofrais', 'Eco-Fee'), frais, '');
		}

	}

	addCharges(parent, text, price, state) {
		let add = true;
		let li = document.createElement('LI');
		let p = document.createElement('p');
		let span = document.createElement('SPAN');
		span.classList.add('service_price');
		p.textContent = text;
		span.textContent = price + '$';

		li.classList.add('service_detail-product');

		if (state === 'discount') {
			add = false;
			p.classList.add('service_detail-product--red');
			span.classList.add('service_price--red');
			if(CoreTools.lang == 'fr'){
				span.classList.add('frAlign');
				span.textContent = '-' + price + '$';
			}
			else{
				span.textContent = '-$' + price;
			}
		}
		else {
			add = true;
			li.classList.add('service_detail-product');
			if(CoreTools.lang == 'fr'){
				span.classList.add('frAlign');
				span.textContent = parseFloat(price).toFixed(2) + '$';
			}
			else{
				span.textContent = '$'+parseFloat(price).toFixed(2);
			}
		}
		li.append(p);
		li.append(span);
		parent.append(li);

		if (parent == this.sectionMonthly) {
			this.monthlySousTotal = this.calcSousTotaux(this.monthlySousTotal, price, add);
		}
		else {
			this.oneTimeSousTotal = this.calcSousTotaux(this.oneTimeSousTotal, price, add);
		}
	}

	addDiscount() {

		var isInternetCAAValid = CookiesService.getCookies('noCAAInternet');
		if(isInternetCAAValid == 'true'){
			isInternetCAAValid = false;
		}else{
			isInternetCAAValid = true;
		}

		if(this.totalCaaInternet > 0 && isInternetCAAValid){
			this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais CAA-Québec - Internet', 'CAA-Quebec discount - Internet'), this.totalCaaInternet.toFixed(2), 'discount');
		}

		if(this.totalCaaTV > 0){
			this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais CAA-Québec - Télévision', 'CAA-Quebec discount - Television'), this.totalCaaTV.toFixed(2), 'discount');
		}

		if(this.totalCaaPhone > 0){
			this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais CAA-Québec - Téléphonie', 'CAA-Quebec discount - Home phone'), this.totalCaaPhone.toFixed(2), 'discount');
		}

		if (this.promoDuo) {
			this.promoDuo.forEach(promo => {
				let section = '';
				if(parseFloat(promo.prix) > 0){
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					if(CoreTools.lang == 'fr'){
						this.addCharges(section, 'Rabais duo TV', promo.prix, 'discount');
					}else{
						this.addCharges(section, 'TV duo rebate', promo.prix, 'discount');
					}
				}
			});
		}

		/*if (this.promoInfo) {
			this.promoInfo.forEach(promo => {
				let section = '';
				if(parseFloat(promo.prix) > 0){
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}

			});
		}*/

		if (this.promoInternet) {
			this.promoInternet.forEach(promo => {
				if(parseFloat(promo.prix) > 0){
					let section = '';
					if (promo.recurrent === 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}
			});
		}
		const qualif = CookiesService.getCookies('eboxQualification', true).details;
		const adslInfo = qualif.adslinfo ? qualif.adslinfo : false;
		if(this.isADSL){
			adslInfo.forEach(plan => {
				if(plan.dsl_plan == this.internetPlan.forfait){
					var adslSelected = plan;
					if(adslSelected.credit_dryloop_price){
						//this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais de ligne sèche','Dryloop discount'), adslSelected.credit_dryloop_price, 'discount');
					}
					if(adslSelected.credit_dryloop_rural){
						//this.addCharges(this.sectionMonthly, CoreTools.translate('Rabais de ligne sèche','Rural Dryloop discount'), adslSelected.credit_dryloop_rural_price);
					}
				}
			});
		}

		if (this.equipementInternetPromo) {
			this.equipementInternetPromo.forEach(promo => {
				if(parseFloat(promo.prix) > 0){
					let section = '';
					if (promo.recurrent == 'oui') {
						section = this.sectionMonthly;
					}
					else {
						section = this.sectionOneTime;
					}
					this.addCharges(section, promo.titre_panier, promo.prix, 'discount');
				}
			});
		}
		if(this.billInfo.promo_free_month_tv.promo_qty){

			var monthFree = this.billInfo.promo_free_month_tv.promo_qty;
			const bundle = LocalStorage.get('selectedBundles');
			if(this.promoCrave && this.promoCrave.amount){
				monthFree = parseFloat(monthFree) - parseFloat(this.promoCrave.amount);
			}

			if(this.totalCaaTV > 0){
				monthFree = parseFloat(monthFree) - parseFloat(this.totalCaaTV);
			}

			this.addCharges(this.sectionOneTime, CoreTools.translate('Un mois de télévision gratuite', 'One month of free television'), monthFree.toFixed(2), 'discount');

		}

		if(this.promoCrave && this.promoCrave.amount){
			this.addCharges(this.sectionOneTime, CoreTools.translate('Un mois de Crave offert (durée de la promo 3 mois)', '1 month of Crave on us (duration of the promotion 3 months)'),parseFloat(this.promoCrave.amount).toFixed(2), 'discount');
		}

		if(this.promoDecodeur.length>0){
			this.promoDecodeur.forEach(promo => {
				//this.addCharges(this.sectionMonthly, promo.titre_panier, promo.prix, 'discount')
			});
		}

		if (this.phonePlan.type != 'none' && this.internetPlan.forfait) {
			//this.discount.push({ text: CoreTools.translate('Rabais ligne téléphonique', 'Phone line discount'), price: '5.00', monthly: true })
		}

		this.discount.forEach(element => {
			let parent = ''
			element.monthly ? parent = this.sectionMonthly : this.sectionOneTime;
			this.addCharges(parent, element.text, element.price, 'discount');
		});

	}

	calcSousTotaux(total, amount, add) {
		if (add) {
			total = total + parseFloat(amount);
		}
		else {
			total = total - parseFloat(amount);
		}
		total = Math.round((total + Number.EPSILON) * 100) / 100;
		return total;
	}


	displaySousTotaux() {
		if(CoreTools.lang == 'fr'){
			this.sousTotalOneTimeSection.parentElement.classList.add('frSpacing');
		}
		this.sousTotalMonthlySection.textContent =  CoreTools.translate(this.monthlySousTotal.toFixed(2)+'$', '$'+this.monthlySousTotal.toFixed(2));
		this.oneTimeSousTotal = this.oneTimeSousTotal + this.monthlySousTotal;
		this.sousTotalOneTimeSection.textContent = CoreTools.translate(this.oneTimeSousTotal.toFixed(2)+'$', '$'+this.oneTimeSousTotal.toFixed(2));
	}



	calcTaxes() {
		if (this.province == 'on') {
			const taxTVHMOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TVH);
			this.displayTotals(this.oneTimeTVH, CoreTools.translate(taxTVHMOneTime.toFixed(2)+'$', '$'+taxTVHMOneTime.toFixed(2)));
			this.total = this.roundAmounts(this.oneTimeSousTotal + taxTVHMOneTime);

		}
		else if (this.province == 'qc') {
			//CALCULER LES TAXES PAR MOIS ET TOTALES
			const taxTPSOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TPS);
			const taxTVQOneTime = this.roundAmounts((this.oneTimeSousTotal - this.taxe_exempt) * this.TVQ);

			// AFFICHER LES TAXES PAS MOIS ET TOTALES
			this.displayTotals(this.oneTimeTPS, CoreTools.translate(taxTPSOneTime.toFixed(2)+'$','$'+taxTPSOneTime.toFixed(2)));
			this.displayTotals(this.oneTimeTVQ, CoreTools.translate(taxTVQOneTime.toFixed(2)+'$','$'+taxTVQOneTime.toFixed(2)));


			//CALCULER ET AFFICHER LES TOTAUX APRES TAXES

			this.total = this.roundAmounts(this.oneTimeSousTotal + taxTVQOneTime + taxTPSOneTime);

		}


		this.displayTotals(this.totalSection, CoreTools.translate(this.total.toFixed(2)+'$','$'+this.total.toFixed(2)));
		localStorage.setItem('total', this.total);
	}


	displayTotals(section, amount) {
		if(CoreTools.lang == 'fr'){
			section.parentElement.classList.add('frSpacing');
		}
		section.textContent = amount;
	}

	roundAmounts(amount) {
		let res = Math.round((amount + Number.EPSILON) * 100) / 100;
		return res;
	}

	addprixEbox(){
		const sectionEboxPrice = document.getElementById('eboxPrice');
		sectionEboxPrice.textContent = this.billInfo.expected_amount_total;
	}


}
