.chosen-channel {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 6px;
    cursor: pointer;
    will-change: opacity;
    background: {
        position: center;
        repeat: no-repeat;
        size: 50%;
        color: $c-white;
    }
    z-index: 1;
    &:hover {
        .chosen-channel__delete-button {
            opacity: 1;
        }
    }

    &--disabled {
        cursor: default !important;
    }

    &--chosen {
        border: 4px solid #BE2323;
    }

    &--has-extra {
        background-position: center bottom 18px;
    }

    &__delete-button {
        position: absolute;
        background: radial-gradient(rgba(#3B3B3B, 0.8), rgba(#222222, 0.85));
        border: none;
        appearance: none;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 0;
        border-radius: 5px;
        opacity: 0;
        transition: all 0.1s linear;
        z-index: 1;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            width: 17px;
            height: 17px;
            left: auto;
            right: 0;
            opacity: 1;
            border-radius: 0px 8px 0 5px;
            background: #999;

            .chosen-channel__icon {
                width: 10px;
                height: 10px;
            }
        }
    }

    &__icon {
        position: absolute;
        display: block;
        width: 35%;
        height: 35%;
        fill: $c-white;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    &__cost {
        text-align: center;
        margin-top: 5px;
    }

    &__print-title {
        display: none;
        margin-bottom: 0 !important;

        @media print {
            display: block;
        }
    }

    &__extra-container {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        bottom: 0;
        left: 0;
        width: 100%;
        background: $c-primary;
        border-radius: 0 0 5px 5px;
        color: #FFF;
        font: 400 10px/1.2 $ebtv-f-primary;
        padding: 3px;
    }

    &__extra-icon {
        display: inline-block;
        position: relative;
        width: 7px;
        height: 7px;
        fill: currentColor;
        bottom: 1px;
        margin-right: 1px;
    }
}