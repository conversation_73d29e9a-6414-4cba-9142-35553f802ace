.page-template-page-routeurs {
    .SinglePrice {
        width: 100%;
        //min-height: 325px;
        height: 325px;
        background: url('~@app/img/red-pattern.png') left top repeat;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        @media (max-width: 1199px) {
            margin-top: 30px;
        }
        .Listing__recap {
            border-top: 0;
            padding-top: 0;
            
            &-wrapper {
                text-align: center;
            }
        }
        .Listing__apartir {
            font:400 16px/22px $f-primary;
            color: $c-white;
            margin-left: 6px;
            margin-bottom: 0;
            text-align: center;
        }
        .Listing__prix {
            font:900 100px/100px $f-primary;
            color: $c-white;
            position: relative;
            margin-left: 0;
            sup {
                font-size:35px;
                vertical-align: super;
                top: 3px;
            }
            span {
                position: absolute;
                right: 3px;
                top: 50px;
                font: 400 12px $f-secondary;
            }
            &--promo {
                font:900 22px $f-primary;
                margin-right: 10px;
                color: $c-white;
                position: relative;
                display: none;
                sup {
                    font-size: 12px;
                }
                &:after {
                    content: "";
                    width: 100%;
                    height: 1px;
                    background: $c-light-grey;
                    opacity: 0.7;
                    position: absolute;
                    left: 0;
                    top: 14px;
                }
            }
        }
    }
}