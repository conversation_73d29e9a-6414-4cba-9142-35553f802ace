/* global jQuery */
import { Directive } from '@core/directive';
import { LocalStorage } from '@common/ts/services/LocalStorage.ts';

export class PopupVitesseDirective extends Directive {

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[popup-vitesse]';

    internetPlan = {};

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        this.internetPlan = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : {};
        var minVitesse = 25;
        if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){
            minVitesse = 50;
            if(this.internetPlan.technology == 'v-cable'){
                minVitesse = 30;
            }
            if( this.internetPlan.technology == 'c-cable'){
                minVitesse = 15;
            }
        }

        document.getElementsByClassName("speedDownload")[0].innerText = minVitesse;

        if(parseInt(this.internetPlan.downloadSpeed) < minVitesse){
            this.host.classList.remove("hidden");
            document.getElementsByClassName("tv-container")[0].classList.add('hidden');
            document.getElementsByClassName("tv-container")[0].remove();
        }else{
            document.getElementsByClassName("tv-container")[0].classList.remove('hidden');
        }

        // Écoute simultané
        /*var simultane = 4;
        if(this.internetPlan.downloadSpeed == 15){
            simultane = 4;
        }else if(this.internetPlan.downloadSpeed >= 30 && this.internetPlan.downloadSpeed <= 50){
            simultane = 6;
        }else{
            simultane = 8;
        }
        document.getElementById('simultane-ecoute').innerHTML = simultane;*/

        // InfoNuagique
        var infoNuagique = document.getElementsByClassName('infonuagique_choix');
        var is_none_available = false;
        if(this.internetPlan.cableTech == 'DSL'){


        }

        Array.from(infoNuagique).forEach((el) => {
            // el.setAttribute('data-speed', '10');
            var speed = el.getAttribute('data-speed');

            if(this.internetPlan.cableTech == 'DSL'){
                speed = el.getAttribute('data-speedADSL');
            }
            if(speed != "" && parseInt(this.internetPlan.downloadSpeed) < parseInt(speed)){
                is_none_available = true;
                var none_available_ul = document.getElementById('nuagique-none-available');
                el.querySelector('.cloud-selector__fake-radio').remove();
                none_available_ul.innerHTML += el.innerHTML;
                el.remove();
                if(this.internetPlan.cableTech == 'DSL'){
                document.querySelectorAll('.InfoNuagique100')[0].innerHTML = '25';
                document.querySelectorAll('.InfoNuagique100')[1].innerHTML = '25';

                document.querySelectorAll('.InfoNuagique200')[0].innerHTML = '25';
                document.querySelectorAll('.InfoNuagique200')[1].innerHTML = '25';
                document.querySelectorAll('.InfoNuagique300')[0].innerHTML = '50';
                document.querySelectorAll('.InfoNuagique300')[1].innerHTML = '50';
                }
            }

        });

        if(!is_none_available){
            document.getElementById('nuagique-none-available').remove();
            document.getElementById('none-available-text').remove();
        }
        
	}

}


