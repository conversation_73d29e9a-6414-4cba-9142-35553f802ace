import { LocalStorage } from '@common/ts/services/LocalStorage.ts';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { Tools } from '@common/ts/services/Tools.service.ts';
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";
import { PromotionsService } from "@eboxtv/js/services/promotions.service";
import { PriceCalcService } from "@eboxtv/js/services/PriceCalc.service";
import { CookiesService } from '@common/ts/services/Cookies.service';
import { data } from 'jquery';
/**
 * ---> NOTE FOR DEPENDING DIRECTIVES: Any Directives made to react to OrderOnlineService.selectedChannels
 * changes via the OrderOnlineService.selectedChannelsChange$ BehaviorSubject MUST BE treated as passive observers
 * that will only reflect OrderOnlineService.selectedChannels's value. If the have to change it, the MUST
 * do it via OrderOnlineService public methods which will automatically update the OrderOnlineService.selectedChannelsChange$
 * BehaviorSubject after which the observer directives will update.
 */
export class OrderOnlineService {
	//#region Singleton creation and distribution
	/**
	 * @private
	 */
	static _instance;
	/**
	 * @public
	 * @return {OrderOnlineService}
	*/
	static getInstance() {
		if (OrderOnlineService._instance === undefined)
			OrderOnlineService._instance = new OrderOnlineService();

		return OrderOnlineService._instance;
	}
	$tvOrderService = TVOrderService.getInstance();
	$promotionsService = PromotionsService.getInstance();

	$priceCalcService = PriceCalcService.getInstance();

	//#endregion

	// static $stepsManager = PriceStepsManager.getInstance();

	//#region Static readonly values
	// static STEP_MIN_VALUE = OrderOnlineService.$stepsManager.STEP_MIN_VALUE;
	// static STEP_SIZE = OrderOnlineService.$stepsManager.STEP_SIZE;

	charges = new BehaviorSubject('');
	chargesValues = {
		internetPlan: {},
		internetEquipement: {},
		phoneEquipement: {},
		phonePlan: {},
		order: {},
		promoDuo : {},
		promoInternet : {},
		promoInfo : {},
		promoInternetEquipement : {},
		promoCode : {},
		promoCrave : {},
		promoCaa : {}
	};

	static IS_DEV = false;
	static SKIP_RATIO_VALIDATION = true;

	personalData = {};

	infoClient = {};
	credit_request = {};

	formInfoPerso = [];

	availabilities = {};

	personalInfoToggle = {};

	order = {};

	shipping = {};

	internetPlan = {};
	internetEquipement = {};

	phonePlan = {};
	phoneEquipement = {};

	billInfo = {};

	decodeurInfo = {};

	stepValidation = {};

	qualificationAddresse = {};
	qualificationAppartement = {};
	//#endregion

	lang = '';


	cartMonthlyPrice = new BehaviorSubject(0)
	cartUniquePrice = new BehaviorSubject(0);
	/**
	 * @private
	 * @type {Set}
	 * Private value for public selectedChannels property.
	 */

	/**
	 * @public
	 *
	 */
	initFormInformationPersonnel() {
		this.lang = Tools.lang;
		this.infoClient = LocalStorage.get('infoClient') || {};
		this.credit_request = LocalStorage.get('credit_request') || {};
		this.shipping = LocalStorage.get('shipping') || {};
		this.personalInfoToggle = LocalStorage.get('personalInfoToggle') || {};
		this.order = LocalStorage.get('order') || {};
		this.stepValidation = LocalStorage.get('stepValidation') || {};
		this.initDecodeur()
		if (LocalStorage.get('availabilities')) {
			this.availabilities = LocalStorage.get('availabilities');
		}
		if (!this.availabilities.hasOwnProperty('availability_days')) {
			this.availabilities.availability_days = {};
		}

		this.internetEquipement = LocalStorage.get('internetEquipement') || {};

		this.phoneEquipement = LocalStorage.get('phoneEquipement') || {};

		this.qualificationAddresse = LocalStorage.get('qualificationAddresse') || {};
		this.qualificationAppartement = LocalStorage.get('qualificationAppartement') || {};
		this.billInfo = LocalStorage.get('credit_response') || {};
		if (LocalStorage.get('stepValidation')) {
			this.stepValidation = LocalStorage.get('stepValidation');
		}
		this.initDecodeur();
	}

	_toArray(obj) {
		var array = [];
		for (var i = obj.length >>> 0; i--;) {
			array[i] = obj[i];
		}
		return array;
	}

	setForm(form) {
		this.formInfoPerso = form;
	}

	setStepValidation(key, isValid) {
		this.stepValidation = LocalStorage.get('stepValidation') || {};
		this.stepValidation[key] = isValid;
		LocalStorage.set('stepValidation', JSON.stringify(this.stepValidation));
	}

	getStepValidation() {
		this.stepValidation = LocalStorage.get('stepValidation') || {};
		return this.stepValidation;
	}

	_personalInfoFormStorage(form) {
		LocalStorage.delete('availabilities');
		form = this._toArray(form.elements);
		form.forEach(input => {
			this._infoFormDataBuilder(input);
		});
	}

	_infoFormDataBuilder(input) {
		if (input.name.includes('sunday') || input.name.includes('monday') || input.name.includes('tuesday') || input.name.includes('wednesday') || input.name.includes('thursday') || input.name.includes('friday') || input.name.includes('saturday')) {
			let [time, day] = input.name.split('_');
			if (!this.availabilities.availability_days.hasOwnProperty(time)) {
				this.availabilities.availability_days[time] = {};
			}
			this.availabilities.availability_days[time][day] = input.checked;
		}
		else {
			switch (input.name) {
				case 'userFirstName':
					this.infoClient.firstname = input.value;
					break;
				case 'userLastName':
					this.infoClient.lastname = input.value;
					break;
				case 'userMail':
					this.infoClient.email = input.value;
					break;
				case 'userPhone':
					this.infoClient.phone = input.value;
					break;
				case 'BirthDay':
				case 'BirthMonth':
				case 'BirthYear':
					const day = this.formInfoPerso.elements['BirthDay'].value;
					const month = this.formInfoPerso.elements['BirthMonth'].value;
					const year = this.formInfoPerso.elements['BirthYear'].value;
					const birthDay = day + '-' + month + '-' + year;
					this.infoClient.birthdate = birthDay;
					break;
				case 'internetProvider':
					this.infoClient.actual_provider = input.value;
					break;
				case 'entenduEbox':
					this.infoClient.entendu_ebox = input.value;
					break;
				case 'creditConsent':
					this.credit_request.client_consent = this.formInfoPerso.elements['creditConsent'].value;
					break;
				case 'oldLivingAddress':
					this.credit_request.oldLivingAddress = this.formInfoPerso.elements['oldLivingAddress'].value;
					// pour la selection de l'adresse, il manque des details car la selection de l'adresse demanderait plusieurs champs
					break;
				case 'haveInternet':
					this.infoClient.haveInternet = this.formInfoPerso.elements['haveInternet'].value;
					break;
				case 'internetProvider':
					this.infoClient.actual_provider = input.value;
					break;
				case 'isEtages':
					this.infoClient.isEtages = this.formInfoPerso.elements['isEtages'].value;
					break;
				case 'entenduEbox':
					this.infoClient.entendu_ebox = input.value;
					break;
				case 'safetyPin':
					this.infoClient.safetyPin = this.formInfoPerso.elements['safetyPin'].value;
					break;
				case 'oldTech':
					this.infoClient.oldTech = this.formInfoPerso.elements['oldTech'].value;
					break;
				case 'delivery':
					this.shipping.type = this.formInfoPerso.elements['delivery'].value;
					break;
				case 'sameDeliveryAddress':
					this.shipping.sameShippingAddress = this.formInfoPerso.elements['sameDeliveryAddress'].value;
					break;
				case 'deliveryAppartement':
					this.shipping.deliveryAppartement = this.formInfoPerso.elements['deliveryAppartement'].value;
					break;
				case 'appNumber':
					this.shipping.appNumber = input.value;
					this.infoClient.appartment = input.value;
					break;
				case 'appNumberDelivery':
					this.shipping.appNumberDelivery = input.value;
					break;
				case 'dateFrom':
					this.availabilities.availability_from = input.value;
					break;
				case 'consent':
					// Ne pas confondre avec creditConsent!! :)
					this.availabilities.consent = input.checked;
					break;
				case 'sameAddress':
					this.personalInfoToggle.sameAddress = this.formInfoPerso.elements['sameAddress'].value;
					break;
				case 'isAppartement':
					this.personalInfoToggle.isAppartement = this.formInfoPerso.elements['isAppartement'].value;
					break;
				case 'friendCode':
					this.infoClient.friendCode = input.value;
					break;
				case 'availability_template_id':
					this.availabilities.availability_template_id = input.value;
					break;
				// Temporaire:
				case 'riskScore':
					this.credit_request.riskScore = input.value;
					break;
			}
		}

		LocalStorage.set('infoClient', JSON.stringify(this.infoClient));
		LocalStorage.set('availabilities', JSON.stringify(this.availabilities));
		LocalStorage.set('personalInfoToggle', JSON.stringify(this.personalInfoToggle));
		LocalStorage.set('shipping', JSON.stringify(this.shipping));
		LocalStorage.set('credit_request', JSON.stringify(this.credit_request));

		var actualTypeOffre = CookiesService.getCookies('typeOffre');
		if( actualTypeOffre != 'numerique'){
			this.$promotionsService.getInfoPromotion(this.shipping.type, this.infoClient.haveInternet, this.infoClient.oldTech, this.infoClient.actual_provider);
		}
		
		this.initCart();
	}

	_set_addresses(data) {
		this.credit_request.previous_address = data;
		LocalStorage.set('credit_request', JSON.stringify(this.credit_request));

	}
	_set_addressesShipping(data) {
		this.shipping.address = data;
		LocalStorage.set('shipping', JSON.stringify(this.shipping));

	}

	setShippingFee(fee) {
		this.order.shippingFee = fee;
		LocalStorage.set('order', JSON.stringify(this.order));
		this.chargesValues.order = this.order;
		this.charges.next(this.chargesValues);
	}

	setInstallFee(fee) {
		this.order.installFee = fee;
		LocalStorage.set('order', JSON.stringify(this.order));
		this.chargesValues.order = this.order;
		this.charges.next(this.chargesValues);
	}

	setCreditRequest(data) {
		this.billInfo = data;
		LocalStorage.set('credit_response', JSON.stringify(this.billInfo));
	}

	_retSavedInfo(){
		return LocalStorage.get('infoClient') || {}
		
	}

	_retForfaitOption(){
		return LocalStorage.get('forfaitOption') || {}
	}

	_retInfoClient() {
		let data = {};
		if (this.infoClient.birthdate) {
			const birthDay = this.infoClient.birthdate.split('-');
			data.BirthDay = birthDay[0];
			data.BirthMonth = birthDay[1];
			data.BirthYear = birthDay[2];
		}
		if (this.infoClient.firstname) {
			data.userFirstName = this.infoClient.firstname;
		}
		if (this.infoClient.lastname) {
			data.userLastName = this.infoClient.lastname;
		}
		if (this.infoClient.email) {
			data.userMail = this.infoClient.email;
		}
		if (this.infoClient.phone) {
			data.userPhone = this.infoClient.phone;
		}
		if (this.infoClient.haveInternet) {
			data.haveInternet = this.infoClient.haveInternet;
		}
		if (this.infoClient.actual_provider) {
			data.internetProvider = this.infoClient.actual_provider;
		}
		if (this.infoClient.isEtages) {
			data.isEtages = this.infoClient.isEtages;
		}
		if (this.infoClient.entendu_ebox) {
			data.entenduEbox = this.infoClient.entendu_ebox;
		}
		if (this.infoClient.safetyPin) {
			data.safetyPin = this.infoClient.safetyPin;
		}
		if (this.infoClient.oldTech) {
			data.oldTech = this.infoClient.oldTech;
		}

		return data;
	}

	_retAvailibilities() {
		let data = {};
		let availability_days = {};
		let isEmpty = true;
		let counter = 0;

		if (!jQuery.isEmptyObject(this.availabilities.availability_days)) {
			for (const [time, value] of Object.entries(this.availabilities.availability_days)) {
				if (!jQuery.isEmptyObject(value)) {
					for (const [day, value] of Object.entries(this.availabilities.availability_days[time])) {
						availability_days[time + '_' + day] = value;
						if (value) {
							counter++;
							if(counter > 2){
								isEmpty = false;
							}
						}
					}
				}
			}
		}

		data.dateFrom = this.availabilities.availability_from || '';
		data.consent = this.availabilities.consent || false;
		data.availability_days = availability_days;
		data.isEmpty = isEmpty;

		return data;
	}

	_retShippingInfo() {
		let data = {}
		if (this.shipping.type) {
			data.delivery = this.shipping.type;
		}
		if (this.shipping.sameShippingAddress) {
			data.sameDeliveryAddress = this.shipping.sameShippingAddress;
		}
		if (this.shipping.address) {
			data.addDelivery = this.shipping.address;
		}
		if (this.shipping.deliveryAppartement) {
			data.deliveryAppartement = this.shipping.deliveryAppartement;
		}
		if (this.shipping.appNumberDelivery) {
			data.appNumberDelivery = this.shipping.appNumberDelivery;
		}
		if (this.shipping.appNumber) {
			data.appNumber = this.shipping.appNumber;
		}
		return data
	}
	_retPersonalInfoToggle() {
		let data = {};
		if (this.personalInfoToggle.sameAddress) {
			data.sameAddress = this.personalInfoToggle.sameAddress;
		}
		if (this.personalInfoToggle.isAppartement) {
			data.isAppartement = this.personalInfoToggle.isAppartement;
		}
		return data;
	}



	_retCreditInfo() {
		let data = {}
		data.creditConsent = this.mustRentEquipement() && this.credit_request.client_consent;
		data.lastAddress = this.credit_request.previous_address || {};
		data.oldLivingAddress = this.credit_request.oldLivingAddress;

		return data;
	}

	_retBillInfo() {
		let data = LocalStorage.get('credit_response');
		return data;
	}

	sendForm() {
		this.initInternetPlan();
		this.initPhonePlan();

		this.partner_discount = LocalStorage.get('partner_discount') || '';
		this.partner_membership_number = LocalStorage.get('partner_membership_number') || '';
		
		if(this.partner_discount && this.partner_membership_number){
			this.infoClient.partner_discount = this.partner_discount;
			this.infoClient.partner_membership_number = this.partner_membership_number;
		}

		return new Promise((resolve, reject) => {
			const data = {
				lang: this.lang,
				infoClient: this.infoClient,
				credit_request: this.credit_request,
				availabilities: this.availabilities,
				shipping: this.shipping,
				personalInfoToggle: this.personalInfoToggle,
				internetPlan: this.internetPlan,
				internetEquipement: this.internetEquipement,
				phoneplan: this.phonePlan,
				phoneEquipement: this.phoneEquipement,
				addressQualification: this.qualificationAddresse,
				creditResponse: this.billInfo,
				tvInfo : this.getSkuTv(),
				decodeur : this.decodeurInfo,
				internetPromotion : LocalStorage.get('InternetPromotion') || {},
				duoPromotion : LocalStorage.get('DuoPromotion') || {},
				infoPromotion : LocalStorage.get('InfoPromotion') || {},
				promoCode : LocalStorage.get('codePromo') || {},
				promoCrave : LocalStorage.get('promoCrave') || {},
				promoFibre : LocalStorage.get('promoFibre') || {},
				equipementInternetPromotion : this.retEquipementInternetPromotion(),
				infoNuagique : this._reteboxTVOptions(),
				decodeurPromo : this.retPromoDecodeur(),
			}

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validate_order' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});

	}

	sendFormNumerique() {
		this.initInternetPlan();
		this.initPhonePlan();
		return new Promise((resolve, reject) => {
			const data = {
				lang: this.lang,
				infoClient: this.infoClient,
				credit_request: this.credit_request,
				availabilities: this.availabilities,
				shipping: this.shipping,
				personalInfoToggle: this.personalInfoToggle,
				internetPlan: this.internetPlan,
				internetEquipement: this.internetEquipement,
				phoneplan: this.phonePlan,
				phoneEquipement: this.phoneEquipement,
				addressQualification: this.qualificationAddresse,
				creditResponse: this.billInfo,
				tvInfo : this.getSkuTv(),
				decodeur : this.decodeurInfo,
				internetPromotion : {},
				duoPromotion : {},
				infoPromotion : {},
				equipementInternetPromotion : this.retEquipementInternetPromotion(),
				infoNuagique : this._reteboxTVOptions(),
				decodeurPromo : this.retPromoDecodeur()
			}

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validate_order' + '/controler_ajax_numerique.php',
				data, resolve, 'json'
			).error(reject);
		});

	}

	mustRentEquipement() {
		return true;
		/*const hasTV = CookiesService.getCookies('hasTV');
		let checkRent = (equipement) => { return equipement.buyRent === 'rent' };
		this.decodeurInfo = this.retDecodeur();
		if(this.phoneEquipement === null){
			this.phoneEquipement = [];
		}
		if(hasTV !== 'true'){
			this.decodeurInfo.buyRent = 'own';
		}
		this.initInternetPlan();
		if(this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'){
			return this.internetEquipement.some(checkRent) || this.decodeurInfo.buyRent === 'rent';

		}

		return this.phoneEquipement.some(checkRent) || this.internetEquipement.some(checkRent) || this.decodeurInfo.buyRent === 'rent';
		*/
	}

	mustRentEquipementNumerique(){
		return true;
	}

	payBill(payementInfo) {
		const infoClient = this._retSavedInfo();
		return new Promise((resolve, reject) => {
			const data = {
				payementInfo: payementInfo,
				billInfo: this._retBillInfo(),
				infoClient : infoClient
			}
			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validate_pay_order' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}


	initInternetPlan() {
		LocalStorage.get('internetPlan') !== null ? this.internetPlan = LocalStorage.get('internetPlan') : {};
		LocalStorage.get('internetEquipement') !== null ? this.internetEquipement = LocalStorage.get('internetEquipement') : {};
		this.chargesValues.internetEquipement = this.internetEquipement;
		this.chargesValues.internetPlan = this.internetPlan;
		this.charges.next(this.chargesValues);
	}

	initDecodeur() {
		LocalStorage.get('decodeur') !== null ? this.decodeurInfo = LocalStorage.get('decodeur') : {};
	}

	saveInternetService(data) {
		this.internetPlan = data;
		this.initPhonePlan();
		LocalStorage.set('internetPlan', JSON.stringify(this.internetPlan));
		LocalStorage.delete('codePromo');
		CookiesService.setCookies('selectedInternetEbox', this.internetPlan);
		CookiesService.setCookies({
			name: 'selectedInternetEbox',
			value: this.internetPlan,
		});
		this.chargesValues.internetPlan = this.internetPlan;
		this.charges.next(this.chargesValues);

		var actualTypeOffre = CookiesService.getCookies('typeOffre');
		if( actualTypeOffre != 'numerique'){
			this.$promotionsService.getInternetPromotion();
			this.$promotionsService.getDuoPromotion();
		}

		//CookiesService.setCookies({name: 'CommandeStarted', value: 'oui'});

		const expirationDate = new Date();
		expirationDate.setDate(expirationDate.getDate() + 3);

		CookiesService.setCookies({
			name: 'CommandeStarted',
			value: 'oui',
			expires: expirationDate
		});

		this.initCart();
	}

	saveEquipementInternet(data) {
		this.internetEquipement = data;
		LocalStorage.set('internetEquipement', JSON.stringify(this.internetEquipement));
		// this.updateModemService();
		this.chargesValues.internetEquipement = this.internetEquipement;
		this.charges.next(this.chargesValues);

		var actualTypeOffre = CookiesService.getCookies('typeOffre');
		if( actualTypeOffre != 'numerique'){
			this.$promotionsService.getEquipementInternetPromotion();
		}

		this.initCart();
	}

	_retInternetOrder() {
		return this.internetPlan;
	}
	_retInternetEquipement() {
		return this.internetEquipement;
	}

	// debut service phone
	initPhonePlan() {
		LocalStorage.get('phonePlan') !== null ? this.phonePlan = LocalStorage.get('phonePlan') : {};
		this.updateModemService();
		if(LocalStorage.get('phoneEquipement') === null){
			LocalStorage.set('phoneEquipement', JSON.stringify([]));
		}

		LocalStorage.get('phoneEquipement') !== null ? this.phoneEquipement = LocalStorage.get('phoneEquipement') : [];
	}

	retPromoInfo(){
		return LocalStorage.get('InfoPromotion') !== null ? LocalStorage.get('InfoPromotion') : {};
	}
	retPromoDuo(){
		return LocalStorage.get('DuoPromotion') !== null ? LocalStorage.get('DuoPromotion') : {};
	}
	retInternetPromo(){
		return LocalStorage.get('InternetPromotion') !== null ? LocalStorage.get('InternetPromotion') : {};
	}

	savePhoneService(data) {
		this.initInternetPlan();
		this.phonePlan = data;
		LocalStorage.set('phonePlan', JSON.stringify(this.phonePlan));
		this.chargesValues.phonePlan = this.phonePlan;
		this.updateModemService();
		this.charges.next(this.chargesValues);
		this.saveEquipementInternet(this.internetEquipement);
	}


	updateModemService(){
		if(LocalStorage.get('qualificationAddresse')){
			let province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
			if(this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'){
				if( !(this.internetPlan.technology == 'v-cable' || (this.internetPlan.technology == 'c-cable' && province == 'qc') )){

					if(this.phonePlan.type == undefined || this.phonePlan.type == 'none'){
						if(this.internetEquipement[0]){
							this.internetEquipement.forEach(equipement => {
								if(equipement.type == 'modem'){
									equipement.buyPrice = '120.00';
									equipement.rentPrice = '5.95';
									if(equipement.rebate){
										equipement.rebate = '12.00';
									}
								}
							});
						}
					}
					else{
						if(this.internetEquipement[0]){
							this.internetEquipement.forEach(equipement => {
								if(equipement.type == 'modem'){
									equipement.buyPrice = '160.00';
									equipement.rentPrice = '5.95';
									if(equipement.rebate){
										equipement.rebate = '16.00';
									}
								}
							});
						}
					}
				}
			}
		}
	}

	saveEquipementPhone(data) {
		this.phoneEquipement = data;
		LocalStorage.set('phoneEquipement', JSON.stringify(this.phoneEquipement));
		this.chargesValues.phoneEquipement = this.phoneEquipement;
		this.charges.next(this.chargesValues);

	}

	saveDecodeur(data) {
		this.decodeurInfo = data;
		LocalStorage.set('decodeur', JSON.stringify(this.decodeurInfo));
		this.charges.next(this.chargesValues);
		this.initCart()
	}

	retDecodeur(){
		const decodeur = LocalStorage.get('decodeur') || {};
		return decodeur;
	}
	retPromoDecodeur(){
		return LocalStorage.get('DecodeurPromotion') || {};
	}

	retPhoneOrder() {
		return LocalStorage.get('phonePlan') || {};
	}

	_retPhoneOrder() {
		return this.phonePlan;
	}

	_retPhoneEquipement() {
		return this.phoneEquipement;
	}

	_retOrder() {
		return this.order;
	}

	initBillInfo() {
		this.billInfo = LocalStorage.get('credit_response') || {};
	}

	initOrderConfirmation() {
		this.initBillInfo();
		this.initFormInformationPersonnel();
		this.initPhonePlan();
		this.initInternetPlan();
		this.initDecodeur();
	}



	SaveQualificationAddresse(data) {
		this.qualificationAddresse = data;
		LocalStorage.set('qualificationAddresse', JSON.stringify(this.qualificationAddresse));
	}

	saveQualificationAppartement(data) {
		this.qualificationAddresse.app = data;
		LocalStorage.set('qualificationAddresse', JSON.stringify(this.qualificationAddresse));

	}

	_retQualificationAddresse() {
		return LocalStorage.get('qualificationAddresse') || {};
	}

	retEquipementInternetPromotion(){

		return LocalStorage.get('EquipementInternetPromotion') || {};
	}

	retInternetPlan(){
		return LocalStorage.get('internetPlan') || {};
	}

	_reteboxTVOptions(){
		return LocalStorage.get('eboxTVOptions') || {};
	}

	_retPromoCode(){
		return LocalStorage.get('codePromo') || {};
	}

	retPromoCode(){
		return LocalStorage.get('codePromo') || {};
	}

	_retPromoCrave(){
		return LocalStorage.get('promoCrave') || {};
	}

	retPromoCrave(){
		return LocalStorage.get('promoCrave') || {};
	}


	initCart() {
		this.initDecodeur();
		this.internetPlan = LocalStorage.get('internetPlan') || {};
		this.internetEquipement = LocalStorage.get('internetEquipement') || {};
		this.phoneEquipement = LocalStorage.get('phoneEquipement') || {};
		this.order = LocalStorage.get('order') || {};


		const promoDuo = this.retPromoDuo();
		const promoInternet = this.retInternetPromo();
		const promoInfo = this.retPromoInfo();
		const promoInternetEquipement = this.retEquipementInternetPromotion();
		const decodeur = this.retDecodeur();
		const eboxTVOptions = this._reteboxTVOptions();
		const promoDecodeur = this.retPromoDecodeur();
		const promoCode = this._retPromoCode();
		const promoCrave = this._retPromoCrave();
		const promoFibre = LocalStorage.get('promoFibre') || {};
		// const promoInternetEquipement = this.retPromoInternetEquipement();

		this.chargesValues.internetPlan = this.internetPlan;
		this.chargesValues.internetEquipement = this.internetEquipement;
		this.chargesValues.phonePlan = LocalStorage.get('phonePlan') || {};
		this.chargesValues.phoneEquipement = this.phoneEquipement;
		this.chargesValues.order = this.order;
		this.chargesValues.promoDuo = promoDuo;
		this.chargesValues.promoInfo = promoInfo;
		this.chargesValues.promoInternet = promoInternet;
		this.chargesValues.promoInternetEquipement = promoInternetEquipement;
		this.chargesValues.decodeur = decodeur;
		this.chargesValues.eboxTVOptions = eboxTVOptions;
		this.chargesValues.promoDecodeur = promoDecodeur;
		this.chargesValues.promoCode = promoCode;
		this.chargesValues.promoCrave = promoCrave;
		this.chargesValues.promoFibre = promoFibre;

		this.charges.next(this.chargesValues);
	}



	getSkuTv() {
		let tvChannel = {
			tvRegion : '',
			Basic : {
				pack : [],
			},
			Theme : {
				pack : [],
				channel : []
			},
			Package : {
				pack : [],
				channel : []
			},
			StandAlone : {
				channel: []
			},
			Prenium : {
				channel: []
			},
			dvrs : []
		}

		const hasTV = CookiesService.getCookies('hasTV');
		let baseChannel = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvChannels.base : [];
		tvChannel.tvRegion = baseChannel[0].area_name;
		if(hasTV == 'true'){
			let stepsArray = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvOptions.steps.details : [];
			let baseChannel = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvChannels.base : [];


			tvChannel.tvRegion = baseChannel[0].area_name;
			tvChannel.Basic.pack.push({Q:1, C: baseChannel[0].area_sku});

			const listeChannels = this.$tvOrderService.selectedChannels;
			const theme = this.$tvOrderService.getSelectedBundle();

			if(theme && theme.sku != ""){
				var skuTheme = theme.sku;
				if(skuTheme == 'TVT00005'){
					var lang = CookiesService.getCookies('wp-wpml_current_language')
					if(lang == 'en'){
						skuTheme = 'TVT00006';
					}
				}

				tvChannel.Theme.pack.push({Q: 1 , C: skuTheme});
				theme.channels.forEach(id => {
					const channel = this.$tvOrderService.getChannelById(id);
					//if(channel != undefined){
						if(channel.children.length > 0){
							channel.children.forEach(chan =>{
								if(chan.sku && chan.sku == 'TVC00410'){
									tvChannel.Theme.channel.push({Q:1, C:chan.sku});
								}
							})
						}
						//if(channel.sku != undefined){
							tvChannel.Theme.channel.push({Q:1, C:channel.sku});
						//}
					//}
				});
			}

			let selectedStep = [];
			let firstComplete = false;
			for( const property in listeChannels){
				listeChannels[property].forEach(element => {
					let i = 0;
					if(property === 'a-la-carte'){
						if(!firstComplete){
							stepsArray.forEach(step => {
								if(step.size <= listeChannels[property].size && listeChannels[property].size > 0){
									selectedStep = step;
									tvChannel.Package.pack[0] = {Q : 1 , C : selectedStep['sku']};
								}
							});
							firstComplete = true;
						}
					}

					const channel = this.$tvOrderService.getChannelById(element);
					let data = {Q : 1, C : channel.sku}
					if(property === 'a-la-carte'){
						if(i++ < selectedStep.size){
							tvChannel.Package.channel.push(data);
						}
						else{
							tvChannel.StandAlone.channel.push(data);
						}
					}
					else if(property === 'premium'){
						tvChannel.Prenium.channel.push(data);
					}
					else if(property === 'others'){
						tvChannel.StandAlone.channel.push(data);
					}

				});

			}

			const dvrs = this._reteboxTVOptions();
			if(dvrs.cloudSpace && dvrs.cloudSpace.sku != 0){
				const data = {Q : 1, C: dvrs.cloudSpace.sku};
				tvChannel.dvrs.push(data);
			}else{
				const data = "";
				tvChannel.dvrs = "";
			}
		}
		return tvChannel;

	}



	saveTVOption(options){
		LocalStorage.set('eboxTVOptions', options);
		this.initCart();

		this.charges.next(this.chargesValues);

	}
	retTVOption(){
		return LocalStorage.get('eboxTVOptions') || {};
		// this.charges.next(this.chargesValues);

	}

	retTVOption(){
		return LocalStorage.get('selectedBundles') || [];
		// this.charges.next(this.chargesValues);

	}

}