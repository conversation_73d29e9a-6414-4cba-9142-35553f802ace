.HomeDivertissement {
	background-image: url('../../../img/videobg.png');
	width: 100%;
	padding: 80px 0;
	background-repeat: no-repeat;
	background-size: 100% 730px;

	@media (max-width: 991px) {
		background-size: 100% 300px;
	}
	@media(max-width: 767px) {
		padding:60px 15px 0;
	}

	&__slider {
		margin: 0 0 0 10px;
	}
	&__slider li {
		text-align: center;
	}
	&__slider img {
		width: auto !important;
		margin: 0px auto !important;
	}
	&__slider p {
		color: #ffffff;
		font: 400 16px/16px Gloria Hallelujah !important;
		text-align: center;
	}
	&__slider span {
		color: #ffffff;
		font: 700 16px/16px $f-primary !important;
		text-transform: uppercase;
		text-align: center;
		margin-top: 10px;
	}
	&__slider-item .socialheight {
		@media (max-width: 767px) {
			min-height: 85px;
		}
	}
	&__devices-image {
		max-width: 100%;

		&--games {
			max-width: 488px;

			@media (max-width: 991px) {
				max-width: 100%;
			} 
		}

		&--mobile {
			display: none;
		}

		@media (max-width: 991px) {
			width: 440px;
			margin-bottom: 0 !important;

			&--mobile {
				display: block;
			}

			&--no-mobile {
				display: none;
			}
		}
	}
	&__content {
		margin-bottom: 40px;
	}

	&__carousel{
		padding: 0 !important;

	}
	@media (min-width: 767px) and (max-width: 767px) {
		&__slider button.owl-prev {
			float: left;
			position: absolute;
			top: 30%;
			left: -25%;
			background: transparent;
			border: none;
		}
		&__slider button.owl-prev span {
			font-size: 60px !important;
			font-weight: normal !important;
		}
		&__slider button.owl-next {
			float: right;
			position: absolute;
			top: 30%;
			right: -25%;
			background: transparent;
			border: none;
		}
		&__slider button.owl-next span {
			font-size: 60px !important;
			font-weight: normal !important;
		}
	}
	@media (max-width: 767px) {
		&__slider button.owl-prev {
			float: left;
			position: absolute;
			top: 30%;
			left: 15px;
			background: transparent;
			border: none;
		}
		&__slider button.owl-prev span {
			font-size: 60px !important;
			font-weight: normal !important;
		}
		&__slider button.owl-next {
			float: right;
			position: absolute;
			top: 30%;
			right: 45px;
			background: transparent;
			border: none;
		}
		&__slider button.owl-next span {
			font-size: 60px !important;
			font-weight: normal !important;
		}
	}

	&__item5050{
		display: flex;
		flex-direction: column;
		align-content: center;
		align-items: center;
		width: 540px;
		padding: 0 50px;
		justify-content: center;


		p,h2{
			padding: 0 20px;
			text-align: center;
		}

		&:first-child{
			@media screen and (max-width: 1199px){
				margin-bottom: 40px;
			}
			
		}
		
	}

	

	&__block5050{
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		color: white;
		width: 100%;
	}

	&__spliter{
		height: 100px;
		width: 1px;
		background-color: #979797;
		@media screen and (max-width: 1199px){
			display: none
		}
	}

	&__telephone{
		padding: 0 !important;
	}

	&__telephone__title{
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
		width: 100%;
		color: white;
		margin-bottom: 60px;

		p{
			font: 24px $f-secondary;
		}
	}

	.container {
		@media (max-width: 991px) {
			text-align: center;
		}
		@media (max-width: 767px) {
			padding-right: 0 !important;
			padding-left: 0 !important;
		}
	}
	&__purdivertissement {
		width: 100%;
		text-align: center;
	}
	&__purdivertissement>h2 {
		text-align: center;
		color: #ffffff;
		font: 900 45px/50px $f-primary;
		@media (max-width: 767px) {
			font: 900 26px/32px "BrandonGrotesque", sans-serif!important;
		}
	}
	&__purdivertissement>img {
		margin: 50px auto;
	}
	&__section {
		padding: 70px 0;
		border-bottom: 1px solid #C7C7C7;
		margin: 0 auto;

		@media (max-width: 1200px) {
			width:100%;
		}
		
		&--installeapps {
			padding: 40px 0 80px;

			@media(max-width: 767px) {
				padding: 40px 0;
			}
			h4 {
				font: 700 45px/50px $f-primary;
				color: #ffffff;
				margin-bottom: 10px;
				@media (max-width: 767px) {
					font: 700 26px/32px $f-primary;
				}
			}
			span {
				display: block;
				font: 400 16px/22px $f-primary;
				color: #ffffff;
			}
		}

		&--jeux {
			.text-right {
				float: right;
				@media(max-width: 767px) {
					text-align:center;
					float: left;
					width: 100%;
					margin: 0px auto;
				}
			}
			.text-right img {
				@media(max-width: 767px) {
					margin: 0 auto;
				}
			}

			h4 {
				float: left;
				width: 100%;
				font: 700 45px/50px $f-primary;
				color: #ffffff;
				margin-bottom: 10px;
				@media (max-width: 767px) {
					font: 700 45px/50px $f-primary;
				}
				@media (max-width: 768px) {
					font: 700 24px/26px $f-primary;
				}
			}
			span {
				float: left;
				width: 100%;
				font: 400 16px/22px $f-primary;
				color: #ffffff;
			}
		}

		&--bluetooth {
			.text-left {
				@media(max-width: 767px) {
					text-align:center;
					float: left;
					width: 100%;
					margin: 0px auto;
				}
			}
			h4 {
				float: left;
				width: 100%;
				font: 700 45px/50px $f-primary;
				color: #ffffff;
				margin-bottom: 10px;
				@media (max-width: 767px) {
					font:700 40px/46px "BrandonGrotesque", sans-serif;
				}
				@media (max-width: 768px) {
					font: 700 24px/26px $f-primary;
				}
			}
			span {
				float: left;
				width: 100%;
				font: 400 16px/22px $f-primary;
				color: #ffffff;
			}
		}
	}

	&__section:last-child {
		border: none;
		
		@media (min-width: 768px) {
			padding-bottom: 0;
		}
	}

	&__socialmedia {
		display: flex;
		justify-content: space-between;
		width: 100%;
		text-align: center;

		@media (min-width: 1025px) {
			padding: 0 !important;
			.owl-stage {
				display: flex;
				justify-content: space-between;
				width: auto !important;

				&:after {
					content: none !important;
				}
			}
		}

		
		@media (max-width: 767px) {
			margin: 0 !important;
		}
	}
	&__socialmedia .col-lg-3 {
		text-align: center;
		float: left;
	}
	&__socialmedia img {
		margin: 0 auto 40px;
		height: 50px;
		width: auto !important;

		@media (max-width: 1024px) {
			margin-bottom: 20px;
			height: 35px;
		}
	}
	&__socialmedia p {
		font: 400 20px/24px Gloria Hallelujah;
		color: #ffffff;
		margin-bottom: 0px;

		@media (max-width: 1024px) {
			font: 400 16px/22px Gloria Hallelujah;
		}
	}
	&__socialmedia span {
		font: 700 25px/30px $f-primary;
		color: #ffffff;
		text-transform: uppercase;

		@media (max-width: 1024px) {
			font: 700 16px/22px $f-primary;
		}
	}
	&__socialmedia .socialheight {
		// min-height: 140px;
	}
	
	&__text--middle {
		padding: 80px 0;

		@media(max-width: 991px) {
			float: left;
			width: 100%;
			padding: 30px 0;
		}

		@media(max-width: 767px) {
			float: left;
			width: 100%;
			padding: 30px 0 0;
		}
	}

	&__align-items-center {
		display: flex;

		> * {
			display: flex;
			align-items: center;
		}

		&--no-mobile {
			@media(max-width: 991px) {
				display: block;
				> * {
					display: block;
				}
			}
		}
	}
}
