import { ChannelsService } from '../../js/services/channels.service';

export class PriceStepsManager {
	private static instance: PriceStepsManager;
	public static getInstance(): PriceStepsManager {
		if (PriceStepsManager.instance === undefined)
			PriceStepsManager.instance = new PriceStepsManager();

		return PriceStepsManager.instance;
	}

	public readonly STEP_SIZE: number = 5;

	public $channels: ChannelsService = ChannelsService.getInstance();
	
	private hasReadableSteps: boolean = 'wpLocalizedVars' in window &&
		!!wpLocalizedVars.eboxtvOptions.steps.details.length;

	private costPerAdditionalSteps: number = this.hasReadableSteps && 'wpLocalizedVars' in window ?
		wpLocalizedVars.eboxtvOptions.steps.additionnalStepCost : 5;
	
	private steps: StepsDetails[];

	public STEP_MIN_VALUE: number = 10;

	private constructor() {
		this.buildSteps();
	}

	public buildSteps(): void {
		const channelsQty: number = this.$channels.getAll().length;
		const steps: StepsDetails[] = this.hasReadableSteps
			? [{cost: 0, size: 0}, ...wpLocalizedVars.eboxtvOptions.steps.details]
			: [{cost: 0, size: 0}];
		const lastStep: StepsDetails = steps.length ? steps[steps.length - 1] : {cost: 0, size: 0};
		let i: number = 0;
		
		while (true) {
			const size: number = lastStep.size + ((i + 1) * this.STEP_SIZE);
			const cost: number = lastStep.cost + ((i + 1) * this.costPerAdditionalSteps);
			
			steps.push({ size, cost });
			
			if (size >= channelsQty) break;
			i++;
		}

		this.steps = steps;
	}

	public getStepBySize(size: number): StepsDetails {
		for (let i: number = 0; i < this.steps.length; i++) {
			const currentStep: StepsDetails = this.steps[i];

			if (size <= currentStep.size) {
				return currentStep;
			}
		}

		return null;
	}
}

declare var wpLocalizedVars: {
	eboxtvOptions: {
		steps: {
			details: StepsDetails[];
			additionnalStepCost: number;
		}
	}
};

export interface StepsDetails {
	size: number;
	cost: number;
}