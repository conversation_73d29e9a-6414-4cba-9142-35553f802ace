import { Directive } from '@core/directive';
import { TVOrderService } from '../../services/TVOrder.service';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service.ts';

export class AddClassAtHeightDirective extends Directive {
	static selector = '[add-class-at-height]';

	// PRIVATE PROPERTIES //
	_resizeDebounce;
	_tvOrderChangeSub;
	_wrapperElement = this.host.querySelector(this.attrs.wrapperSelector);
	_removeClasses = this.attrs.removeClasses ? this.attrs.removeClasses.replace(' ', '').split(',') : [];

	// DI //
	_changeDetector = ViewChangeDetector.getInstance();
	_$TVOrder = TVOrderService.getInstance();


	// PUBLIC PROPERTIES //
	/**
	 * @type {boolean}
	 */
	get hasClass() {
		return this.$host.hasClass(this.attrs.className);
	}


	// ON INIT //
	constructor(host) {
		super(host, [
			{name: 'add-class-at-height', type: 'int', required: true},
			{name: 'class-name', required: true},
			'remove-classes',
			'wrapper-selector'
		]);

		this._init();
	}
	_init() {
		this._subscribeToOrderChange();
		this._bindEvents();
		setTimeout(() => {
			this._onTVOrderChange();
		}, 100);
	}


	// PRIVATE METHODS //
	_bindEvents() {
		jQuery(window).on('resize', this._onResize.bind(this));
	}

	_onResize() {
		clearTimeout(this._resizeDebounce);
		setTimeout(this._onTVOrderChange.bind(this), 100);
	}

	_subscribeToOrderChange() {
		this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onTVOrderChange.bind(this)
		);
	}

	_onTVOrderChange() {
		const scrollHeight = this._getScrollHeight();
		const removedClass = [this.attrs.className, ...this._removeClasses].join(' ');
		const hasClass = this.hasClass;

		if (scrollHeight >= this.attrs.addClassAtHeight && !hasClass)
			this._addClass();
		else if (scrollHeight < this.attrs.addClassAtHeight)
			this._removeClass(removedClass);
	}

	_removeClass(removedClass) {
		this.$host.removeClass(removedClass);
		this._changeDetector.emitChange(this);
	}

	_addClass() {
		this.$host.addClass(this.attrs.className);
		this._changeDetector.emitChange(this);
	}

	_getScrollHeight() {
		if (this._wrapperElement)
			return this._wrapperElement.scrollHeight;
		else
			return this.host.scrollHeight;
	}
}