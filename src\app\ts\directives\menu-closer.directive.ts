export class MenuCloserDirective {
    static selector: string = '[data-menu-closer]';

    App: any;
    $host: JQuery = jQuery(this.host);

    constructor(private host: HTMLElement) {
        this.onInit();
    }

    private onInit(): void {
        this.bindEvents();
        this.onAppBootstrapped();
    }

    private bindEvents(): void {
        jQuery(window).on('App.bootstrapped', this.onAppBootstrapped.bind(this));
        this.$host.on('click', this.onHostClick.bind(this));
    }

    private onAppBootstrapped(): void {
        this.App = (<any>window).App;
    }

    private onHostClick(): void {
        jQuery('.header--menu-opened').removeClass('header--menu-opened');
        setTimeout(MenuCloserDirective.handleSubMenus, 250);
    }

    static handleSubMenus(): void {
        const _menuItemHasChildrenInstances: any[] = MenuCloserDirective.findMenuItemHasChildren();

        for (let i = 0; i < _menuItemHasChildrenInstances.length; i++) {
            let _curr: any = _menuItemHasChildrenInstances[i];

            if ( _curr.isOpened )
                _curr.isOpened = false;
        }
    }

    static findMenuItemHasChildren(): any[] {
        return (<any>window).App.directivesInstances.filter((dir: any) => dir.name === 'MenuItemHasChildren')[0].instances;
    }
}