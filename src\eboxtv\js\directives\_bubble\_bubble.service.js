/* global jQuery */
import { CoreTools } from '@core/helpers';
import { Observable } from 'rxjs/Observable';
import { Subject } from 'rxjs/Subject';

export class BubbleService {
	_currentBubbleChangeSubject$ = new Subject();

	currentBubbleChange$ = this._currentBubbleChangeSubject$.asObservable();
	
	/**
	* @private
	* @type {string}
	*/
	_currentBubbleValue = null;

	constructor() {
		this._bindEvents();
	}

	_bindEvents() {
		window.addEventListener('keydown', this._onWindowKeydown.bind(this));
		window.addEventListener('click', this._onWindowClick.bind(this));
	}

	_onWindowKeydown(evt) {
		if (this.currentBubble && CoreTools.isKey('esc', evt.which))
			this.clearBubbles();
	}

	_onWindowClick(evt) {
		if ('ontouchstart' in window && (this.currentBubble && !this.currentBubble.attrs.isTooltip)) return;

		if (!BubbleService.isFromBubbleElement(evt.target) && this.currentBubble !== null) {
			this.clearBubbles();
		}
	}

	/**
	 * @alias _currentBubbleValue
	 */
	get currentBubble() {
		return this._currentBubbleValue;
	}
	set currentBubble(val) {
		if (val !== this._currentBubbleValue) {
			this._currentBubbleValue = val;
			this._currentBubbleChangeSubject$.next(val);
		}
	}

	setCurrentBubble(bubbleInstance) {
		this.currentBubble = bubbleInstance;
	}

	clearBubbles() {
		this.currentBubble = null;
	}

	static isFromBubbleElement(element, detectBtn = false) {
		const $element = jQuery(element);

		try {
			return !!(element.hasAttribute('bubble') || $element.isWithin('[bubble]') || (detectBtn ? $element.hasClass('channel__details-btn') : false));
		} catch(e) {
			return false;
		}
	}
}

export const $bubbleServiceProvider = new BubbleService();