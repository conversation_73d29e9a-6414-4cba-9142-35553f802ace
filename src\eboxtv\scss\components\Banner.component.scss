.Banner {
	position: relative;
	left: -14px;
	display: block;
	width: calc(100% + 14px);
	padding-bottom: 14px;
	animation: unveil 0.3s $cubic forwards;
	
	&__wrapper {
		position: relative;
		background: $c-primary;
		padding: 11px 34px 11px 34px;
		box-sizing: content-box;

		&:before {
			content: '';
			position: absolute;
			display: block;
			transform: translateZ(0);
			top: 100%;
			left: 0;
			width: 0;
			height: 0;
			border-style: solid;
			border-width: 0 14px 14px 0;
			border-color: transparent $c-primary-darker transparent transparent;
		}
	}

	&__title-container {
		position: relative;
	}

	&__title {
		position: relative;
		font: 400 18px/1.2 $ebtv-f-primary;
		text-align: center;
	}

	&__count {
		display: inline-block;
		will-change: transform;
		animation: pulse 0.25s $cubic;
	}

	&__price {
		position: relative;
		vertical-align: middle;
		
		&.is-doodled {
			&--underlined {
				margin-bottom: 7px;

				&:after {
					top: 100%;
					height: 20%;
					background-image: url('~@app/img/line-white.svg');
				}
			}
		}
	}

	&__icon {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		width: 17px;
		height: 17px;
		fill: currentColor;
		bottom: 2px;
		margin-right: 5px;
		animation: pulse 1s steps(2) infinite;
	}

	&__splash {
		position: absolute;
		display: inline-block;
		width: 15px;
		height: 100%;
		stroke: #FFF;
		right: 100%;
		top: 2px;
		animation: pulseIntense 1s steps(2) infinite;

		&--right {
			transform: rotateY(180deg);
			right: auto;
			left: 100%;
			animation-name: pulseMirrorIntense;
		}
	}
}