.FAQCard {
	position: relative;
	display: block;
	padding-bottom: 20px;

	&--opened {
		.FAQCard {
			&__body {
				height: auto;
				overflow: visible;

				&:after {
					content: none;
				}
			}

			&__button {
				&-title {
					&--opened {
						display: inline-block;
					}

					&--closed {
						display: none;
					}
				}
			}
		}
	}

	&__wrapper {
		padding: 16px 16px 44px;
		background-color: #FFF;
		border-radius: 12px;
		box-shadow: 0 2px 6px rgba(#000, 0.5);
	}

	&__head {
		text-align: center;
		padding: 0 8px 8px;
		border-bottom: 1px solid #C7C7C7;

		&-title {
			font: 700 35px/40px $f-primary;
			text-transform: uppercase;

			@media (max-width: 767px) {
				font: 700 24px/26px $f-primary;
			}
		}
	}

	&__body {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		padding: 16px 0 0;
		height: 133px;
		overflow: hidden;

		/*&:after {
			content: '';
			position: absolute;
			display: block;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 31px;
			background-image: linear-gradient(180deg, rgba(#FFF, 0), rgba(#FFF, 0.62) 50%, #FFF 100%);
		}*/
	}

	&__question-list-container {
		&:not(:last-child) {
			margin-bottom: 64px;
		}
	}

	&__question-list-title {
		font: 400 20px/30px $f-secondary;
		color: $c-primary;
		margin-bottom: 8px;
	}

	&__question-list {
		margin: 0;
	}

	&__question-item {
		&:not(:last-child) {
			margin-bottom: 8px;
		}
	}

	&__question-link {
		color: $c-black;
		font: 700 16px/22px $f-primary;
		transition: color 0.1s linear;

		&:hover {
			color: $c-primary-darker;
			text-decoration: underline;
		}
	}

	&__button {
		position: absolute;
		appearance: none;
		border: none;
		bottom: 0;
		left: 50%;
		transform: translate(-50%, 0);
		height: 40px;
		font: 700 12px/42px $f-primary;
		padding: 0 20px;

		&-title {
			&--opened {
				display: none;
			}
		}
		&:focus {
			outline: 2px solid blue;
			outline-offset: 2px;
		}
	}
}