export class ChannelsService {
	static instance;
	/**
	 * @return {ChannelsService}
	 */
	static getInstance() {
		if (ChannelsService.instance === undefined)
			ChannelsService.instance = new ChannelsService();

		return ChannelsService.instance;
	}
	_parents = window.wpLocalizedVars ? window.wpLocalizedVars.eboxtvChannels : {};
	_allParents = [].concat.apply([], Object.values(this._parents));
	_bundles = window.wpLocalizedVars ? window.wpLocalizedVars.eboxtvBundles : [];
	_forfaits = window.wpLocalizedVars ? window.wpLocalizedVars.forfaitsTV : [];
	
	all = this.getAll('all', false);

	getAll(types = 'all', idOnly = false) {
		let output = [];

		if (!(types instanceof Array)) {
			types = [types];
		}

		for (const type of types) {
			const children = this.getChildren(type);
			if (type === 'all') {
				output = [...this._allParents, ...children];
				break;
			} else {
				if(Object.keys(this._parents).length !== 0) {
					output = [
						...output,
						...this._parents[type],
						...children
					];
				}

			}
		}
		if (idOnly)
			return output.map(channel => channel.ID);
		else
			return output;
	}

	getChildren(type = 'all', getCount = false) {
		let parents = type === 'all' ? this._allParents : this._parents[type];
		let children = [];
		
		if (getCount) {
			var count = 0;
		}

		if(parents){
			parents.forEach(curr => {
				if ('children' in curr) {
					const childrenArray = Object.values(curr.children);
					
					if (getCount) {
						count += childrenArray.length;
					} else {
						children = children.concat(childrenArray);
					}
				}
			});
		}

		
		if (getCount)
			return count;
		else
			return children;
	}

	getBundlesById(...ids) {
		return this._bundles.filter(bundle => ~ids.indexOf(bundle.ID));
	}

	getBundleById(id) {
		for (let i = 0; i < this._bundles.length; i++) {
			if (this._bundles[i].ID == id)
				return this._bundles[i];
		}
		return null;
	}

	getForfaitById(id) {
		for (let i = 0; i < this._forfaits.length; i++) {
			if (this._forfaits[i].ID == id)
				return this._forfaits[i];
		}
	}

	getLength(type = 'all') {
		if (type !== 'all') {
			return this._parents[type].length + this.getChildren(type, true);
		} else {
			return this._allParents.length + this.getChildren('all', true);
		}
	}

	static clearGlobals() {
		if ('eboxtvChannels' in window)
			window.eboxtvChannels = undefined;
	}
}