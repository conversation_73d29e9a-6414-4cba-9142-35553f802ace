.steps {
	font-family: $ebtv-f-primary !important;

	&__title {
		position: relative;
		margin: 0 0 100px;

		@media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
			font: 900 30px/34px $ebtv-f-primary;
			margin: 0 0 70px;
		}
		@media (max-width: 399px) {
			font: 900 26px/32px $ebtv-f-primary;
		}
	}

	&__title-note {
		position: absolute;
		bottom: 100%;
		right: -45px;
		padding-bottom: 30px;
		font: 400 16px/20px $ebtv-f-secondary;
		color: $c-primary;
		text-transform: none;
		// width: 250px;
		&:before {
			content: '';
			position: absolute;
			top: 5px;
			right: 100%;
			width: 75px;
			height: 75px;
			transform: rotate(-70deg);
			background: {
				image: url('../../img/drawn-arrow-03.svg');
				size: 90%;
				position: center;
				repeat: no-repeat;
            }
        }
        &:lang(en) {
            &:before {
                top: -10px;
                transform: rotate(-50deg);
            }
        }

		@media (max-width: 1300px) {
			display: block;
			position: relative;
			bottom: auto;
			right: auto;
			font: 700 25px/45px $ebtv-f-primary;
			color: currentColor;

			&:before {
				content: none;
			}
		}

		@media (max-width: 767px) {
			display: block;
			position: relative;
			bottom: auto;
			right: auto;
			font: 700 20px/30px $ebtv-f-primary;
			color: currentColor;

			&:before {
				content: none;
			}
		}
	}

	&__container.steps__container {
		width: 100%;
		margin: 0 auto;
		position: relative;

		&--timeline {
			&:after {
				content: '';
				position: absolute;
				display: block;
				width: 90%;
				height: 2px;
				background-color: $c-primary;
				top: 44%;
				left: 50%;
				margin-top: -1px;
				transform: translateX(-50%);

				@media (max-width: map-get($ebtvBreakpoints, BSmd)) {
					top: 50%;
					margin-top: -1px;
				}

				@media (max-width: 730px) {
					margin-top: -5px;
					width: 100%;
				}
			}
		}

		@media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
			padding: 0 !important;
		}
	}
}