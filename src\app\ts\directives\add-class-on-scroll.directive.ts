import { Directive } from '@core/directive.js';
import { Tools } from '@common/ts/services/Tools.service';

export class AddClassOnScrollDirective extends Directive {
	static selector: string = '[data-add-class-on-scroll]';

	private readonly EVENT_NAMESPACE: string = 'data-add-class-on-scroll' + Tools.generateRdmString();
	private $window: JQuery = jQuery(window);
	private $targetElement: JQuery;
	private targetPosition: number;
	/**
	 * Gets its value from converting the value of attrs.viewOffset which
	 * must have a value between 1 and -1. This value represents the vertical point the
	 * trigger must reach in order to trigger the CSS class change.
	 *
	 * 1 represents the top of the screen and -1 represents the bottom.
	 * 0 represents the center of the screen.
	 *
	 * viewOffset value gets converted into a float between 0 and 1 in order to
	 * multiply its value by window.innerHeight.
	 */
    private dataViewOffset: number = Tools.map(this.attrs.dataViewOffset, -1, 1, 1, 0);
    private dataElementOffset: number = Tools.map(this.attrs.dataElementOffset, -1, 1, 1, 0);
	private get viewTriggerPosition(): number {
		const scrollPosY: number = window.pageYOffset;
		return scrollPosY + (window.innerHeight * this.dataViewOffset);
	}

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'data-add-class-on-scroll', as: 'className', required: true},
            {name: 'data-view-offset', type: 'float', default: 0},
			{name: 'data-element-offset', type: 'float', default: 0},
			{name: 'delay', type: 'int', default: 0},
			'data-remote-element-selector',
			{name: 'remote-is-child', type: 'eval', default: false}
		]);
		this.onInit();
	}

	public updateFromPosition(): void {
		const triggerPosition: number = this.viewTriggerPosition;

        if (triggerPosition >= this.targetPosition) {
			this.addClass(this.attrs.delay);
		}
	}

	private onInit(): void {
		this.getTargetElements();
		this.getTargetPosition();
		this.bindEvents();
		this.updateFromPosition();
	}

	private getTargetElements(): void {
		if (!this.attrs.dataRemoteElementSelector) {
			this.$targetElement = this.$host;
		} else {
			if (this.attrs.remoteIsChild)
				this.$targetElement = jQuery(this.attrs.dataRemoteElementSelector, this.host);
			else
				this.$targetElement = jQuery(this.attrs.dataRemoteElementSelector);
		}
	}

	private getTargetPosition(): void {
		this.targetPosition = this.$targetElement.offset().top + (this.dataElementOffset * this.$targetElement[0].clientHeight);
	}

	private bindEvents(): void {
		this.$window.on('resize.' + this.EVENT_NAMESPACE, this.onResize.bind(this));
		this.$window.on('scroll.' + this.EVENT_NAMESPACE, this.onScroll.bind(this));
	}

	private unbindEvents(): void {
		this.$window.off('resize.' + this.EVENT_NAMESPACE);
		this.$window.off('scroll.' + this.EVENT_NAMESPACE);
	}

	private onResize(): void {
		this.getTargetPosition();
	}

	private onScroll(): void {
        this.updateFromPosition();
	}

	private addClass(delay: number = 0): void {
		setTimeout(() => {
			this.$targetElement.addClass(this.attrs.className);
		}, delay);
		this.unbindEvents();
	}
}