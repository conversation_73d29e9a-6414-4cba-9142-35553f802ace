/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { Tools } from '@common/ts/services/Tools.service';
import { OriginRatioChecker } from '../services/OriginRatioChecker.service';
import { BundledChannelManager } from '@common/ts/services/BundledChannelManager';
import { ChannelsService } from '@eboxtv/js/services/channels.service';
import { OrderOnlineService } from "@eboxtv/js/services/orderOnline.service";
import { PromotionsService } from '../services/promotions.service';
import { CoreTools } from '@core/helpers';
/**
 * Allows the user to select channels from the UI. This directive will
 * add remove the ID of its corresponding channel into the @see TVOrderService.selectedChannels
 * property and will also subscribe for change using @see TVOrderService.selectedChannelsChange$
 * BehaviorSubject.
 * 
 * IMPORTANT! See 'NOTE FOR DEPENDING DIRECTIVES' at begining of TVOrderService file.
 * {@link '../services/TVOrder.service.js'}
 */
export class ChannelSelectDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[channel-select]';
	static IN_BUNDLE_HIDE_TYPE = 'hidden';

	id = 'channelSelect__' + Tools.generateRdmString({length: 16});

	
	// PRIVATE PROPERTIES //
	/**
	 * @private
	 * Subscription to the TVOrderService.selectedChannelsChange$ BehaviorSubject.
	 */
	_channelChangeSubscription;

	_bubbleInstances = [];

	_BCManager = BundledChannelManager.getInstance();
	_promotionService = PromotionsService.getInstance();
	// PUBLIC PROPERTIES //
	/**
	 * @public
	 * @type JQuery<HTMLElement> Must be a checkbox.
	 * Checkbox element to reflect the directive's value.
	 */
	$valueElement;


	/**
	 * @public
	 * @type {JQuery<HTMLElement>}
	 */
	$triggerElement = jQuery('.channel__logo-container', this.host);
	$wrapperElement = jQuery('.channel__logo-container', this.host);
	$liParent = this.$host.parents('li');
	$bubbleTrigger = jQuery('.Bubble__add-btn', this.$host);

	_$TVOrder = TVOrderService.getInstance();
	$ratioChecker = OriginRatioChecker.getInstance();
	$channels = ChannelsService.getInstance();

	_$orderOnline = OrderOnlineService.getInstance();

	_isFromBundleValue = false;


	// GETTERS AND SETTERS //
	/**
	 * @public
	 * @type {boolean}
	 * Boolean value that will reflect whether or not the directive's channel
	 * is part of TVOrderService.selectedChannels IDs Array,
	 */
	get value() {
		return this.$valueElement.length ? this.$valueElement[0].checked : false;
	}
	set value(val) {
		this._updateValues(val);
	}

	isFromBundle;

	channelObject = this._$TVOrder.getChannelById(this.attrs.channelId);

	// ON INIT //
	constructor(host) {
		super(host, [
			'channel-selected-class',
			'change-value-of',
			'true-title',
			'false-title',
			'title',
			{name: 'channel-id',type: 'int'},
			{name: 'click-disabled',type: 'eval'},
			{name: 'channel-type', required: true},
			{name: 'is-dummy', type: 'eval', default: false},
			{name: 'allow-bubble-trigger', type: 'eval', default: false},
			'channel-origin'
		]);

		// Initializing the directive's core methods
		this._init();
	}
	_init() {
		if (this.attrs.isDummy) {
			this._setDummyClass();
			return;
		}
		this._cacheDOM();
		this._bindEvents();
		
		if (this.channelObject && !this.attrs.title)
			this.attrs.title = this.channelObject.post_title;
	}

	_setDummyClass() {
		this.$host.addClass('channel-select--dummy');
	}
	
	/**
	 * @public
	 * Once the app is completely bootstrapped, every directive gets to run this
	 * special method that MUST be called 'onAppInit'. This assures that every directives
	 * and services of the app are instantiated.
	 * 
	 * @return {void}
	 */
	onAppInit() {
		if (this.attrs.isDummy) return;
		
		this._resetValueChanger();
		this._updateValues(this.value);
		this._subscribeToChanges();
		this._subscribeToOrderChange();
		this._validateOriginRatio();
	}

	onAppDestroy() {
		this._unbindEvents();
	}


	// PUBLIC METHODS //
	/**
	 * @public
	 * Will add the directive's instance's channel-id in @see TVOrderService.selectedChannels Set.
	 * 
	 * @return {void}
	 */
	addToOrder() {
		if (this.attrs.channelType === 'pre-selected') return;
		this._$TVOrder.addChannel(this.attrs.channelId, this.attrs.channelType);
		this._$orderOnline.initCart();
	}

	/**
	 * @public
	 * Will remove the directive's instance's channel-id from @see TVOrderService.selectedChannels Set.
	 * 
	 * @return {void}
	 */
	removeFromOrder() {
		if (this.attrs.channelType === 'pre-selected') return;
		this._$TVOrder.removeChannel(this.attrs.channelId, this.attrs.channelType);
		this._$orderOnline.initCart();

	}
	

	// PRIVATE METHODS //
	_resetValueChanger() {
		if (this.$valueElement[0] && this.$valueElement[0].value)
			this.$valueElement[0].value = false;
	}
	/**
	 * @private
	 * Storing DOM elements.
	 * 
	 * @return {void}
	 */
	_cacheDOM() {
		if (this.attrs.changeValueOf)
			this.$valueElement = jQuery(this.attrs.changeValueOf, this.$host);
	}

	/**
	 * @private
	 * Binding events on DOM elements.
	 * 
	 * @return {void}
	 */
	_bindEvents() {
		if (!this.attrs.clickDisabled)
			this.$triggerElement.add(this.$bubbleTrigger).on(`click.${ this.id }`, this._onHostClick.bind(this));
		else if (this.attrs.allowBubbleTrigger && this.$bubbleTrigger.length)
			this.$bubbleTrigger.on(`click.${ this.id }`, this._onHostClick.bind(this));
	}

	_unbindEvents() {
		this.$triggerElement.off(`click.${ this.id }`);
	}

	/**
	 * @private
	 * Depending on the value of the 'val' parameter, this method will change
	 * all of the UI aspect in order to reflect a true or a false value.
	 * 
	 * @param {boolean} val Value to reflect to the UI.
	 * @return {void}
	 */
	_updateValues(val) {
		if (val)
			this._onTrueValue();
		else
			this._onFalseValue();

		if (this.$valueElement.length)
			this.$valueElement[0].checked = val;
	}

	_subscribeToChanges() {
		this._subscribeToOrderChange();
	}

	/**
	 * @private
	 * Subscribing to TVOrderService.selectedChannelsChange$ BehaviorSubject in order
	 * to react to any change on TVOrderService.selectedChannels.
	 * 
	 * @return {void}
	 */
	_subscribeToOrderChange() {
		if (this.attrs.isDummy) return;
		
		this._channelChangeSubscription = this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);
	}

	/**
	 * @private
	 * When TVOrderService.selectedChannels changes, this method will check if its 
	 * channel-id is part of the TVOrderService.selectedChannels Set.
	 * 
	 * @return {void}
	 */
	_onOrderChange() {
		if (this.channelObject === undefined) return;
		const isFromBundle = this._$TVOrder.isInBundle(this.attrs.channelId);
		const isSelected = this._isInselectedChannels(this.attrs.channelType) || isFromBundle;

		if (!isFromBundle && isSelected && (this.channelObject.status === false || this.channelObject.status == 0)) {
			this.removeFromOrder();
			return;
		} else {
			this.isFromBundle = isFromBundle;
			this.value = isSelected;
			this._validateOriginRatio();
		}
	}

	_validateOriginRatio() {
		const validation = this.$ratioChecker.ratioValidation;

		if (this.attrs.channelOrigin !== 'canada') {
			const { qtyBeforeInvalid } = validation;

			if (qtyBeforeInvalid >= 0.5 && !this.value)
				this._lowerOpacity(true);
			else
				this._resetOpacity(true);
				
		}
	}

	/**
	 * @private
	 * Runs when the user clicks on the directive's host. If the directive's
	 * instance channel-id is not in TVOrderService.selectedChannels, we'll add
	 * it. Otherwise, we'll remove it.
	 */
	_onHostClick() {

		if (this.isFromBundle) {
			let selectedBundle;

			selectedBundle = Array.from(this._$TVOrder.selectedBundles).map(bundleID => this.$channels.getBundleById(bundleID))[0];
			
			this._BCManager.alert(selectedBundle);
			return;
		}

		if (!this._isInselectedChannels(this.attrs.channelType)){
			this.addToOrder();
			
			if( jQuery("div[channel-id='" + this.attrs.channelId + "']").find('.channel__link-label').length > 0 ){
				if( CoreTools.lang === 'en' ){
					var linkedChannel = jQuery("div[channel-id='" + this.attrs.channelId + "']:eq(0)").find('.channel__link-label').text() + ' was added to your selection too';
				}else{
					var linkedChannel = jQuery("div[channel-id='" + this.attrs.channelId + "']:eq(0)").find('.channel__link-label').text() + ' a été ajouté à votre sélection aussi';
				}
			}else{
				var linkedChannel = '';
			}


			if( CoreTools.lang === 'en' ){
				this.srSpeak( this.channelObject.post_title + ' was added to your selection. ' + linkedChannel, 'assertive' );
			}else{
				this.srSpeak( this.channelObject.post_title + ' a été ajouté à votre sélection. '  + linkedChannel, 'assertive' );
			}
		}else{
			this.removeFromOrder();

			if( jQuery("div[channel-id='" + this.attrs.channelId + "']").find('.channel__link-label').length > 0 ){
				if( CoreTools.lang === 'en' ){
					var linkedChannel = jQuery("div[channel-id='" + this.attrs.channelId + "']:eq(0)").find('.channel__link-label').text() + ' was removed from your selection too';
				}else{
					var linkedChannel = jQuery("div[channel-id='" + this.attrs.channelId + "']:eq(0)").find('.channel__link-label').text() + ' a été retiré de votre sélection aussi';
				}
			}else{
				var linkedChannel = '';
			}

			if( CoreTools.lang === 'en' ){
				this.srSpeak( this.channelObject.post_title + ' was removed from your selection. ' + linkedChannel, 'assertive' );
			}else{
				this.srSpeak( this.channelObject.post_title + ' a été retiré de votre sélection. ' + linkedChannel, 'assertive' );
			}
		}
	}

	/**
	 * @private
	 * We tell wheter the directive's instance's channel-id is part of the
	 * TVOrderService.selectedChannels Set or not.
	 * 
	 * @return {boolean}
	 */
	_isInselectedChannels(type) {
		if (type === 'pre-selected') type = 'a-la-carte';

		try {
			if (type === 'a-la-carte') {
				return this._$TVOrder._selectedChannels[type].has(this.attrs.channelId) ||
					this._$TVOrder._selectedChannels['others'].has(this.attrs.channelId);
			} else {
				return this._$TVOrder._selectedChannels[type].has(this.attrs.channelId);
			}
			
		} catch(e) {
			return this.isFromBundle;
		}
	}

	/**
	 * @private
	 * Will apply necessary DOM changes in order to reflect a TRUE VALUE.
	 * 
	 * @return {void}
	 */
	_onTrueValue() {
		let hostTitle;

		if (!this.attrs.isDummy && this.attrs.trueTitle)
			hostTitle = this.attrs.trueTitle || this.attrs.title;
		else
			hostTitle = this.channelObject.post_title;
		
		this.$host.addClass(this.attrs.channelSelectedClass);
		this.$wrapperElement.attr('title', hostTitle);
	}

	/**
	* @private
	* Will apply necessary DOM changes in order to reflect a FALSE VALUE.
	* 
	* @return {void}
	*/
	_onFalseValue() {
		let hostTitle;

		if (!this.attrs.isDummy && this.attrs.falseTitle)
			hostTitle = this.attrs.falseTitle || this.attrs.title;
		else
			hostTitle = this.channelObject.post_title;

		this.$host.removeClass(this.attrs.channelSelectedClass);
		this.$wrapperElement.attr('title', hostTitle);
	}

	_showParent() {
		if (!this.$liParent.length) return;

		if (ChannelSelectDirective.IN_BUNDLE_HIDE_TYPE === 'opacity')
			this._resetOpacity();
		else
			this.$liParent.show();
	}

	_hideParent() {
		if (!this.$liParent.length) return;

		if (ChannelSelectDirective.IN_BUNDLE_HIDE_TYPE === 'opacity')
			this._lowerOpacity();
		else
			this.$liParent.hide();
	}

	_lowerOpacity(logoOnly = false) {
		const $el = logoOnly ? this.$wrapperElement : this.$liParent;
		$el.css({opacity: 0.5});
	}

	_resetOpacity(logoOnly = false) {
		const $el = logoOnly ? this.$wrapperElement : this.$liParent;
		$el.css({opacity: 1});
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}