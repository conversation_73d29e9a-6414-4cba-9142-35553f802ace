.TVForfaitItem {
	display: block;

	&--is-recommended {
		.TVForfaitItem {
			&__row {
				&--title {
					background: url('~@common/img/ebox-texture-background--red.jpg');
				}
			}

			&__promo-banner {
				background-image: url('~@common/img/banner-bg.svg');
			}
		}
	}
	
	&__wrapper {
		border-radius: 12px;
		box-shadow: 0 2px 6px 0 rgba(#000, 0.6);
	}
	
	&__main-container {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		background: #FFF;
		border-radius: 12px 12px 0 0;
	}

	&__row {
		display: flex;
		flex: 1 0 auto;
		flex-wrap: wrap;
		padding: 26px 0;
		
		&--title {
			flex: 0 0 20%;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-top-left-radius: 12px;
			color: #FFF;
			background: url('~@common/img/ebox-texture-background--black.jpg');
		}

		&--title-special {
			background: #626262;
		}

		&--content {
			flex: 0 0 80%;
		}

		@media (max-width: 991px) {
			padding: 20px 0;

			&--title {
				flex: 0 0 100%;
				border-radius: 12px 12px 0 0;
				flex-direction: row;
				padding: 8px 0;
			}

			&--content {
				flex: 0 0 100%;
			}
		}
		@media (max-width: 699px) {
			&--content {
				padding: 0 20px;
			}
		}
		@media (max-width: 460px) {
			&--title {
				flex-direction: column;
			}
		}
	}

	&__channels {
		@media (max-width: 991px) {
			margin-right: 18px;
		}
		@media (max-width: 460px) {
			margin-right: 0;
		}
	}

	&__channel {
		&-count, &-word {
			vertical-align: middle;
		}

		&-count {
			font: 600 40px/48px $ebtv-f-primary;

			@media (max-width: 991px) {
				font: 600 30px/40px $ebtv-f-primary;
			}
			@media (max-width: 359px) {
				font: 600 24px/32px $ebtv-f-primary;
			}
		}

		&-word {
			font: 400 16px/24px $ebtv-f-primary;
		}
	}

	&__title {
		font: 600 18px/20px $ebtv-f-primary;
		text-transform: uppercase;
		text-align: center;
		padding: 0 10px;

		@media (max-width: 991px) {
			position: relative;
			top: 2px;
			font: 600 14px/16px $ebtv-f-primary;
		}
		@media (max-width: 359px) {
			font: 600 12px/14px $ebtv-f-primary;
		}
	}

	&__features {
		display: flex;
		flex-wrap: wrap;
		margin: 0;
		list-style: none;
		flex: 0 0 74%;

		@media (max-width: 699px) {
			flex: 0 0 100%;
			padding: 20px 0;
		}
	}

	&__feature {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 0 0 33.3333%;
		text-align: center;
		padding: 0 5%;
		border-right: 1px solid $c-light-grey;

		@media (max-width: 1100px) {
			padding: 0 15px;
		}

		&:last-child {
			border-right: none;

			.TVForfaitItem {
				&__feature-plus-icon {
					display: none;
				}
			}
		}

		&--is-single {
			flex: 0 0 100%;
			text-align: left;
			align-items: flex-start;
		}

		&--full-flex {
			flex: 1 1 33.3333%;
		}

		&-title {
			font: 700 20px/24px $ebtv-f-primary;
		}
		&-sub-title {
			font: 400 12px/16px $ebtv-f-primary;
			color: $c-grey;
		}

		&-icon {
			display: block;
			margin: 0 auto 15px;
			width: 50px;
			height: 50px;
		}

		&-channels {
			margin: 15px 0 0;
			font-size: 0;
		}

		&-channel-item {
			display: inline-block;
			margin-right: 16px;

			&:last-child {
				margin-right: 0;
			}
		}

		&-channel-img {
			display: inline-block;
			width: 32px;
			height: 32px;
		}

		&-plus-icon {
			position: absolute;
			display: inline-block;
			width: 24px;
			height: 24px;
			fill: $c-primary;
			top: 50%;
			right: 0;
			margin: -22px -14px 0 0;
			background: #FFF;
			padding: 10px 0;
			box-sizing: content-box;
		}

		@media (max-width: 991px) {
			&-title {
				font: 700 16px/22px $ebtv-f-primary;
			}

			&-sub-title {
				font: 400 12px/16px $ebtv-f-primary;
				color: $c-grey;
			}

			&-plus-icon {
				width: 16px;
				height: 16px;
				margin: -13px -9px 0 0;
				padding: 4px 0;
			}
		}
		@media (max-width: 699px) {
			flex: 0 0 50%;

			&:nth-child(1):nth-last-child(3) {
				flex: 0 0 100%;
				border: none;
				align-items: flex-start;
				text-align: left;
				padding: 0 0 20px;

				.TVForfaitItem {
					&__feature-plus-icon {
						display: none;
					}
				}
			}

			&-channel-item {
				&:nth-child(2):nth-last-child(2) {
					margin-right: 0;
				}

				&:nth-child(n+3) {
					display: none;
				}
			}

			&--is-single {
				flex: 0 0 100%;
				padding: 0;

				.TVForfaitItem__feature-channel-item {
					display: inline-block;

					&:nth-child(n+7) {
						display: none;
					}
				}
			}
		}
		@media (max-width: 399px) {
			&:not(.TVForfaitItem__feature--full-flex) {
				&.TVForfaitItem__feature {
					&--is-single {
						.TVForfaitItem__feature-channel-item {
							&:nth-child(n+5) {
								display: none;
							}
						}
					}
				}
			}
		}
	}

	&__pricing {
		display: flex;
		justify-content: center;
		flex: 0 0 26%;
		height: 100%;
		margin-left: auto;
		border-left: 1px solid $c-light-grey;
		@media (max-width: 767px) {
			height: auto;
		}
		.ItemRecap {
			&:not(.ItemRecap--has-special-promo-image) {
				@include applyAtRoot('html:lang(en-CA)') {
					span {
						right: -13px;
						top: 31px;
					}
				}
			}
		}

		@media (max-width: 699px) {
			flex: 0 0 100%;
			border-top: 1px solid $c-light-grey;
			border-left: none;
			padding: 22px 0;
		}
	}

	&__promo-banner {
		position: absolute;
		right: -25px;
		top: 10px;
		width: 109px;
		height: 27px;
		text-align: center;
		background: {
			image: url('~@common/img/banner-bg--red.svg');
			position: center;
			size: 100% 100%;
			repeat: no-repeat;
		};
		color: $c-white;
		transform: rotate(41deg);
		font: 600 12px/27px $ebtv-f-primary;

	}
	
	&__toggle {
		position: relative;
		color: #FFF;
		border-radius: 0 0 12px 12px;
		height: 36px;
		overflow: hidden;
		background: url('~@common/img/ebox-texture-background--black.jpg');

		&--opened {
			.TVForfaitItem__toggle-title-icon {
				transform: rotate(180deg);
			}
		}
		
		&-head {
			position: relative;
			background: url('~@app/img/black-pattern.png');
			text-align: center;
			padding: 9px 0;
			cursor: pointer;
			border-radius: 0 0 12px 12px;
			z-index: 1;
		}

		&-title, &-title-icon {
			display: inline-block;
			vertical-align: middle;
			user-select: none;
		}

		&-title-icon {
			display: inline-block;
			width: 20px;
			height: 20px;
			fill: currentColor;
			margin-left: 5px;
		}

		&-body {
			margin-top: -36px;
			padding: 96px 40px 60px;
			border-radius: 0 0 12px 12px;
			box-shadow: inset 0 1px 26px 0 rgba(0,0,0,0.60);

			@media (max-width: 768px) {
				padding: 76px 40px 60px;
			}
			@media (max-width: 500px) {
				padding: 66px 20px 30px;
			}
		}
	}

	&__recap {
		display: flex;
		flex: 0 0 auto;
		padding: 0;
		justify-content: center;
		

		@media (max-width: 991px) {
			border-top: none;
		}

		.Listing {
			&__apartir {
				margin-left: 0;
			}

			&__prix {
				color: $c-primary;
				margin: 0;
	
				&--promo {
					color: $c-black;
					display: block;
					margin-right: 6px;

					&:after {
						opacity: 1;
					}
				}

				&--no-promo {
					display: none;

					+ .Listing__prix {
						color: $c-black;
					}
				}
			}
		}
	}

	&__channel-list-container {
		margin-bottom: 70px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	&__channel-list.TVForfaitItem__channel-list {
		margin: -20px;

		@media (max-width: 400px) {
			margin: -10px;
		}
	}

	&__channel-list-title {
		margin-bottom: 20px;
	}

	&__channel-list-item.TVForfaitItem__channel-list-item {
		margin: 20px !important;
		width: auto !important;
		padding-bottom: 0;

		@media (max-width: 400px) {
			margin: 10px !important;
		}

		.channel {
			padding-bottom: 0;

			&__logo-container {
				width: 65px;
				height: 65px;
			}

			&__add-overlay {
				display: none;
			}
		}
	}

	&__specific-note {
		margin-top: 15px;
	}

	.ItemRecap__price-wrapper {
		min-width: 155px;
	}
	
}

.page-template-commande-tv-forfait-tele {
	.TVForfaitItem {
		.ItemRecap__from,
		.ButtonEffect {
			display: none;
		}
	}
}

@media (min-width: 1024px){
	.tv-container{
		.TVForfaitItem__wrapper{
			.TVForfaitItem__main-container{
				.TVForfaitItem__row--content{
					.radioTV{
						>.TVForfaitItem__row--content{
							@media (max-width: 1200px){
								padding: 0;

							}
							.radio__radio{
								@media (max-width: 1200px){
									display: none;

								}
							}

							.TVForfaitItem__features{
								@media (max-width: 1200px){
									flex:0 0 100% ;
									padding: 20px 0;
								}
							}
							.TVForfaitItem__feature:last-child{
								align-items: center;
							}
							.TVForfaitItem__pricing{
								@media (max-width: 1200px){
									height: auto;
									flex:0 0 100% ;
									border-left: none;
									border-top: 1px solid #C7C7C7;
									padding: 20px 0 0 0;
								}
								.ButtonEffect {
									@media (max-width: 1200px){
									display: block;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
.TVForfaitItem__active{
	border: 2px solid $c-primary !important;

}