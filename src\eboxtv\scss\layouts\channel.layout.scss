.channel {
    position: relative;
    display: inline-block;
    padding-bottom: 10px;

    &--unavailable {
        cursor: default;

        .channel {
            &__logo-container {
                cursor: default;
                opacity: 0.6 !important;
                filter: saturate(0) !important;
            }
        }

        .Bubble {
            &__add-btn {
                display: none
            }
        }
    }

    .Bubble {
        &__add-container {
            &--add {
                display: block;
            }

            &--remove {
                display: none;
            }
        }
    }

    &.channel-select {
        &--dummy {
            .Bubble__add-btn {
                display: none !important;
            }
        }
    }

    &--selected {
        .channel {
            &__logo-container {
                border: 4px solid $c-primary;
            }

            &__add-overlay {
                display: flex;
                background: $c-primary;
                left: auto;
                width: 17px;
                height: 17px;
                left: auto;
                top: 0;
                right: 0;
                opacity: 1;
                border-radius: 0 0 0 5px;
                transition: none;
            }

            &__logo {
                // filter: saturate(1);
                // transition: filter 0.25s ease-out;
            }
            
            &__action-icon {
                &--add {
                    display: none;
                }

                &--check {
                    display: block;
                }
            }

            &__add-title {
                display: none;
            }
        }

        .channel-list__link-icon {
            margin-left: 5%;
        }

        .Bubble {
            &__add-container {
                &--add {
                    display: none;
                }

                &--remove {
                    display: block;
                }
            }
        }
    }

    &--disabled {
        cursor: default;
        
        .channel {
            &__logo-container {
                cursor: default;
                pointer-events: none;
                &:hover {
                    .channel__add-overlay {
                        opacity: 0;
                    }
                }
            }
        }
    }

    &__logo-container {
        position: relative;
        border: 1px solid $c-grey-faded;
        border-radius: 8px;
        background: #FFF;
        appearance: none;
        outline: none;
        width: 90px;
        height: 90px;
        cursor: pointer;
        margin-bottom: 10px;
        transition-delay: 0s;
        transition: all 0.1s linear;

        &:hover {
            border-color: $c-primary;

            .channel {
                &__add-overlay {
                    opacity: 1;
                }
            }
        }

        &--has-price {
            $path: &;
            @at-root .channel:not(.channel--selected) {
                #{ $path } {
                    .channel {
                        &__logo-wrapper {
                            height: 68%;
                            position: static;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        &__logo {
                            position: relative;
                            left: auto;
                            top: auto;
                            transform: none;
                        }

                        &__price-container {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    }
                }
            }
        }
    }

    &__logo-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    &__banner {
        position: absolute;
        top: 7px;
        left: -6px;
        width: 100%;
        z-index: 2;
        color: #FFF;

        &--premium {
            .channel {
                &__banner-wrapper {
                    background: $c-primary;
                }
            }
        }
    }

    &__banner-wrapper {
        position: relative;
        width: 100%;
        padding: 3px 6px;
        box-sizing: content-box;
        background: #343434;
        font: 600 14px/20px $ebtv-f-primary;
        text-align: center;

        &:before, &:after {
            content: '';
			position: absolute;
			display: block;
			top: 100%;
			width: 0;
			height: 0;
			border-style: solid;
        }

        &:before {
            left: 0;
            border-width: 0 6px 6px 0;
            border-color: transparent #1F1F1F transparent transparent;
        }

        &:after {
            right: 0;
            border-width: 6px 6px 0 0;
            border-color: #1F1F1F transparent transparent transparent;
        }
    }

    &__logo {
        position: absolute;
        width: 60%;
        height: auto;
        top: 50%;
        left: 50%;
        user-select: none;
        transform: translate(-50%, -50%);
        // filter: saturate(0);
        transition: none;
        font-size: 10px;
        color: #000;
    }

    &__add-overlay {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        top: -1px;
        left: -1px;
        width: 100%;
        height: 100%;
        z-index: 1;
        background: $c-primary;
        color: $c-white;
        opacity: 0;
        box-sizing: content-box;
        padding: 1px;
        border-radius: 7px;
        transition: background 0.1s linear;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            width: 17px;
            height: 17px;
            left: auto;
            top: 0;
            right: 0;
            opacity: 1;
            border-radius: 0 6px 0 8px;
            background: $c-grey-faded;
            transition: none;
        }
    }

    &__action-icon {
        position: relative;
        display: block;
        width: 16px;
        height: 16px;
        fill: currentColor;
        z-index: 0;

        &--check {
            display: none;
        }

        // @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            width: 10px;
            height: 10px;
        // }
    }

    &__add-title {
        font-family: $ebtv-f-primary;
        font-size: 12px;
        color: currentColor;
        font-weight: 400;
        margin: 0;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            display: none;
        }
    }

    &__add-checkbox {
        position: absolute;
        visibility: hidden;
    }

    &__price-container {
        position: absolute;
        display: none;
        bottom: -1px;
        left: -1px;
        background: $c-black;
        width: 100%;
        height: 32%;
        text-align: center;
        border-radius: 0 0 8px 8px;
        box-sizing: content-box;
        padding: 0 1px 1px;
    }

    &__price {
        display: table-cell;
        vertical-align: middle;
        font: 400 16px/22px $ebtv-f-primary;
        color: $c-white;
    }

    &__details {
        position: absolute;
        left: 50%;
        bottom: 0;
        margin-bottom: 30px;
        opacity: 0;
        will-change: transform, opacity;
        z-index: 10;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            margin-bottom: 10px;
        }
        @media (max-width: 500px) {
            left: 0;
            transform: translate(0)!important;
        }
    }

    &__details-btn {
        font: 700 18px/20px $ebtv-f-primary;
        color: $c-light-grey;
        cursor: pointer;
        vertical-align: middle;
        text-align: center;
        z-index: 2;

        @media (min-width: #{ map-get($breakpoints, tabletLandscape) + 1px }) {
            &:hover {
                color: $c-primary;
            }
        }
        
        @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
            font: 700 14px/20px $ebtv-f-primary;
        }
    }

    &__details-btn-icon {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        fill: currentColor;
        bottom: 2px;
    }

    @media (max-width: 500px) {
        position: inherit;
    } 
}