.page-template-page-landing-demenagement {
	.Li<PERSON>Header {
		@media (max-width: 1600px) {
			padding: 15px 0;
		}
	}

	.Demenagement {
		&__title {
			text-align: left;
			padding-left: 60px;

			.is-doodled--underlined {
				&:after {
					height: 11%;
				}
			}

			@media (max-width: 1075px) {
				.is-doodled--pointed {
					&:before {
						bottom: 30%;
					}
				}
			}
			@media (max-width: 800px) {
				font-size: 25px;
				.is-doodled--pointed {
					&:before {
						bottom: 0;
					}
				}
			}

			@media (max-width: 800px) {
				padding-left: 15px;
				padding-right: 15px;
				text-align: center;
			}
		}

		&__col {
			&--left {
				.Demenagement__col-wrapper {
					padding-bottom: 135px;
				}

				@media (max-width: 1600px) {
					padding-top: 75px;

					.Demenagement__col-wrapper {
						padding-bottom: 115px;
					}
				}

				@media (max-width: 1075px) {
					.Demenagement__col-wrapper {
						padding-bottom: 45px;
					}
				}
				@media (max-width: 1075px) {
					.Demenagement__col-wrapper {
						padding-bottom: 0;
					}
				}
			}

			&--right {
				.Demenagement__col-wrapper {
					padding-bottom: 45px;
				}
			}
		}

		&__box-svg {
			left: 99px;

			@media (max-width: 1600px) {
				bottom: auto;
			}
			@media (max-width: 1075px) {
				display: none;
			}
		}

		@media (max-width: 1024px) {
			background: {
				image: url('../../img/red-pattern.png');
				size: auto;
				repeat: repeat;
			};
		}
	}

	.BandeFormulaire {
		&__right {
			p {
				font: 900 34px/40px $f-primary;
			}
		}
	}

	.landing-demenagement {
		&__title-description {
			font: 700 24px/30px $f-primary;
			max-width: 635px;
			text-transform: none;
			text-align: left;
			margin-top: 13px;

			@media (max-width: 1075px) {
				font: 700 18px/24px $f-primary;
			}
			@media (max-width: 800px) {
				text-align: center;
			}
		}
	}

	.gfield {
		margin-bottom: 40px !important;
		&:last-child {
			margin-bottom: 0 !important;
		}

		@media (max-width: 800px) {
			margin-bottom: 30px !important;
		}
	}

	.material-input-container {
		margin-bottom: 0 !important;
	}

	.gform_wrapper .gform_ajax_spinner {
		padding-left: 10px;
		top: 50%;
		position: absolute;
		transform: translateY(-50%);
	}
}