/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { TVOrderService } from '@eboxtv/js/services';
import { CookiesService } from '@common/ts/services/Cookies.service';

export class BoutonCommandeDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()
    _$tvOrder = TVOrderService.getInstance();

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-bouton-commande]';

	$form;
	$type = "";
    dataInternet = {
        forfait: "",
        technology: "",
        planPrice: "",
        title: '',
        downloadSpeed : '',
        prixADSL : 0
    };

    dataTelephonie = {
        "type":"",
        "planCode":"",
        "lineCode":"",
        "planPrice":""
    };

    dataTele = [];

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {

        this.type = this.host.getAttribute('data-type');

        this.host.addEventListener('click', function(evt){
            this.saveToLocalStorage(evt);
        }.bind(this))

	}

    saveToLocalStorage(evt){  
        this.$form = this.host.parentElement;
        if(this.type == "internet"){
            this.dataInternet.technology = this.host.getAttribute('data-fournisseur');   
            this.dataInternet.forfait = this.host.getAttribute('data-sku');  
            this.dataInternet.planPrice = this.host.getAttribute('data-price');  
            this.dataInternet.title = this.host.getAttribute('data-titre-internet');
            this.dataInternet.downloadSpeed = this.host.getAttribute('data-downloadSpeed');
            this.dataInternet.prixADSL = this.host.getAttribute('data-promoadsl');

            this._$OrderService.saveInternetService(this.dataInternet); 
        }
        if(this.type == 'telephonie'){
                this.dataTelephonie.type = this.host.getAttribute('data-titre');
                this.dataTelephonie.planPrice = this.host.getAttribute('data-price');
    
                if(this.dataTelephonie.type == 'Ligne standard' || this.dataTelephonie.type == 'Standard line'){
                    this.dataTelephonie.lineCode = this.host.getAttribute('data-sku');
                }else{
                    this.dataTelephonie.planCode = this.host.getAttribute('data-sku');
                    this.dataTelephonie.lineCode = this.host.getAttribute('data-sku2');
                }



            this._$OrderService.savePhoneService(this.dataTelephonie);
        }
        if(this.type == 'forfait'){

            // Internet
            this.dataInternet.technology = this.host.getAttribute('data-fournisseur');   
            this.dataInternet.forfait = this.host.getAttribute('data-internet-sku');  
            this.dataInternet.planPrice = this.host.getAttribute('data-internet-price');  
            this.dataInternet.downloadSpeed = this.host.getAttribute('data-internet-vitesse');
            this.dataInternet.prixADSL = this.host.getAttribute('data-promoadsl');

            // Telephonie
            if(this.host.getAttribute('data-telephone-titre')){
                this.dataTelephonie.type = this.host.getAttribute('data-telephone-titre');
                this.dataTelephonie.planPrice = this.host.getAttribute('data-telephone-price');
    
                if(this.dataTelephonie.type == 'Ligne standard' || this.dataTelephonie.type == 'Standard line'){
                    this.dataTelephonie.lineCode = this.host.getAttribute('data-telephone-sku');
                }else{
                    this.dataTelephonie.planCode = this.host.getAttribute('data-telephone-sku');
                    this.dataTelephonie.lineCode = this.host.getAttribute('data-telephone-sku2');
                }
            }
            else{
                this.dataTelephonie=  {};
            }


            if(this.host.getAttribute('data-tele') != ""){
                this.dataTele = this.host.getAttribute('data-tele');
                const expirationDate = new Date();
				expirationDate.setDate(expirationDate.getDate() + 3);

				CookiesService.setCookies({
					name: 'hasTV',
					value: true,
					expires: expirationDate
				});

                // Cookie pour savoir que l'utilisateur à selectionné une option
                CookiesService.setCookies({
                    name: 'isTvPackageSelected',
                    value: true
                });
            }

            this._$OrderService.savePhoneService(this.dataTelephonie);
            this._$OrderService.saveInternetService(this.dataInternet); 
            this._$tvOrder.addBundle(this.dataTele);

        }
    }

}


