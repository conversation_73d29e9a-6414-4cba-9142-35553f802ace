import { Directive } from '@core/directive.js';


/**
 * This Directive is used to filter certain sections in the DOM according
 * to its host's children checkboxes.
 */
export class FilterSectionsDirective extends Directive {
	//#region Static properties
	public static selector: string = 'form[filter-sections]';

	/* Name of the attribute that filtered sections must have. The value inside
	this attribute will determine if whether or not the section is shown by comparing
	it with the value of the checked filter checkboxes. */
	public static readonly SECTION_ATTR_NAME: string = 'section-filter-id';
	//#endregion
	
	//#region this.attrs types
	public attrs: {
		// Value of the name attribute of the child checkboxes
		filtersName: string;
	};
	//#endregion
	
	//#region Private properties
	private filteredSections: NodeListOf<HTMLElement> = document.querySelectorAll(
		'[section-filter-id]'
	);
	private $checkBoxes: JQuery<HTMLInputElement> = jQuery(
		`input[type="checkbox"][name="${ this.attrs.filtersName }"]`,
		this.host
	) as J<PERSON><PERSON><PERSON><HTMLInputElement>;
	//#endregion
	
	//#region Getters
	private get filterValues(): string[] {
		const checkboxesArray: HTMLInputElement[] = this.$checkBoxes.toArray();

		return checkboxesArray
			.filter(checkbox => checkbox.checked)
			.map(checkbox => checkbox.value);
	}
	//#endregion

	//#region On init
	constructor(host: HTMLFormElement) {
		super(host, [
			{name: 'filters-name', required: true}
		]);
		this.onInit();
		this.onCheckboxesChange();
	}

	private onInit(): void {
		this.bindEvents();
	}

	private bindEvents(): void {
		this.$checkBoxes.on('change', this.onCheckboxesChange.bind(this));
	}
	//#endregion
	
	//#region Private methods
	private onCheckboxesChange(): void {
		const filterValues: string[] = this.filterValues;

		if (filterValues.length > 0)
			this.showFilteredOnly(filterValues);
		else
			this.showAllSections();
	}

	private showFilteredOnly(filterValues: string[] = this.filterValues): void {
		for (let i: number = 0; i < this.filteredSections.length; i++) {
			const section: HTMLElement = this.filteredSections[i];
			const sectionFilterID: string = section.getAttribute(FilterSectionsDirective.SECTION_ATTR_NAME);

			if (filterValues.indexOf(sectionFilterID) === -1)
				section.style.display = 'none';
			else if (section.style.display === 'none')
				section.style.display = '';
		}
	}

	private showAllSections(): void {
		for (let i: number = 0; i < this.filteredSections.length; i++) {
			const section: HTMLElement = this.filteredSections[i];
			
			if (section.style.display === 'none')
				section.style.display = '';
		}
	}
	//#endregion
}