import './fall-on-load.directive.scss';
import { TweenLite } from 'gsap/TweenLite';
import * as CustomEase from '@common/js/resources/gsap/CustomEase.min';
import { Directive } from '@core/directive';

export class FallOnLoadDirective extends Directive {
	static selector: string = '[fall-on-load]';
	static get CUSTOM_EASE_PATH(): string {
		return 'M0,0 C0.321,0 0.688,1 0.688,1 0.688,1 0.798,0.976 0.83,0.976 0.862,0.976 0.987,0.976 1,1';
	}

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'fall-delay', type: 'eval', default: 1},
			'on-completed-class-name'
		]);
		this.init();
	}
	
	private init(): void {
		this.createCustomEasing();
		this.landHost();
	}

	public landHost(): void {
		TweenLite.fromTo(this.host, 0.4, {
			y: -400,
			alpha: 0,
		}, {
			y: 0,
			alpha: 1,
			ease: 'fallBounce',
			delay: this.attrs.fallDelay,
			onComplete: () => {
				if (this.attrs.onCompletedClassName)
					this.$host.addClass(this.attrs.onCompletedClassName);
			}
		});
	}

	private createCustomEasing(): void {
		CustomEase.create('fallBounce', FallOnLoadDirective.CUSTOM_EASE_PATH);
	}
}