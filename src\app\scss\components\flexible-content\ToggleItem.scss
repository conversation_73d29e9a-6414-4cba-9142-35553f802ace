.ToggleItem {
    width: auto;
    margin: 0 0 40px;
    border: 1px solid $c-primary;
    border-radius: 5px;
    &__title {
        button {
            background: none;
            border: 0;
            &:focus {
                outline: 2px solid blue;
                outline-offset: 2px;
            }
        }
        width: 100%;
        padding: 20px;
        position: relative;
        cursor: pointer;
        transition: all 0.3s $cubic;
        display: table;
        h2 {
            font-size: 27px;
            line-height: 30px;
            font-weight: 700;
            color: $c-primary;
            transition: all 0.3s $cubic;
            margin: 0;
            padding: 0 24px 0 0;
            display: table-cell;
            text-align: left !important;
            vertical-align: middle;
            background: url('~@app/img/ic_arrow-down-faq.svg') no-repeat right center / 26px 14px;
            height: 30px;
            text-transform: none;
            @media (max-width: 991px) {
                font-size: 23px;
            }
            @media (max-width: 767px) {
                font-size: 20px;
            }
            button {
                text-align: left !important;
            }
        }
    }
    &.title--opened {
        .ToggleItem__title {
            background: $c-primary;
            h2 {
                background: url('~@app/img/ic_arrow-top-faq.svg') no-repeat right center / 26px 14px;
                color: $c-white;
                text-align: left !important;
            }
            
        }
    }
    &__content {
        padding: 20px;
        p {
            font-family: $f-primary;
            font-size: 18px;
            line-height: 24px;
            margin: 0 0 20px;
            &:last-child {
                margin: 0;
            }
        }
    }
    &:last-child {
        margin: 0;
    }
}
.toggleitem__content {
    padding: 26px!important;
}