/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { BubbleService } from './_bubble/_bubble.service';
import { Tools } from '@common/ts/services/Tools.service';
import { ChannelSelectDirective } from '@eboxtv/js/directives';
import { OrderOnlineService } from '@eboxtv/js/services/orderOnline.service';
import { CoreTools } from '@core/helpers';

/**
 * IMPORTANT! See 'NOTE FOR DEPENDING DIRECTIVES' at begining of TVOrderService file.
 * {@link '../services/TVOrder.service.js'}
 */
export class ToggleAllChannelSelectDirective extends Directive {
	static selector = 'input[type="checkbox"][toggle-all], input[type="checkbox"][data-toggle-all]';

	/**
	 * @type {ChannelSelectDirective[]}
	 */
	_childrenDirectives = [];
	$trigger = this.attrs.triggerSelector !== null ? jQuery(this.attrs.triggerSelector) : null;
	_$TVOrder = TVOrderService.getInstance();
	_$orderOnline = OrderOnlineService.getInstance();
	get value() {
		return this.host.checked;
	}
	set value(val) {
		if (this.attrs.sectionType !== 'pre-selected') {
			this.setAllChildDirectivesValue(val);
		} else {
			let action;

			if (val)
				action = this._$TVOrder.addBundle(this.attrs.bundleID);
			else
				action = this._$TVOrder.removeBundle(this.attrs.bundleID);

			action
				.catch(this._onActionError.bind(this));

		}
		this._$orderOnline.initCart();
	}

	constructor(host) {
		super(host, [
			'toggle-all',
			'toggle-all-selector',
			'toggle-all-parent-selector',
			{name: 'toggle-all-section-type', as: 'sectionType', default: 'normal', accept: ['normal', 'pre-selected']},
			{name: 'toggle-all-bundle-id', as: 'bundleID', type: 'int', default: -1},
			{name: 'toggle-all-trigger-selector', as: 'triggerSelector'}
		]);
	}
	onAppInit() {
		this._cacheDOM();
		this._bindEvents();
		this._getChildrenDirectives();
		this._subscribeToOrderChange();
	}

	setAllChildDirectivesValue(val) {
		let tvOrderAction;
		const channels = this._childrenDirectives.map(channelDir => ({
			id: channelDir.attrs.channelId,
			type: this.attrs.sectionType === 'pre-selected' ? 'pre-selected' : channelDir.attrs.channelType
		}));

		if (val){
			tvOrderAction = this._$TVOrder.addMultipleChannels(channels);
			var titleSection = this.$hostParent.find('h4').text();
			if( CoreTools.lang === 'fr' ){
				this.srSpeak( titleSection + ' a été ajouté à votre sélection.', 'assertive');
			}else{
				this.srSpeak( titleSection + ' has been added to your selection.', 'assertive');
			}
		}else{
			tvOrderAction = this._$TVOrder.removeMultipleChannels(channels);
			var titleSection = this.$hostParent.find('h4').text();
			if( CoreTools.lang === 'fr' ){
				this.srSpeak( titleSection + ' a été retiré de votre sélection.', 'assertive');
			}else{
				this.srSpeak( titleSection + ' has been removed from your selection.', 'assertive');
			}
		}

		tvOrderAction
			.catch(this._onActionError.bind(this));

		
	}

	_cacheDOM() {
		this.$hostCheckboxes = jQuery(this.attrs.toggleAllSelector);
		this.$hostParent = this.$host.parents(this.attrs.toggleAllParentSelector);
	}

	_bindEvents() {
		if (this.$trigger && this.$trigger.length)
			this.$trigger.on('click', this._onTriggerClick.bind(this));

		this.$host.on('change', this._onHostChange.bind(this));
	}
	
	_getChildrenDirectives() {
		this.$hostCheckboxes.each((i, el) => {
			const directiveInstances = this._getDirectiveInstancesByName(el.directives, this.attrs.toggleAll);
			
			if (directiveInstances)
				this._childrenDirectives = this._childrenDirectives.concat(directiveInstances);
		});
	}

	_subscribeToOrderChange() {
		this.tvOrderChange$ = this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);
	}

	_onOrderChange() {
		let isAllChecked = true;
		for (let i = 0; i < this._childrenDirectives.length; i++) {
			const currentChildren = this._childrenDirectives[i];

			if (!currentChildren.value && currentChildren.channelObject.status !== false) {
				isAllChecked = false;
				break;
			}
		}

		this.host.checked = isAllChecked;

		if (this.attrs.toggleAllParentSelector === undefined) return;

		if (isAllChecked && this._childrenDirectives.length > 0){
			this.$hostParent.addClass('channel-list--selected');
		}else{
			this.$hostParent.removeClass('channel-list--selected');
		}
	}

	_onHostChange(evt) {
		this.value = this.host.checked;
	}

	_onTriggerClick(evt) {
		const isFromBubble = BubbleService.isFromBubbleElement(evt.target, (this.attrs.sectionType === 'normal'));
		const isHostSibling = Tools.areSiblings(evt.target, this.host);
		const targetIsParent = evt.target !== this.host.parentElement;

		/* Making sure we are not clicking in a bubble element inside the trigger element AND
		the target of the click event is not a sibling of the host (inside a <label/>) or
		is not an <label/> itself. Because if the click is on a <label/> element, the host 
		element will emit a change on click and the change will be made by _onHostChange method 
		automatically. */
		if (!isFromBubble && !isHostSibling && targetIsParent) {
			if (evt.target !== this.host)
				this.host.checked = !this.host.checked;

			this.$host.trigger('change');
		}
	}

	_getDirectiveInstancesByName(directivesObject, name) {
		return directivesObject[name];
	}

	/**
	 * @private
	 * If an error occured while trying to add channels, we reset the checkbox input
	 * since the action has been triggered by its 'change' event.
	 * @returns {void}
	*/
	_onActionError() {
		this.host.checked = !this.host.checked;
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}