import { Directive } from '@core/directive';
import { TVOrderService } from '@eboxtv/js/services';
import { CoreTools } from '@core/helpers';

export class ForfaitTitleDirective extends Directive {
	static selector: string = '[forfait-title]';

	private tvOrder: TVOrderService = TVOrderService.getInstance();

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'will-change', type: 'eval', default: true}
		]);
		this.onInit();
	}

	private onInit(): void {
		if (this.attrs.willChange) {
			this.tvOrder.selectedBundlesChange$.subscribe(
				this.updateForfaitTitle.bind(this)
			);
		} else {
			this.updateForfaitTitle();
		}
	}

	private updateForfaitTitle(): void {
		const currentForfait = this.tvOrder.getSelectedBundle();
		let title: string;

		if (currentForfait) {
			title = CoreTools.translate(currentForfait.name, currentForfait.nameEN);
		} else {
			CoreTools.translate('Aucun forfait sélectionné', 'No package selected');
		}

		this.host.innerText = title;
	}
}