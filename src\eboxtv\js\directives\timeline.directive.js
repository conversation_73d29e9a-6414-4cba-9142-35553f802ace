import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";

export class TimelineDirective extends Directive {
    _$OrderService = OrderOnlineService.getInstance();

    static selector = '[timeline]';

    currentStep = null;
    timeLineArray = [];
    timeLineArrayMobile = [];

    selectedClass = "timeline__token timeline__token--selected";
    selectedClassMobile = "steps steps--active";

    currentClass = "timeline__token timeline__token--current";
    currenClassMobile = "steps steps--current";

    // Internet elements
    internetStep = document.getElementById("internet");
    internetStepMobile = document.getElementById("internetMobile");
    internetStepTrigger = document.getElementById("internet-trigger");

    // Telephonie elements
    phoneStep = document.getElementById("phone");
    phoneStepMobile = document.getElementById("phoneMobile");
    phoneStepTrigger = document.getElementById("phone-trigger");

    // Television elements
    tvStep = document.getElementById("tv");
    tvStepMobile = document.getElementById("tvMobile");
    tvStepTrigger = document.getElementById("tv-trigger");

    // Infos personnelles elements
    infoStep = document.getElementById("info");
    infoStepMobile = document.getElementById("infoMobile");
    infoStepTrigger = document.getElementById("info-trigger");

    // Payment elements
    paymentStep = document.getElementById("payment");
    paymentStepMobile = document.getElementById("paymentMobile");

    isTvAvailable = typeof (this.tvStepTrigger) != 'undefined' && this.tvStepTrigger != null;

    // Il y a 2 étapes pour la TV, mais c'est le même icône sur la timeline. Faire attention.
    INTERNET = 1;
    PHONE = 4;
    TV = 2;
    TV2 = 3;
    INFO = 5;

    constructor(host) {
        super(host, []);
        this.currentStep = parseInt(host.attributes.stepId.value);
        this._onInit();
    }

    _onInit() {
        // Set onclick events
        this.internetStepTrigger.onclick = (event) => { this.changeCurrentStep(this.INTERNET, event); };
        this.phoneStepTrigger.onclick = (event) => { this.changeCurrentStep(this.PHONE, event); };
        if (this.isTvAvailable) {
            this.tvStepTrigger.onclick = (event) => { this.changeCurrentStep(this.TV, event); };
        }
        this.infoStepTrigger.onclick = (event) => { this.changeCurrentStep(this.INFO, event); }

        this.changeCurrentStep(this.currentStep);
    }

    changeCurrentStep(step, event = null) {
        switch (step) {
            case this.INTERNET:
                this._$OrderService.setStepValidation('internet', false);
                break;
            case this.TV:
                this._$OrderService.setStepValidation('tv', false);
                break;
            case this.PHONE:
                this._$OrderService.setStepValidation('phone', false);
                break;
            case this.TV2:
            case this.INFO:
                this._$OrderService.setStepValidation('personalInfo', false);
                break;
        }

        let stepValidation = this._$OrderService.getStepValidation();

        switch (step) {
            case this.INTERNET:
                this.internetStep.className = this.currentClass;
                this.internetStepMobile.className = this.currenClassMobile;
                break;
            case this.PHONE:
                if (stepValidation.internet && (!this.isTvAvailable || (this.isTvAvailable && stepValidation.tv))) {
                    this.internetStep.className = this.selectedClass;
                    this.internetStepMobile.className = this.selectedClassMobile;
                    this.phoneStep.className = this.currentClass;
                    this.phoneStepMobile.className = this.currenClassMobile;
                    if (this.isTvAvailable) {
                        this.tvStep.className = this.currentClass;
                        this.tvStepMobile.className = this.currenClassMobile;
                    }
                } else if (event) {
                    event.preventDefault();
                }
                break;
            case this.TV:
            case this.TV2:
                if (stepValidation.internet) {
                    this.internetStep.className = this.selectedClass;
                    this.internetStepMobile.className = this.selectedClassMobile;
                    
                    this.tvStep.className = this.currentClass;
                    this.tvStepMobile.className = this.currenClassMobile;
                } else if (event) {
                    event.preventDefault();
                }
                break;
            case this.INFO:
                if (stepValidation.internet && stepValidation.phone && (!this.isTvAvailable || (this.isTvAvailable && stepValidation.tv))) {
                    this.internetStep.className = this.selectedClass;
                    this.internetStepMobile.className = this.selectedClassMobile;
                    this.phoneStep.className = this.selectedClass;
                    this.phoneStepMobile.className = this.selectedClassMobile;
                    if (this.isTvAvailable) {
                        this.tvStep.className = this.selectedClass;
                        this.tvStepMobile.className = this.selectedClassMobile;
                    }
                    this.infoStep.className = this.currentClass;
                    this.infoStepMobile.className = this.currenClassMobile;
                } else if (event) {
                    event.preventDefault();
                }
                break;
        }
    }
}