.faq {
	position: relative;

	&:after {
		content: '';
		position: absolute;
		display: block;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: calc(100% - 32px);
		height: 1px;
		background-color: #979797;

		@media (min-width: 768px) {
			max-width: calc(750px - 32px);
		}
		@media (min-width: 992px) {
			max-width: calc(970px - 32px);
		}
		@media (min-width: 1200px) {
			max-width: 1140px;
		}
	}

	&__section {
		&--top.faq__section--top {
			padding-bottom: 100px;

			@media (max-width: 991px) {
				padding-bottom: 70px;
			}

			@media (max-width: 767px) {
				padding-bottom: 50px;
			}
		}

		&--searchbar {
			padding: 40px 0!important;
			margin-bottom: 50px;

			@media (max-width: 767px) {
				margin-bottom: 24px;
			}
		}
	}

	&__head-row {
		display: flex;
		margin: 0 -48px;
	}

	&__head-col {
		padding: 0 48px;
		width: 50%;

		&:not(:last-child) {
			border-right: 1px solid #979797;
		}

		&-button {
			margin-top: 40px;
		}
	}

	&__search-form {
		position: relative;
		display: block;
		max-width: 600px;
		margin: 0 auto;
		color: #FFF;
	}

	&__search-input {
		width: 100%;
		appearance: none;
		background-color: $c-white;
		border: none;
		padding: 16px 64px 16px 16px;
		border-radius: 4px;

		&, &::placeholder {
			font: 700 16px/26px $f-primary;
			color: $c-black;
		}
	}

	.ebox-autocomplete--filled {
		~ .faq__search-reset-button {
			transform: scale(1);
			opacity: 1;
			pointer-events: all;
		}

		~ .faq__search-icon {
			transform: scale(0.5);
			opacity: 0.00001;
		}
	}

	&__search-reset-button,
	&__search-icon {
		position: absolute;
		top: 50%;
		right: 16px;
		transition: 0.2s $cubic;
		transition-property: transform, opacity;
	}

	&__search-reset-icon,
	&__search-icon {
		fill: currentColor;
	}

	&__search-icon {
		display: block;
		width: 32px;
		height: 32px;
		margin-top: -16px;
		transform: scale(1);
		opacity: 1;
		pointer-events: none;
		fill: $c-black;
	}

	&__search-reset-button {
		display: block;
		appearance: none;
		background: none;
		border: none;
		outline: none;
		margin-top: -8px;
		pointer-events: none;
		transform: scale(0.5);
		opacity: 0.00001;
		&:focus {
			outline: 2px solid blue;
			outline-offset: -2px;
		}
	}

	&__search-reset-icon {
		display: block;
		width: 16px;
		height: 16px;
	}

	&__question-list {
		display: flex;
		flex-wrap: wrap;
		padding-top: 0px;
		margin: -30px;

		@media (max-width: 767px) {
			padding-top: 50px;
		}
	}

	&__question-item {
		flex: 0 0 50%;
		padding: 30px;
		&--partenaires {
			flex: 0 0 33%;
		}
	}

	@media (max-width: 991px) {
		&__head-row {
			margin: -48px 0;
			flex-wrap: wrap;
		}

		&__head-col {
			position: relative;
			padding: 48px 0;
			width: 100%;

			&:not(:last-child) {
				border-right: none;
				
				&:after {
					content: '';
					position: absolute;
					display: block;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 100%;
					height: 1px;
					background-color: #979797;
				}
			}
		}

		&__question-list {
			margin: -16px;
		}

		&__question-item {
			padding: 16px;
		}
	}

	@media (max-width: 767px) {
		&__search-input {
			padding: 12px 64px 12px 12px;

			&, &::placeholder {
				font-size: 16px;
				line-height: 22px;
			}
		}

		&__question-item {
			flex-basis: 100%;
		}
	}
}