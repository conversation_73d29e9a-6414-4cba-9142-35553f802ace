.InternetItem {
	&--has-promo-banner {
		&.InternetItem {
			height: auto;
			min-height: 172px;
		}

		.Listing {
			&__name {
				border-bottom-left-radius: 0;
				
			}
		}
	}

	&--is-recommended {
		position: relative !important;
		
		&:not(:first-child) {
			margin-top: 90px !important;
		}
	}

	&__recommended-star {
		position: absolute;
		bottom: 100%;
		right: 0;
		margin-bottom: 8px !important;
	}

	&__promo-banner {
		display: flex;
		align-items: center;
		padding: 8px 16px;
		width: 100%;
		height: var(--promoBannerHeight);
		background-color: $c-primary;
		color: #FFF;
		font: 700 16px/24px $f-primary;
		border-radius: 0 0 12px 12px;
	}

	@media (min-width: 768px) and (max-width: 991px) {
		&--is-recommended {
			margin-top: 14px !important;
		}

		&__recommended-star {
			margin-bottom: 4px !important;
		}
	}
}

.Listing__name {
	h3 {
		font: 700 18px/25px "BrandonGrotesque", sans-serif!important;
		color: #FFF!important;
		text-align: center!important;
	}
}