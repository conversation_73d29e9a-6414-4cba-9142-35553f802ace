/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { ChannelsService } from '../services/channels.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';


/**
 * Semi-specific directive used to alert the user before he changes page to tell 
 * him that he's about to reach a certain step in term of chosen channels.
 * 
 * Ex.: If the steps are set at 5. Every 5 selected channels, the user gets a reduced 
 * cost. So if the user tries to move to the next page and he has 13 selected channels,
 * we will prompt him to get 15 in order to get a reduced cost at the end.
 */
export class WarnForMoreDirective extends Directive {
	// Static properties //
	static selector = 'a[warn-for-more]';


	// Private properties //
	_$modalElement = jQuery(this.attrs.modalSelector);
	_$qtyHolder = jQuery('[data-warn-qty-holder]', this._$modalElement);
	_$modalConfirmBtn = jQuery('[warn-confirm]', this._$modalElement);

	get _orderSize() {
		return this._$tvOrder.getSelectedChannels('a-la-carte').size + this._$tvOrder.getSelectedChannels('others').size;
	}


	// Public readonly //
	static get MINIMUM_STEP_SIZE() {
		return TVOrderService.STEP_MIN_VALUE;
	}
	static get STEP_SIZE() {
		return TVOrderService.STEP_SIZE;
	}
	static get THRESHOLD() {
		return 2;
	}

	// Services //
	_$tvOrder = TVOrderService.getInstance();
	_$channels = ChannelsService.getInstance();


	// Init //
	constructor(host) {
		const bundle = LocalStorage.get('selectedBundles');
        if(bundle != '113570' && bundle != '113581'){
			super(host, [
				{name: 'modal-selector', required: true},
				'href'
			]);
			this._init();
		}else{
			super(host);
		}
	}
	_init() {
		this._bindEvents();
	}

	_bindEvents() {
		this.$host.on('click', this._onHostClick.bind(this));
		this._$modalConfirmBtn.on('click', this._onConfirmClick.bind(this));
	}

	_onHostClick(evt) {
		const channelsQtyToNextStep = this._getQtyBeforeNextStep();

		if (this._orderSize === 0) return;

		if (channelsQtyToNextStep > 0 && (this._orderSize < WarnForMoreDirective.MINIMUM_STEP_SIZE || channelsQtyToNextStep <= WarnForMoreDirective.THRESHOLD)) {
			evt.preventDefault();
			this._setHoldersValue(channelsQtyToNextStep);
			this._alertUser();
		}
	}
	
	/**
	 * If the host has an href attribute value, we want to redirect the user to
	 * that initial href once he clicks on the _$confirmElement which is the confirm
	 * button of the modal.
	 * @param {MouseEvent} evt 
	 * @return {void}
	 */
	_onConfirmClick(evt) {
		evt.preventDefault();
		if (this.attrs.href)
			location.href = this.attrs.href;
	}

	/**
	 * Returns how many channels must be selected to reach the next step.
	 * @return {number}
	 */
	_getQtyBeforeNextStep() {
		if (this._orderSize <= WarnForMoreDirective.MINIMUM_STEP_SIZE)
			return WarnForMoreDirective.MINIMUM_STEP_SIZE - this._orderSize;
		else
			return WarnForMoreDirective.STEP_SIZE - (this._orderSize % WarnForMoreDirective.STEP_SIZE);
	}

	/**
	 * Triggers the modal.
	 */
	_alertUser() {
		this._$modalElement.modal({show: true});
	}

	/**
	 * Sets the elements in charge of displaying to the user the number of channels
	 * left before getting to the next step.
	 * @param {number} val 
	 */
	_setHoldersValue(val) {
		this._$qtyHolder.html(`${ val } ${ CoreTools.pluralize(val, CoreTools.translate('chaîne', 'channel')) }`);
	}
}