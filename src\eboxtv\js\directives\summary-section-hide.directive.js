import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service';

/**
 * Hides or shows a order-summary section depending on the count of 
 * selected channels in a given type ('a-la-carte' or 'premium').
 */
export class SummarySectionHideDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[summary-section-hide]';


	// PRIVATE PROPERTIES //
	_countValue = -1;
	_hidden = false;
	
	/* Services injection */
	_$TVOrder = TVOrderService.getInstance();
	_$changeDetector = ViewChangeDetector.getInstance();



	// PUBLIC PROPERTIES //
	get count() {
		return this._countValue;
	}
	set count(val) {
		if (val !== this._countValue) {
			this._countValue = val;
			this.update(val);
		}
	}


	// INIT //
	constructor(host) {
		super(host, [
			{name: 'keep-alive', type: 'eval', default: true},
			{name: 'summary-section-hide', type: 'eval', default: false},
			{name: 'section-type', required: true}
		]);
		this._init();
	}
	_init() {
		/* If attribute option 'keep-alive' is set to false, we only update
		the count value on init. Otherwise, we subscribe to order change in
		get in sync with the current order. */
		if (this.attrs.keepAlive)
			this._subscribeToOrderChange();

		this._updateCount();
	}


	// PUBLIC METHODS //
	show() {
		this.$host.show();
		if (this._hidden) this._hidden = false;
		this._$changeDetector.emitChange(this);
	}

	hide() {
		this.$host.hide();
		if (!this._hidden) this._hidden = true;
		this._$changeDetector.emitChange(this);
	}

	update() {
		if (this._countValue > 0 && this._hidden)
			this.show();
		else if (this._countValue <= 0 && !this._hidden)
			this.hide();
	}


	// PRIVATE METHODS //
	_subscribeToOrderChange() {
		this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);
	}

	_onOrderChange() {
		this._updateCount();
	}

	_updateCount() {
		this.count = this._$TVOrder.getSelectedChannels(this.attrs.sectionType).size;
	}
}