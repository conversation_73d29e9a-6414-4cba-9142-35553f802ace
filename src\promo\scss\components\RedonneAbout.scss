.RedonneAbout {
	padding: 65px 0 73px;

	@media (max-width: 767px) {
		padding: 70px 0 41px;

		@include applyAtRoot('html:lang(en)') {
			padding: 70px 0 26px;
		}
	}

	&__title {
		position: relative;
		color: #4D4D4D;
		font-size: 36px;
		line-height: 1.2;
		margin-bottom: 28px;

		@media (max-width: 767px) {
			font-size: 27px;
			margin-bottom: 37px;
		}

		.red {
			color: $c-primary-dark;
		}

		&:after {
			content: '';
			display: inline-block;
			position: relative;
			display: inline-block;
			width: 24px;
			height: 24px;
			top: 12px;
			left: 6px;
			transform: rotate(12deg);
			background: {
				image: url('../../img/Propos_fleche.svg');
				size: 100% 100%;
				position: center;
				repeat: no-repeat;
			};
		}
	}

	&__bottom {
		display: flex;
		margin-top: 30px;
		justify-content: space-between;
		align-items: flex-end;

		@media (max-width: 767px) {
			margin-top: 37px;
		}
	}

	.SPCA_logo {
		width: 48px;
		height: 60px;
		margin-right: 109px;
		margin-bottom: -10px;

		@media (max-width: 767px) {
			margin-right: 0;
		}
		@media (max-width: 499px) {
			display: none;
		}
	}

	&__legend {
		@media (max-width: 767px) {
			margin-top: 40px;
		}
	}
}