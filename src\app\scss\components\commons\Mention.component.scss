.Mention {
    margin-bottom: 70px;
    @media (max-width: 767px) {
        margin-top: 40px;
        margin-bottom: 40px;
    }
    &__content {
        background: $c-primary-new;
        width: 100%;
        border-radius: 12px;
        display: inline-block;
        padding: 20px 15px 20px 105px;
        @media (max-width: 991px) {
            padding: 20px 15px 20px 80px;
        }
        @media (max-width: 767px) {
            padding: 10px 40px 10px 50px;
            &:after {
                content: "";
                position: absolute;
                right: 20px;
                top: 45%;
                transform: translateY(-50%);
                background: url('../../../img/Icon/ic_arrow-right-white.svg') center center no-repeat;
                background-size: 50%;
                display: inline-block;
                width: 25px;
                height: 25px;

            }
        }

    }
    &__content-text {
        padding: 0 25px;
        @media (max-width: 991px) {
            margin-top: 4px;
            padding: 0 10px;
        }
        @media (max-width: 767px) {
            margin-top: 0;
            padding: 0 15px
        }
    }
    &__text-norm {
        color: $c-white;
        font: 400 16px/23px $f-primary;
        margin: 0;
        @media (max-width: 991px) {
            font: 400 12px/16px $f-primary;
        }
    }
    &__text-gras {
        color: $c-white;
        font: 700 20px/24px $f-primary;
        margin: 0;
        @media (max-width: 991px) {
            font: 700 16px/22px $f-primary;
        }
        @media (max-width: 767px) {
            font: 700 12px/16px $f-primary;
        }
    }
    &__ribbon {
        position: absolute;
        background: url('../../../img/mention-ribbon.svg') left top no-repeat;
        background-size: 100%;
        top: -5px;
        left: 48px;
        width: 50px;
        height:71px;
        svg {
            fill: $c-white;
            width: 20px;
            height: 23px;
            margin-left: 12px;
            margin-top: 14px;
            @media (max-width: 767px) {
                width: 10px;
                height: 13px;
                margin-left: 8px;
                margin-top: 9px;
            }
        }
        @media (max-width: 991px) {
            left: 37px;
        }
        @media (max-width: 767px) {
            width: 28px;
            height: 41px;
            top: -3px;
        }
    }
    &__link-mobile {
        display: none;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
        @media (max-width: 767px) {
            display: inline-block;
        }
    }
    &--flexible {
        margin-top: 100px;
        @media (max-width: 991px) {
            margin-top: 70px;
            .col-md-12 {
                padding: 0 22px;
            }
        }
        @media (max-width: 767px) {
            .col-md-12 {
                padding: 0 15px;
            }
        }
    }
}


.page-template-listing-internet {
    .Mention--flexible {
        margin-top: inherit;
    }
}

.page-template-listing-internet {
    .VirageNum__intro-right {
        img {
            @media (max-width: 1024px) {
                display: none;
            }
        }
    }
    .VirageNum__intro-left {
        @media (max-width: 1024px) {
            padding-bottom: 0;
        }
    }
    .VirageNum__intro-icons {
        @media (max-width: 1024px) {
            margin-bottom: 0;
        }
    }
}