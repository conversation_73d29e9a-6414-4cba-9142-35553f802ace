.<PERSON> {
    background: 0;
    border: 0;
    display: none;
    //margin-right: 15px;
    margin-right: -10px;
    svg {
        fill: $c-black;
        width: 36px;
        height: 36px;
        vertical-align: middle;
    }
    &:focus,
    &:active {
        outline: 2px solid blue;
        outline-offset: -2px;
    }
    &:hover {
        svg {
            fill: #BE2323;
        }
    }
    @media (max-width:1200px) {
        display: inline-block;
        margin-right: 0;
    }
}

.Menu {
    width: 100vw;
    height: 100vh;
    background: $c-medium-grey;
    transition: $t-primary;
    position: fixed;
    z-index: 100;
    right: 0;
    top: 0;
    transition: $t-primary;
    transform: translateX(100%);
    visibility: hidden;
    @media (max-width: 767px) {
        height: 100%;
        overflow: hidden;
    }
    &__wrapper {
        height: 100%;
        overflow: scroll;
        padding-bottom: 100px;
        visibility: hidden;
    }
    &__header {
        position: relative;
        height: 60px;
        background: $c-black;
        width: 100%;
        &-logo {
            svg {
                margin-left: 20px;
                margin-top: 12px;
                fill: $c-white;
                width: 132px;
                height: 36px;
            }
        }
        button {
            background: none;
            border: 0;
            margin-left:10px;
        }
        &-tools {
            position: absolute;
            right: 5px;
            top: 15px;
            svg {
                width: 30px;
                height: 30px;
                fill: $c-white;
            }
        }
    }
    &__bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 50px;
        width: 100%;
        font: 16px/22px $f-primary;
        svg {
            width: 20px;
            height: 20px;
            fill: $c-light-grey;
            vertical-align: sub;
            margin-right: 5px;
        }
        &-contact {
            text-align: center;
            width: 50%;
            height: 50px;
            background: $c-grey;
            line-height: 50px;
            color: $c-light-grey;
            float: left;
            &:hover {
                color: $c-white;
            }
        }
        &-soutien {
            text-align: center;
            width: 50%;
            height: 50px;
            background: $c-black;
            line-height: 50px;
            color: $c-light-grey;
            float: left;
            &:hover {
                color: $c-white;
            }
        }
        @media (max-width: 767px) {
            position: fixed;
        }
    }
}

.MobileNavPrinc  {
    width: 100%;
    background: #1F1F1F;
    margin: 0;
    
    &__item {
        border-top: 1px solid $c-medium-grey;
        font:400 20px/22px $f-primary;
        padding: 18px 15px;

        &--parent {
            background: url('../../../img/Icon/ic_arrow-right.svg') right 15px center no-repeat;
            background-size: 20px 20px;
            transition: $t-primary;
            &:hover {
                background-position: right 20px center;
            }
        }
        &--internet {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_laptop-white.svg') center center / 100% 100% no-repeat;
            }
        }

        &--menu-btn-transparent {
            > a {
                display: inline-block;
                flex: 0 0 auto;
                text-transform: uppercase;
                text-align: center;
                transition: all 0.1s linear;
                border: 1px #FFF solid;
                border-radius: 5px;
                color: #FFF;
                font: 700 10px/20px $f-primary;
                text-decoration: none;
                padding: 3px 12px;
                text-transform: uppercase;
                width: auto;

                &:hover {
                    background: #FFF;
                    color: #303030;
                }
            }
        }

        &--television {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_television-white.svg') center center / 100% 100% no-repeat;
            }
        }

        &--telephonie {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_phone-white.svg') center center / 100% 100% no-repeat;
            }
        }

        &--forfaits {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_package-white.svg') center center / 100% 100% no-repeat;
            }
        }
        &--hebergement {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_hosting-white.svg') center center / 100% 100% no-repeat;
            }
        }
        &--fibe {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_fibe-white.svg') center center / 100% 100% no-repeat;
            }
        }
        &--network {
            &:before {
                content: "";
                width: 32px;
                height: 32px;
                position: relative;
                display: inline-block;
                vertical-align: middle;
                margin-right: 10px;
                background: url('../../../img/Icon/ic_network-white.svg') center center / 100% 100% no-repeat;
            }
        }
    }

    &__link {
        color: $c-white;
        transition: $t-primary;
        width: 100%;
        height: 100%;
        display: inline-block;
        &:hover {
            color: $c-light-grey;
        }
    }
    &__sub-menu {
        background: $c-medium-grey;
        width: 100vw;
        height: 100vh;
        position: fixed;
        padding-top: 0px;
        z-index: 1;
        right: 0;
        top: 0;
        transform: translateX(100%);
        transition: $t-primary;
        &__item {
            padding: 13px 15px;
            font: 400 20px/22px $f-primary;
            display: flex;
            align-items: center;
        }
        &__link {
            color: $c-white;
            transition: $t-primary;
            flex: 1 0 auto;
            
            &:hover {
                color: $c-light-grey;
            }
        }
    }
}

.MobileNavSec  {
    padding-top: 30px;
    width: 100%;
    margin-left: 0;
    &__item {
        font:400 16px/22px $f-primary;
        padding: 10px 15px;
    }
    &__link {
        color: $c-white;
        transition: $t-primary;
        &:hover {
            color: $c-light-grey;
        }
    }
}

body.menu-opened {
    overflow: hidden;
    position: fixed;
    .Menu {
        transform: translateX(0);
        visibility: visible;
        &__wrapper {
            visibility: visible;
        }
    }
}

li {
    &.sub-opened > {
        ul[class*="__sub-menu "] {
            transform: translateX(0);
        }
    }
}

.back-link {
    height: 60px;
    background: $c-black;
    position: relative;
    margin-bottom: 20px;

    svg {
        width: 20px;
        height: 20px;
        fill: $c-white;
        margin-top: 18px;
        margin-left: 15px;
        position: absolute;
        left: 0px;
        top: 2px;
    }
    a {
        font: 400 20px $f-primary;
        width: 100%;
        height: 100%;
        display: inline-block;
        text-align: center;
        color: $c-white;
        position: relative;
        line-height: 60px;
    }

}
.basket-mobile {
    display: none;
}