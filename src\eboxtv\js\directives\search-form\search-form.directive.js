/* global jQuery */
import { Directive } from '@core/directive';
import './search-form.directive.scss';
import Handlebars from 'handlebars';
import { App } from '@core/bootstrap';
import { ChannelSelectDirective } from '../channel-select.directive';
import { _BubbleDirective } from '../_bubble/_bubble.directive';
import { AccessifyDirective } from '../accessify/accessify.directive';
import { ChannelsService } from '../../services/channels.service';
import { Tools } from '@common/ts/services/Tools.service';
import Fuse from 'fuse.js';
import { CoreTools } from '@core/helpers';

export class SearchFormDirective extends Directive {
	static selector = 'form[search-form]';
	
	static RESULTS_BODY_CLASS_NAME = 'is-search-results-opened';
	static FOCUSED_BODY_CLASS_NAME = 'is-search-input-focused';
	static translations = {
		removeChannel: {
			fr: 'Retirer',
			en: 'Remove'
		},
		addChannel: {
			fr: 'Ajouter',
			en: 'Add'
		},
		byUnit: {
			fr: 'à l\'unité',
			en: 'by unit'
		},
		addToCost: {
			fr: 'au coût de ton ensemble de chaînes',
			en: 'to your bundle cost'
		}
	};
	static itemTemplate = require('./search-form__item.hbs');
	
	_$channels = ChannelsService.getInstance();
	_isLoadingValue = false;
	_resultsValue = [];
	_containerVisibleValue = false;
	_currentTemplate;
	_resultApp;

	filterContainer = this.host.querySelector('[search-filter-container]');
	$searchInput = jQuery('[search-form-input]', this.$host);
	$searchTextContainer = jQuery('[search-form-text]', this.$host);
	$resultContainer = jQuery('[search-form-result-container]', this.$host);
	$noResultMessage = jQuery('[search-form-noresult]', this.$host);
	$searchResultsContainer = jQuery('[search-form-results]', this.$host);
	$searchFilters = jQuery('[search-filter-item]', this.$host);
	allChannels = this._$channels.getAll(this.attrs.channelCategory.split(/,\s?/));
	fuseSearch = new Fuse(this.allChannels, {
		shouldSort: true,
		threshold: 0.3,
		keys: ['post_title']
	});

	get selectedFilters() {
		return Array.from(this.host.elements['genre[]']).filter(input => {
			return input.checked;
		}).map(input => input.value);
	}

	get searchText() {
		return this.$searchInput[0].value;
	}
	set searchText(val) {
		if (val !== this.searchText)
			this.$searchInput[0].value = val;

		if (this.$searchTextContainer.length)
			this.$searchTextContainer[0].innerText = `"${ val }"`;

		if (val.length > 0 && !this.containerIsVisible)
			this.containerIsVisible = true;
		else if (val.length === 0 && this.containerIsVisible)
			this.containerIsVisible = false;
	}

	get containerIsVisible() {
		return this._containerVisibleValue;
	}
	set containerIsVisible(val) {
		if (val !== this._containerVisibleValue) {
			this._containerVisibleValue = val;

			if (val)
				this._showResultContainer();
			else
				this._hideResultContainer();
		}
	}

	get results() {
		return this._resultsValue;
	}
	set results(val) {
		if (val !== this._resultsValue) {
			this._resultsValue = val;

			if (val.length)
				this._onHasResults();
			else
				this._onHasNoResults();

			this._renderTemplate(val);
			this.$searchResultsContainer[0].scrollLeft = 0;
		}
	}

	get isLoading() {
		return this._isLoadingValue;
	}
	set isLoading(val) {
		if (val !== this._isLoadingValue) {
			this._isLoadingValue = val;

			if (val)
				this.$host.addClass('SearchForm--is-loading');
			else
				this.$host.removeClass('SearchForm--is-loading');
		}
	}

	constructor(host) {
		super(host, [
			{name: 'search-form-filter', as: 'filterName'},
			{name: 'limit-results', type: 'int', default: Infinity},
			{name: 'has-result-class', default: 'search-form--has-result'},
			{name: 'can-modify-order', type: 'eval', default: true},
			{name: 'search-in', as: 'channelCategory', default: 'all'},
			{name: 'highlight-premiums', type: 'eval', default: false}
		]);
		this._onInit();

	}

	_onInit() {
		this._bindEvents();
	}

	_bindEvents() {
		this.$searchInput.on('input', this._onInputChange.bind(this))
			.on('keydown', this._onInputKeydown.bind(this))
			.on('focus', this._onInputFocus.bind(this))
			.on('blur', this._onInputBlur.bind(this));

		// this.$searchFilters.on('change', this._onFiltersChange.bind(this));
	}

	_onInputChange(evt) {
		const { value } = evt.target;
		
		if (value.length) {
			this.searchFor(value);
			this.searchText = value;
		} else {
			this.containerIsVisible = false;
			setTimeout(() => {
				this.results = [];
				this.searchText = value;
			}, 150);
		}
	}

	_onInputKeydown(evt) {
		if (evt.which === 27) {
			const searchInput = this.$searchInput[0];
			searchInput.blur();

			if (searchInput.directives && 'ClearableDirective' in searchInput.directives)
				searchInput.directives['ClearableDirective'].clearHostValue();
		}
	}

	_onInputFocus() {
		jQuery(document.body).addClass(SearchFormDirective.FOCUSED_BODY_CLASS_NAME);
	}

	_onInputBlur() {
		jQuery(document.body).removeClass(SearchFormDirective.FOCUSED_BODY_CLASS_NAME);
	}

	_onFiltersChange() {
		this.searchFor(this.searchText);
	}

	_addBodyClass() {
		jQuery(document.body).addClass(SearchFormDirective.RESULTS_BODY_CLASS_NAME);
	}

	_removeBodyClass() {
		jQuery(document.body).removeClass(SearchFormDirective.RESULTS_BODY_CLASS_NAME);
	}

	searchFor(val) {
		if (!val.length) {
			this.results = [];
			return;
		}
		
		// const filters = this.selectedFilters;
		let   results = this.fuseSearch.search(val);

		console.log(results);

		// for each results
		var channels_string = "";
		results.forEach(channel => {
			channels_string += channel.post_title + ", ";
		});
		// remove last comma
		channels_string = channels_string.slice(0, -2);
		if(results.length > 1){
			var resultsLabel = CoreTools.translate('Résultats de recherche: ', 'Search results: ');
			this.srSpeak(resultsLabel + channels_string, "polite");
		}else{
			var resultsLabel = CoreTools.translate('Aucun résultat', 'No results');
			this.srSpeak(resultsLabel, "polite");
		}




		// if (filters.length && getComputedStyle(this.filterContainer).display !== 'none') {
		// 	results = results.filter(channel => {
		// 		const { genres } = channel;
		// 		let foundByFilters = false;

		// 		if (genres !== undefined) {
		// 			for (let i = 0; i < filters.length; i++) {
		// 				if (genres.indexOf(filters[i]) !== -1) {
		// 					foundByFilters = true;
		// 					break;
		// 				}
		// 			}
		// 		}
		// 		return foundByFilters;
		// 	});
		// }

		if (results.length <= this.attrs.limitResults)
			this.results = results;
		else
			this.results = results.slice(0, this.attrs.limitResults);
		
	}

	_showResultContainer() {
		this.$host.addClass(this.attrs.hasResultClass);
		this.$resultContainer.show();
		this._addBodyClass();
	}

	_hideResultContainer() {
		this.$host.removeClass(this.attrs.hasResultClass);
		this.$resultContainer.hide();
		this._removeBodyClass();
	}

	_renderTemplate(results) {
		const lang = Tools.lang;
		const { canModifyOrder } = this.attrs;
		const template = Handlebars.compile(`
			{{#each channels as |channel|}}
				<li class="SearchForm__result-item">{{> channelItem channel=channel byUnit=../byUnit addToCost=../addToCost addChannel=../addChannel removeChannel=../removeChannel canModify=../canModify highlightPremiums=../highlightPremiums }}</li>
			{{/each}}
		`);

		results = results.map(channel => {
			channel.hasFeatures = channel.has_replay || channel.has_rattrapage || channel.has_go || channel.has_vod;
			return channel;
		});

		const newTemplate = template({
			channels: results,
			byUnit: SearchFormDirective.translations.byUnit[lang],
			addToCost: SearchFormDirective.translations.addToCost[lang],
			addChannel: SearchFormDirective.translations.addChannel[lang],
			removeChannel: SearchFormDirective.translations.removeChannel[lang],
			locationOrigin: location.origin,
			canModify: canModifyOrder,
			highlightPremiums: this.attrs.highlightPremiums
		});

		if (newTemplate !== this._currentTemplate) {
			if (this._resultApp) this._resultApp.destroy();

			this.$searchResultsContainer[0].innerHTML = this._currentTemplate = newTemplate;
			this._resultApp = new App({
				directives: [
					ChannelSelectDirective,
					_BubbleDirective,
					AccessifyDirective
				],
				rootElement: this.$searchResultsContainer[0]
			}).bootstrap();
		}
	}

	_onHasResults() {
		this.$noResultMessage.hide();
		this.$searchResultsContainer.show();
	}

	_onHasNoResults() {
		this.$noResultMessage.show();
		this.$searchResultsContainer.hide();
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}

}

Handlebars.registerPartial('channelItem', SearchFormDirective.itemTemplate);
Handlebars.registerHelper('convertedPrice', Tools.convertPriceToLocal);
Handlebars.registerHelper('translate', CoreTools.translate);
Handlebars.registerHelper('eq', (value1, value2) => value1 == value2);
Handlebars.registerHelper('eqBool', (value1, value2) => {
	if (value2 === true) {
		return value1 === true || value1 == 1;
	} else if (value2 === false) {
		return value1 === false || value1 == 0;
	} else {
		return value1 == value2;
	}
});
Handlebars.registerHelper('toUpperCase', CoreTools.toUpperCase);
Handlebars.registerHelper('not', value => !value);
Handlebars.registerHelper('log', value => {
	return 'logging';
});
Handlebars.registerHelper('getParentID', (channel) =>
	channel.post_parent === 0 ? channel.ID : channel.post_parent
);
Handlebars.registerHelper('ifCond', (v1, operator, v2, options) => {
    switch (operator) {
        case '==':
            return (v1 == v2)
        case '===':
            return (v1 === v2)
        case '!=':
            return (v1 != v2)
        case '!==':
            return (v1 !== v2)
        case '<':
            return (v1 < v2)
        case '<=':
            return (v1 <= v2)
        case '>':
            return (v1 > v2)
        case '>=':
            return (v1 >= v2)
        case '&&':
            return (v1 && v2)
        case '||':
            return (v1 || v2)
    }
});