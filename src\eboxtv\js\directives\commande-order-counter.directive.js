import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { PriceCalcService } from '../services/PriceCalc.service';
import { Tools } from '@common/ts/services/Tools.service';
import { ChannelsService } from '@eboxtv/js/services/channels.service';
/**
 * Simple Directive to keep count of the selected channel-ids stored
 * into TVOrderService.selectedChannels and display it in the UI.
 * 
 * IMPORTANT! See 'NOTE FOR DEPENDING DIRECTIVES' at begining of TVOrderService file.
 * {@link '../services/TVOrder.service.js'}
 */
export class CommandeOrderCounterDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[commande-order-counter]';

	
	// PRIVATE PROPERTIES //
	/**
	 * @private
	 * @type {number}
	 * Alias value for this._count's getter.
	 */
	_countValue = 0;
	
	/**
	 * @private
	 * @type {TVOrderService}
	 * Instance of TVOrderService.
	 */
	_$TVOrder = TVOrderService.getInstance();
	_$priceCalc = PriceCalcService.getInstance();
	// $channels = ChannelsService.getInstance();

	$hideParent = this.attrs.hideIfEmpty && this.attrs.hideParentSelector ?
		this.$host.parents(this.attrs.hideParentSelector) : null;

	
	// GETTERS AND SETTERS //
	/**
	 * @private
	 * @type {number}
	 * Every time this value is set, its value is reflected in the UI via
	 * the host element.
	 */
	get _count() {
		return this._countValue;
	}
	set _count(val) {
		this._countValue = val;
		this._render(val);
	}


	// ON INIT //
	constructor(host, childAttrs = []) {
		super(host, [
			'order-type',
			{name: 'show-total', type: 'eval', default: false},
			{name: 'item-name', type: 'string'},
			{name: 'hide-empty', as: 'hideIfEmpty', type: 'eval', default: false},
			{name: 'hide-parent', as: 'hideParentSelector'},
			...childAttrs
		]);
		
		// Initializing the directive's core methods
		this._init();
	}
	_init() {
		// Subscribing to TVOrderService.selectedChannelsChange$.
		this._subscribeToOrderChange();

		// Rendering initial value.
		this._render();
	}


	// PRIVATE METHODS //
	/**
	 * @private
	 * Subscribes the directive's instance to TVOrderService.selectedChannelsChange$
	 * BehaviorSubject.
	 * 
	 * @return {void}
	 */
	_subscribeToOrderChange() {
		this._orderChangeSub = this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);
	}

	/**
	 * @private
	 * Reacts to TVOrderService.selectedChannelsChange$. Applies the size of TVOrderService.selectedChannels
	 * to the _count property, which will be reflected to the host's innerText via _count's setter.
	 * 
	 * @param {Set<number>} selectedChannels New TVOrderService.selectedChannels value given by the BehaviorSubject change.
	 * @return {void}
	 */
	_onOrderChange(selectedChannels) {
		this._setCountOnChange(selectedChannels);
		this._hideIfEmpty(selectedChannels);
	}

	_setCountOnChange(selectedChannels) {
		const { orderType } = this.attrs;

		if (orderType) {
			if (orderType === 'all') {
				this._count = this._countSetsSizes(Object.values(selectedChannels), this._$TVOrder.selectedBundles);
			} else if (orderType !== 'pre-selected' && orderType !== 'forfaits' && orderType !== 'chargeMensuel' && orderType !== 'chargeUnique') {
				this._count = this._$TVOrder.selectedChannels[orderType].size;
			} else if (orderType === 'pre-selected') {
				this._count = this._$TVOrder.getTotalSelectedChannelSize('pre-selected');
			} else if (orderType === 'forfaits' || orderType === 'chargeMensuel' || orderType === 'chargeUnique') {
				this._render();
			}
		}

	}

	_hideIfEmpty(selectedChannels) {
		if (this.attrs.hideIfEmpty && this.$hideParent && this.$hideParent.length) {
			const parentIsVisible = this.$hideParent.is(':visible');

			if (this._count <= 0)
				this.$hideParent.hide();
			else if (this._count > 0)
				this.$hideParent.show();
		}
	}

	_countSetsSizes(setArray, bundles) {
		let count = 0;
		for (let i = 0; i < setArray.length; i++) {
			count += setArray[i].size;
		}

		if (bundles && bundles.size) {
			bundles.forEach(bundleID => {
				const bundle = this.$channels.getBundleById(bundleID);
				count += bundle.channels.length;
			});
		}
		return count;
	}

	/**
	 * @private
	 * Renders this._count's value to the UI by changing host's innerText.
	 * 
	 * @return {void}
	 */
	_render() {
		const currentBundle = this._$TVOrder.getSelectedBundle();
		let count = this._count;

		let { itemName, showTotal, orderType } = this.attrs;

		if (itemName !== undefined && itemName !== 'premium' && count > 1) {
			itemName += 's';
		} else if (itemName === 'premium') {
			itemName = 'Premium';
		}

		if (orderType === 'pre-selected' && currentBundle && currentBundle.isFeatured) {
			count += this.$channels.getLength('base');
		}

		if (showTotal && itemName) {
			const total = this._$priceCalc.totals.channels[orderType];
			this.host.innerHTML = `${ count }<span class="order-counter__text"> ${ itemName } / ${ Tools.convertPriceToLocal(total) }</span>`;
		} else if (showTotal && !itemName) {

			const totals = this._$priceCalc.totals.channels;
			const total = orderType === 'forfaits' ? totals['pre-selected'] + totals['base'] : totals[orderType];
			
			this.host.innerHTML = `<span class="order-counter__text">${ Tools.convertPriceToLocal(total) }</span>`;
		} else if (itemName !== undefined) {

			this.host.innerHTML = `${ count }<span class="order-counter__text">  ${ itemName }</span>`;
		} else {
			this.host.innerHTML = count;
		}
	}
}