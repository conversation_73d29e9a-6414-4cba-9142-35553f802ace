.wrapper-white {
    .help {
        padding-top: 100px;
        padding-bottom: 100px;
        @media (max-width: 991px) {
            padding-top: 70px;
            padding-bottom: 35px;
        }
        @media (max-width: 767px) {
            padding-top: 65px;
        }
        &__title  {
            font-family: $ebtv-f-primary;
            font-weight: 900;
            margin-bottom: 5px;
            color: $c-grey;
            font-size: 45px;
            line-height: 50px;
            text-transform: uppercase;
            @media (max-width: 991px) {
                font-size: 26px;
                line-height: 32px;
            }
            @media (max-width: 767px) {
                font-size: 22px;
                line-height: 24px;
            }
        }
        &__title-exp {
            font-family: $ebtv-f-secondary;
            font-size: 45px;
            margin: 0;
            line-height: 50px;
            @media (max-width: 991px) {
                font-size: 26px;
                line-height: 32px;
            }
            @media (max-width: 767px) {
                font-size: 22px;
                line-height: 24px;
            }
        }
    }
    .joinus {

        .col-sm-6:first-child {
            border-right: 3px solid $c-light-grey;
            position: relative;
            @media (max-width: 767px) {
                border-right: 0px;
                &:after {
                    content: "";
                    width: 100%;
                    border-bottom: 3px solid $c-light-grey;
                    display: inline-block;
                    margin-top: 65px;
                    margin-bottom: 65px;

                }
            }
        }
        .icon-ic_buy-online {
            width: 100px;
            height: 100px;
            margin-bottom: 8px;
            @media (max-width: 767px) {
                width: 80px;
                height: 80px;
                margin-bottom: 0;
            }
        }
        .icon-ic_phone {
            width: 98px;
            height: 98px;
            margin-bottom: 8px;
            @media (max-width: 767px) {
                width: 80px;
                height: 80px;
                margin-bottom: 4px;
            }
        }
        svg {
            transition: all 0.2s $cubic;
            @media (max-width: 991px) {
                width: 90px;
                height: 90px;
                margin-bottom: 10px;
            }
        }
        &__title {
            font-family: $ebtv-f-secondary;
            color: $c-primary;
            font-size: 30px;
            line-height: 34px;
            margin-bottom: 0;
            transition: all 0.2s $cubic;
            @media (max-width: 991px) {
                font-size: 26px;
                line-height: 32px;
            }
            @media (max-width: 767px) {
                font-size: 22px;
                line-height: 34px;
            }
            span {
                margin-bottom: 5px;
                display: block;
                color: $c-grey;
                text-transform: uppercase;
                font-size: 35px;
                font-family: $ebtv-f-primary;
                font-weight: 700;
                line-height: 55px;
                transition: all 0.2s $cubic;
                @media (max-width: 991px) {
                    font-size: 26px;
                    line-height: 32px;
                }
                @media (max-width: 767px) {
                    font-size: 22px;
                    line-height: 24px;
                }
            }
        }
    }
}

.wrapper-connexion {
    margin-top: 100px;
    border-radius: 12px;
    background: #FFFFFF;
    min-height: 540px;
    overflow: hidden;
    display: flex;
    width: 100%;
    box-shadow: 0px 2px 25px rgba(0, 0, 0, 0.5);
    background: url('~@common/img/ebox-texture-background--red.jpg') center center repeat;
    &-first-visit {
        background: $c-white;
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;

    }
    @media (max-width: 991px) {
        margin-top: 35px;
        min-height: 416px;
    }
    @media (max-width: 767px) {
        display: inline-block;
        margin-top: 67px;
    }

    .general-btn {
        min-height: 50px;
        padding: 0 30px;
    }
    .general-btn--inv-red {
        line-height: 50px;
        padding: 0 50px;
    }
}

.wrapper-connexion-account {
    padding-left: 80px;
    display: flex;
    align-items: center;
    &__title {
        color: $c-white;
        font-size: 45px;
        text-transform: uppercase;
        margin-bottom: 28px;
        span {
            display: block;
        }
        @media (max-width: 991px) {
            font-size: 26px;
            line-height: 32px;
        }
    }
    .general-btn {
        min-height: 50px;
        line-height: 50px;
    }
    .general-btn--inv-red {
        line-height: 50px;
    }
    @media (max-width: 991px) {
        padding-left: 30px;
    }
    @media (max-width: 767px) {
        width: 100%;
        max-width: 100%;
        padding-bottom: 90px;
        padding-top: 35px;
        display: inline-block;
        padding-left: 20px;
        padding-right: 20px;
    }
    &-input {
        a {
            color: $c-white;
            font-size: 12px;
            text-decoration: none;
            font-family: $ebtv-f-primary;
            text-align: right;
            display: inline-block;
            width: 100%;
            margin-top: 5px;
        }
        &:first-child {
            margin-bottom: 20px;
        }
    }
    input {
        width: 100%;
        background: none;
        border-top: 0;
        border-bottom: 1px solid $c-white;
        border-left: 0;
        border-right: 0;
        height: 40px;
        border-color: $c-white;
        font-family: $ebtv-f-primary;
        font-weight: 700;
        color: $c-white;
        font-size: 20px;
        transition: all 0.3s $cubic;
        &::placeholder {
            color: $c-white;
            transition: all 0.3s $cubic;
        }

    }
    button {
        margin-top: 40px;
    }
}

.wrapper-connexion-first-visit {
    display: flex;
    align-items: center;
    &__title {
        margin-bottom: 28px;
        span {
            display: block;
        }
        @media (max-width: 991px) {
            font-size: 26px;
            line-height: 32px;
        }
    }
    @media (max-width: 767px) {
        padding-bottom: 35px;
        padding-left: 20px;
        padding-right: 20px;
    }
    .general-btn {
        @media(max-width: 767px) {
            margin-top: 12px;
        }
    }
    &:before {
        content: "";
        width: 100%;
        height: 130%;
        left:-100px;
        top: 50%;
        position: absolute;
        background: $c-white;
        display: inline-block;
        transform-origin: center;
        transform: translateY(-50%) rotate(-10deg);
        @media (max-width: 991px) {
           left: -60px;
           transform: translateY(-50%) rotate(-6deg);
        }
        @media (max-width: 767px) {
            left: 0;
            width: 130%;
            transform: translateY(-50%) rotate(-4deg);
        }
    }
    p {
        font-weight: 700;
        font-size: 25px;
        margin-bottom: 12px;
        @media( max-width: 991px) {
            font-size: 22px;
            line-height: 26px;
        }
    }
    ul {
        margin-bottom: 25px;
        li {
            font-family: $ebtv-f-primary;
            font-size: 16px;
            line-height: 22px;
            margin-bottom: 12px;
            background: url('../../img/ic_check.svg') left 2px no-repeat;
            padding-left: 25px;
            background-size: 16px;
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.connexion-content {
    position: relative;
    z-index: 1;
    @media (max-width: 991px) {
        max-width: 280px;
     }
     @media (max-width: 767px) {
         max-width: 100%;
     }
}

.page-template-ebox-tv-connexion {
    .wrapper-white {
        h1 {
            margin-top: 7px;
        }
    }
}

.ebtv-required {
    border-bottom: 2px solid red!important;
    &::placeholder {
        font-style: italic;
    }
}
.material_input_focused {
    input {
        border-bottom: 2px solid #FFF!important;
        &.ebtv-required {
            border-bottom: 2px solid red!important;
        }
    }
}
.joinus {
    a {
        color: $c-black;
        &:hover {
            .joinus__title {
                transform: scale(1.2);
                will-change: transform;
                span {
                    will-change: transform;
                }
            }
        }
        &:active,
        &:focus {
            text-decoration: none!important;
        }
    }
}

.connexion-content {
    label {
        font-family: $ebtv-f-primary;
        font-weight: 700;
        color: $c-white;
        font-size: 20px;
    }
}
.wrapper-connexion-account-input {
    a {
        &:hover {
            color: rgba($c-white, 0.6);
        }
    }
}