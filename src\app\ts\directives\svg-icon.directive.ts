import { Directive } from '@core/directive.js';
import { TweenLite } from 'gsap/TweenLite';
import { Power2 } from 'gsap/EasePack';

export class SVGIconDirective extends Directive {
	static selector: string = 'svg-icon';

	attrs: {
		src: string;
		className?: string;
		drawOn?: string;
		drawTime: number;
		alwaysOn: boolean;
		pathOffset: number;
		hoverParentSelector?: string;
		reverse: boolean;
		delay: number;
	};

	private $SVGElement: JQuery<any>;
	private $hoverParent: JQuery = this.attrs.drawOn === 'hover' && this.attrs.hoverParentSelector ? 
								   this.$host.parents(this.attrs.hoverParentSelector) : null;

	private get SVGPathLength(): number {
		return this.getSVGPathLength() + this.attrs.pathOffset;
	}

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'src', required: true},
			{name: 'class', as: 'className'},
			'draw-on',
			{name: 'draw-time', type: 'eval', default: 0.4},
			{name: 'always-on', type: 'eval', default: true},
			{name: 'path-offset', type: 'float', default: 3},
			{name: 'reverse-draw', as: 'reverse', type: 'eval', default: false},
			{name: 'draw-delay', as: 'delay', type: 'float', default: 0},
			'hover-parent-selector'
		]);
		this.onInit();
	}

	private onInit(): void {
		if (!this.srcIsSVG()) return;

		this.loadFile()
			.then(this.onFileLoad.bind(this));
	}

	private srcIsSVG(): boolean {
		return /\.svg$/.test(this.attrs.src);
	}

	private loadFile(): Promise<string> {
		return fetch(this.attrs.src)
			.then(resp => resp.text());
	}

	private onFileLoad(htmlString: string): void {
		this.replaceElement(htmlString);
		this.bindEvents();
	}

	private replaceElement(htmlString: string): void {
		this.$SVGElement = jQuery(htmlString);
		
		if (this.attrs.className)
			this.$SVGElement.addClass(this.attrs.className);

		if (!this.attrs.alwaysOn) {
			this.resetSVG();
		}

		this.$host.replaceWith(this.$SVGElement);
	}

	private bindEvents(): void {
		if (this.attrs.drawOn === 'hover' && this.$hoverParent && this.$hoverParent.length) {
			this.$hoverParent.hover(
				this.onParentMouseenter.bind(this),
				this.onParentMouseleave.bind(this)
			);
		} else if (this.attrs.drawOn) {
			this.$SVGElement.on(this.attrs.drawOn, this.drawSVG.bind(this));
		}
	}

	private getSVGPathLength(): number {
		return this.$SVGElement.find('path')[0].getTotalLength();
	}

	private onParentMouseenter(): void {
		this.drawSVG();
	}

	private onParentMouseleave(): void {
		this.resetSVG();
	}

	private drawSVG(): Promise<any> {
		return new Promise(resolve => {
			const { reverse, delay } = this.attrs;
			const pathLength: string = this.SVGPathLength.toString() + 'px';

			TweenLite.fromTo(this.$SVGElement, this.attrs.drawTime, {
				strokeDasharray: pathLength,
				strokeDashoffset: reverse ? (this.SVGPathLength * -1) + 'px' : pathLength
			}, {
				strokeDasharray: pathLength,
				strokeDashoffset: 0,
				ease: Power2.easeInOut,
				delay,
				onComplete: resolve
			});
		});
	}

	private resetSVG(): void {
		if (this.attrs.alwaysOn) {
			TweenLite.set(this.$SVGElement, {
				clearProps: 'all'
			});
		} else {
			const pathLength: string = this.SVGPathLength.toString() + 'px';

			TweenLite.set(this.$SVGElement, {
				strokeDasharray: pathLength,
				strokeDashoffset: pathLength
			});
		}
	}
}