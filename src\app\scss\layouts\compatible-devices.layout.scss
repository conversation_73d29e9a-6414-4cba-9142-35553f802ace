.compatible-devices {
	padding-top: 80px;
	.compatible-devices__modem-button-list {
		h2 {
			font: 700 35px/40px "BrandonGrotesque", sans-serif!important;
			text-transform: none!important;
		}
	}
	.order-tabs {
		&__tab {
			&--active {
				background: $c-primary;
			}
			&:focus {
				outline-offset: 2px;
				border: 2px solid blue;
			}
		}
	}

	&__tab-container {
		border-top: 1px solid $c-medium-grey;
	}

	&__tab-content {
		&--routers {
			.TwoCol-LeftImgRightText,
			.TwoCol-LeftTextRightImg {
				&__leftImg,
				&__rightImg {
					transform: none !important;
					opacity: 1 !important;
				}
			}
		}

		&--modems {
			.row {
				margin: -15px;
			}
		}

		&--modems,
		&--eboxtv {
			padding: 100px 0;

			@media (max-width: 991px) {
				padding: 70px 0;
			}
		}
	}

	&__modem-button-lists {
		margin: -15px -32px;
	}

	&__modem-button-list {
		padding: 15px 32px;

		.ButtonList {
			&__item {
				flex-basis: auto;
			}
		}
	}

	&__modem-list-container {
		padding-top: 80px;
		margin-top: 100px;
		border-top: 1px solid $c-grey-faded;

		@media (max-width: 991px) {
			margin-top: 70px;
		}
	}

	&__modem-list {
		padding: 0 0 70px;

		&-title {
			text-align: center;
			margin-bottom: 40px;
		}

		&-subtitle {
			position: relative;
			text-align: center;
			top: -40px;
		}

		.mc {
			margin: 0 -15px !important;
		}
	}

	&__modem-anchor-list {
		padding: 16px 0 0;
		border-top: 0;

		.Button {
			&:not(.Button--active) {
				background-color: #595959;
			}

			&--active {
				background-color: $c-primary;
			}

			&:focus {
				color: #FFF;
			}
		}
	}

	&__tab-wysiwyg,
	&__eboxtv-devices {
		padding-top: 0;

		h1, h2, h3, h4, h5, h6 {
			margin-bottom: 8px;
		}

		p {
			font: 400 22px/24px $f-primary;
		}
	}

	&__eboxtv-devices {
		padding-top: 64px;

		ul {
			padding-left: 1em;

			&:not(:last-child) {
				margin-bottom: 32px;
			}
		}
	}
}

.mejs-container:focus {
    outline: 2px solid blue!important;
    outline-offset: 2px!important;
}

.ginput_container {
	input,
	textarea {
		&:focus {
			outline: 2px solid blue!important;
			outline-offset: 2px!important;
		}
		
	}
}

.gform_button {
	&:focus {
		outline: 2px solid blue!important;
		outline-offset: 2px!important;
	}
}


// TABLEAU MODEM
/*.mcvalue {
	display: none;
}
.compatible-devices__modem-list {

	@media screen and (max-width: 600px) {
		table {
			width: 100%;
		}
		.mcicon,
		.mcconnection {
			//display: none;
			background: none;

		}
		.mcvalue {
			display: block;
		}
		table {
		  border: 0;
		}
	  
		table caption {
		  font-size: 1.3em;
		}
		
		table thead {
		  border: none;
		  clip: rect(0 0 0 0);
		  height: 1px;
		  margin: -1px;
		  overflow: hidden;
		  padding: 0;
		  position: absolute;
		  width: 1px;
		}
		
		table tr {
		  border-bottom: 3px solid #ddd;
		  display: block;
		  margin-bottom: .625em;
		  border-radius: 0;
		  &:hover {
			box-shadow: none!important;
		  }
		}

		.modemsV {
			padding-left: 0;
		}
		
		table td {
		  border-bottom: 1px solid #ddd;
		  display: block;
		  font-size: .8em;
		  text-align: right;
		  width: 100%;
		}
		
		table td::before {

		  content: attr(data-label);
		  float: left;
		  font-weight: bold;
		  text-transform: uppercase;
		}
		
		table td:last-child {
		  border-bottom: 0;
		}
	  }
} */

.responsiveTable {
	&--break {
		@media screen and (max-width: 920px) {

			width: 100%!important;
			border: 0;
		
		thead {
			border: none;
			clip: rect(0 0 0 0);
			height: 1px;
			margin: -1px;
			overflow: hidden;
			padding: 0;
			position: absolute;
			width: 1px;
		}
		
		tr {
			border-bottom: 3px solid #ddd;
			display: block;
			margin-bottom: .625em;
			border-radius: 0;
			&:hover {
			box-shadow: none!important;
			}
		}
		
		td {
			border-bottom: 1px solid #ddd;
			display: inline-block;
			font-size: .8em;
			text-align: right!important;
			box-sizing: border-box!important;
			width: 100%!important;
			padding: 10px 0 0 0;
		}
		
		td::before {
	
			content: attr(data-label);
			float: left;
			font-weight: bold;
			text-transform: uppercase;
		}
		
		td:last-child {
			border-bottom: 0;
		}
		}
	}
	@media screen and (max-width: 600px) {

		width: 100%;
		border: 0;
	
	thead {
		border: none;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
	
	tr {
		border-bottom: 3px solid #ddd;
		display: block;
		margin-bottom: .625em;
		border-radius: 0;
		&:hover {
		box-shadow: none!important;
		}
	}
	
	td {
		border-bottom: 1px solid #ddd;
		display: inline-block;
		font-size: .8em;
		text-align: right!important;
		box-sizing: border-box!important;
		width: 100%;
		padding: 10px 0 0 0;
	}

	.mcicon {
		background-position: right;
	}
	
	td::before {

		content: attr(data-label);
		float: left;
		font-weight: bold;
		text-transform: uppercase;
	}
	
	td:last-child {
		border-bottom: 0;
	}
	}
}