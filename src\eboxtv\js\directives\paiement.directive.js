/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { Tools } from '@common/ts/services/Tools.service';
import { CoreTools } from '@core/helpers';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { LocalStorage } from '@common/ts/services/LocalStorage';

export class PaiementDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()

	static selector = '[data-paiement]';


	// VARIABLES POUR LE FONCTIONNEMENTS  DU VALIDATEUR
	$form;
	$valid = true;
	$inputs = this.toArray(document.forms['paiementForm'].elements);
	$btnSubmit = document.querySelector('#submitPaiement');
	erreurChase = document.getElementById('erreurPaiement');

	billInfo;

	// transforme les tableau d element html en tableau
	toArray(obj) {
		var array = [];
		for (var i = obj.length >>> 0; i--;) {
			array[i] = obj[i];
		}
		return array;
	}



	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
		this.billInfo = localStorage.getItem('total');

		let stepValidation = this._$OrderService.getStepValidation();
		/*if (false || !stepValidation.internet || !stepValidation.phone || !stepValidation.personalInfo || !stepValidation.tv) {
			if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
		} else { */
			this.toArray(document.forms['paiementForm']);
			this.$btnSubmit.addEventListener('click', function () {
				this.submitForm();
			}.bind(this))

			document.addEventListener('keyup', function (evt) {
				document.activeElement.name === 'cardNumber' ? this.formatCreditCardNumber(document.activeElement) : "";
				document.activeElement.name === 'cardExp' ? this.formatExpDate(document.activeElement, evt) : "";
			}.bind(this))



			jQuery('#nomComplet').on('input', this.nameValidator.bind(this));
			jQuery('#noCarte').on('input', this.cardValidator.bind(this));
			jQuery('#carteCvc').on('input', this.CvcValidator.bind(this));
			jQuery('#carteExp').on('input', this.carteExpValidator.bind(this));

			
		//}
	}

	submitForm() {
		this.$valid = true;
		let data = {};
		this.$inputs.forEach((input) => {
			this.validateInput(input)
			switch (input.name) {
				case 'name':
					data.name = input.value;
					break;
				case 'cardNumber':
					data.cardNumber = input.value;
					break;
				case 'cardExp':
					data.cardExp = input.value;
					break;
				case 'cardCvc':
					data.cardCvc = input.value
					break;
				case 'consent':
					data.consent = input.checked
					if (!input.checked) {
						this.$valid = false;
						this.addError(input);
					}
					break;
			}

		})
		if (this.$valid) {
			data.total_pay = this.billInfo;
			this.sendPaiement(data);
		}
	}

	nameValidator(){
		const input = document.getElementById('nomComplet');
		input.value = input.value.replace(/[^A-Za-zàèìòùÀÈÌÒÙáéíóúýÁÉÍÓÚÝâêîôûÂÊÎÔÛãñõÃÑÕäëïöüÿÄËÏÖÜŸçÇßØøÅåÆæœ-\s]/gi, "");
	}

	cardValidator(){
		const input = document.getElementById('noCarte');
		input.value = input.value.replace(/[^0-9]/gi, "");
	}
	CvcValidator(){
		const input = document.getElementById('carteCvc');
		input.value = input.value.replace(/[^0-9]/gi, "");
	}
	carteExpValidator(){
		const input = document.getElementById('carteExp');
		input.value = input.value.replace(/[^0-9/]/gi, "");
	}
	

	validateInput(input) {
		switch (input.type) {
			case 'text':
				if (input.value.trim() == '') {
					this.$valid = false;
					this.addError(input);
				}
				else {
					this.removeError(input);
				}
				break;
			case 'checkbox':
				if (!input.checked) {
					this.$valid = false;
				}
				else {
					this.removeError(input);
				}
				break;
		}

	}

	addError(input) {
		input.parentElement.classList.add('error');
		// SRSpeak the error message
		this.srSpeak(input.parentElement.querySelector('.errorMsg').innerHTML, 'assertive');
	}
	removeError(input) {
		input.parentElement.classList.remove('error');
	}

	formatCreditCardNumber(input) {
		let str = input.value;
		str = str.replace(/[^0-9]/g, '');
		input.value = str;
	}

	formatExpDate(input, evt) {

		if (!input.value.includes('/')) {
			if (input.value.length > 2) {
				if (evt.key !== 'Backspace') {
					const a = input.value;
					const b = '/';
					var position = 2;
					var output = [a.slice(0, position), b, a.slice(position)].join('');
					input.value = output;
				}
			}
		}
	}

	sendPaiement(data) {
		jQuery('.full-screen-spinner-background').show();
		const rep = this._$OrderService.payBill(data).then((resp) => {
			if (resp.RESPONSE && resp.RESPONSE.status == 'ERROR') {
				this.erreurChase.classList.remove('hidden');
				jQuery('.full-screen-spinner-background').hide();
				jQuery("footer").trigger("click");
			}
			else {
				//redirection vers la page de succes
				this._$OrderService.setStepValidation('payment', this.$valid);

				this.erreurChase.classList.add('hidden');
				let newUrl = '/';
				newUrl += CoreTools.lang == 'fr' ? '' : 'en/'
				LocalStorage.get('qualificationAddresse').pc.toLowerCase() == 'on' ? 'ontario/' : 'quebec/';
				newUrl += CoreTools.lang == 'fr' ? 'residentiel' : 'residential';
				newUrl += CoreTools.lang == 'fr' ? '/confirmation-de-commande' : '/order-confirmation';
				localStorage.clear();
				
				const expirationDate = new Date();
				expirationDate.setDate(expirationDate.getDate() - 10);

				CookiesService.setCookies({
					name: 'CommandeStarted',
					value: 'oui',
					expires: expirationDate
				});
				CookiesService.setCookies({
					name: 'hasTV',
					value: 'false',
					expires: expirationDate
				});
				CookiesService.setCookies({
					name: 'eboxQualification',
					value: '',
					expires: expirationDate
				});
		
				CookiesService.setCookies({
					name: 'isTvPackageSelected',
					value: '',
					expires: expirationDate
				});

				window.location.href = newUrl;
			}
		});
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}