.CoordonneesMap {
    padding: 85px 0 80px;
    width: 100%;
    display: inline-block;
    position: relative;
    background: $c-grey;
    @media (max-width: 991px) {
        padding: 65px 0 0;
    }
    &__siege-social, &__bureau-secondaire {
        padding: 0 30px 0 15px;
        @media (max-width: 991px) {
            padding: 0 19px 0 19px;
        }
        @media (max-width: 767px) {
            padding: 0 15px 0 15px;
        }
        h4 {
            text-transform: none;
            color: $c-light-grey;
            margin: 0 0 15px;
            &:after {
                content: "";
                width: 100%;
                max-width: 480px;
                height: 2px;
                background: $c-light-grey;
                display: block;
                margin: 10px 0 0 0;
                @media (max-width: 991px) {
                    max-width: 100%;
                }
            }
        }
        p {
            margin: 0 0 15px;
            color: $c-light-grey;
            a {
                color: $c-light-grey;
                &:hover {
                    color: $c-white;
                }
            }
            img {
                margin: 0 5px 15px 0;
                float: left;
            }
            &:last-of-type {
                margin: 0;
            }
        }
    }
    &__bureau-secondaire {
        margin: 65px 0 0 0;
        @media (max-width: 991px) {
            margin: 50px 0 65px;
        }
    }
    .GoogleMap {
        height: 536px!important;
        @media (max-width: 1200px) {
            height: 558px!important;
        }
        @media (max-width: 1024px) {
            height: 550px!important;
        }
        @media (max-width: 991px) {
            height: 350px!important;
        }
        @media (max-width: 767px) {
            height: 250px!important;
        }
    }
}
