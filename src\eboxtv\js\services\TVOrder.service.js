import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { LocalStorage } from '@common/ts/services/LocalStorage.ts';
import { Tools } from '@common/ts/services/Tools.service';
import { ChannelsService } from './channels.service';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { OriginRatioChecker } from './OriginRatioChecker.service';
import { PriceStepsManager } from '../../ts/services/PriceStepsManager';
import { PromotionsService } from "@eboxtv/js/services/promotions.service";
/**
 * ---> NOTE FOR DEPENDING DIRECTIVES: Any Directives made to react to TVOrderService.selectedChannels
 * changes via the TVOrderService.selectedChannelsChange$ BehaviorSubject MUST BE treated as passive observers
 * that will only reflect TVOrderService.selectedChannels's value. If the have to change it, the MUST
 * do it via TVOrderService public methods which will automatically update the TVOrderService.selectedChannelsChange$
 * BehaviorSubject after which the observer directives will update.
 */
export class TVOrderService {
	//#region Singleton creation and distribution
	/**
	 * @private
	 */
	static _instance;
	/**
	 * @public
	 * @return {TVOrderService}
	*/
	static getInstance() {
		if (TVOrderService._instance === undefined)
			TVOrderService._instance = new TVOrderService();

		return TVOrderService._instance;
	}
	//#endregion

	static $stepsManager = PriceStepsManager.getInstance();
	$promotionsService = PromotionsService.getInstance();

	//#region Static readonly values
	static STEP_MIN_VALUE = TVOrderService.$stepsManager.STEP_MIN_VALUE;
	static STEP_SIZE = TVOrderService.$stepsManager.STEP_SIZE;
	static IS_DEV = false;
	static SKIP_RATIO_VALIDATION = true;
	//#endregion

	//#region Private properties
	_callCount = 0;
	_callCountDebounce;
	
	_$channelsService = ChannelsService.getInstance();

	_channelList = this._$channelsService.getAll();
	/**
	 * @private
	 * @type {Set}
	 * Private value for public selectedChannels property.
	 */
	_selectedChannels = {
		'base': new Set(this._$channelsService.getAll('base', true)),
		'a-la-carte': new Set(),
		'others': new Set(),
		'premium': new Set()
	};
	_selectedBundles = new Set();
	_originChecker = OriginRatioChecker.getInstance();
	_$ratioAlertModal = jQuery('#ratioAlertModal');
	_$ratioMessageContainer = jQuery('#ratioAlertMessage', this._$ratioAlertModal);
	//#endregion

	//#region Public properties
	/**
	 * @public
	 * @type {BehaviorSubject<Set<number>>}
	 * BehaviorSubject for other directives and services of the App to
	 * subscribe to watch for changes on selectedChannels property.
	 */
	selectedChannelsChange$ = new BehaviorSubject(this._selectedChannels);
	selectedBundlesChange$ = new BehaviorSubject(this._selectedBundles);

	/**
	 * @public
	 * Contains a Set of the selected channel IDs. Every time this value
	 * is set, this selectedChannelsChange$ updates to tell every of its subscriber
	 * that the selectedChannels value has changed.
	 */
	get selectedChannels() {
		let tvPackageCookie = CookiesService.getCookies('ebtv__tvPackage', true);

		if (tvPackageCookie !== null) tvPackageCookie = tvPackageCookie.type;

		if (TVOrderService.IS_DEV)
			this._logRequestCount();
		
		if (tvPackageCookie === 'payant')
			return this._filterOverflowingChannels();
		else
			return {
				'base': new Set(this._$channelsService.getAll('base', true)),
				'a-la-carte': new Set(),
				'others': new Set(),
				'premium': new Set()
			};
	}
	set selectedChannels(val) {
		if (val !== this._selectedChannels) {
			this._selectedChannels = val;
			this._triggerChange('channels');
		}
	}

	get selectedBundles() {
		return this._selectedBundles;
	}
	set selectedBundles(val) {
		if (val !== this._selectedBundles) {
			this._selectedBundles = val;
			this._triggerChange('bundles');
		}
	}

	get bundledChannelIDs() {
		let output = [];
		if(this._selectedBundles){

		this._selectedBundles.forEach(bundleID => {
			const bundleObj = this._$channelsService.getBundleById(bundleID);

			if (bundleObj)
				output = output.concat(bundleObj.channels);
		});
	}

		return output;
	}

	get selectedBundlesObjects() {
		return Array.from(this._selectedBundles).map(bundleID => this._$channelsService.getBundleById(bundleID));
	}

	get currentStep() {
		const steps = TVOrderService.$stepsManager.steps;
		let { size: selectedSize } = this.getSelectedChannels('a-la-carte');
		let stepSize = 0;

		for (let i = 0; i < steps.length; i++) {
			const currentStep = steps[i];

			if (selectedSize >= currentStep.size)
				stepSize = currentStep.size;
			else
				break;
		}

		return Math.max(stepSize, TVOrderService.STEP_MIN_VALUE);

		// const remains = selectedSize % TVOrderService.STEP_SIZE;
		// let   output = selectedSize;

		// if (remains !== 0)
		// 	output = (selectedSize - remains);

		// return Math.max(output, TVOrderService.STEP_MIN_VALUE);
	}

	get nextStep() {
		let { size: selectedSize } = this.getSelectedChannels('a-la-carte');

		const remains = selectedSize % TVOrderService.STEP_SIZE;

		if (selectedSize <= TVOrderService.STEP_MIN_VALUE || remains === 0)
			return this.currentStep;
		else
			return this.currentStep + TVOrderService.STEP_SIZE;
	}
	//#endregion

	//#region Init
	constructor() {

		var hasTV = CookiesService.getCookies('hasTV');
		if(hasTV == undefined){
			CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});
		}

		this._subscribeToChange();
		this._cleanLocalStorage();
		this._updateFromLocalStorage();
		this._triggerChange('channels');
		this._triggerChange('bundles');
		this._originChecker.check(this.getAllSelectedChannels(true, true), null, TVOrderService.SKIP_RATIO_VALIDATION);
	}
	//#endregion

	//#region Public method
	/**
	 * @public
	 * Will add a channelID to selectedChannels property and trigger change
	 * on selectedChannelsChange$ BehaviorSubject. This method MUST be used for every
	 * external changes to selectedChannels's value.
	 * 
	 * @param {number|number[]} channelID 
	 * @return {void}
	 */
	addChannel(id, type) {
		return new Promise((resolve, reject) => {
			if (this._addChannelID(id, type)) {
				this._triggerChange('channels');
				resolve();
			} else {
				reject();
			}
		});
	}

	/**
	 * @param {object[]} channels Must be an array of object containing at least both
	 * 							  properties "id" AND "type".
	 * @returns {Promise<void>}
	 */
	addMultipleChannels(channels = [], type) {
		return new Promise((resolve, reject) => {
			const selectedChannels = this.getAllSelectedChannels(true, true);
			const channelObjects = channels.map(channel => this.getChannelById(channel.id));
			const mergedOldNew = [...selectedChannels, ...channelObjects];
			const ratioValidation = this._originChecker.check(mergedOldNew, null, TVOrderService.SKIP_RATIO_VALIDATION);

			if (ratioValidation.isValid) {
				for (let i = 0; i < channels.length; i++) {
					const channel = channels[i];
					this._addChannelID(channel.id, (type || channel.type), false);
				}
				this._triggerChange('channels');
				resolve();
			} else {
				reject();
			}
		});
	}

	/**
	 * @public
	 * Will remove a channelID from selectedChannels property and trigger change
	 * on selectedChannelsChange$ BehaviorSubject. This method MUST be used for every
	 * external changes to selectedChannels's value.
	 * 
	 * @param {number} channelID 
	 * @return {void}
	 */
	removeChannel(id, type) {
		return new Promise((resolve, reject) => {
			if (this._removeChannelID(id, type)) {
				this._triggerChange('channels');
				resolve();
			} else {
				reject();
			}
		});
	}

	/**
	 * Works just like addMultipleChannels but in reverse.
	 */
	removeMultipleChannels(channels = [], type) {
		return new Promise((resolve, reject) => {
			const channelsIDs = channels.map(channel => channel.id);
			const selectedChannels = this.getAllSelectedChannels(true, true).filter(channel => {
				return !channelsIDs.has(channel.ID);
			});
			const ratioValidation = this._originChecker.check(selectedChannels, null, TVOrderService.SKIP_RATIO_VALIDATION);

			if (ratioValidation.isValid) {
				for (let i = 0; i < channels.length; i++) {
					const channel = channels[i];
					this._removeChannelID(channel.id, (type || channel.type), false);
				}
				this._triggerChange('channels');
				resolve();
			} else {
				reject();
			}
		});
	}

	/**
	 * Returns a channel WPPostObject matching a given ID.
	 * @param {number} id 
	 * @return {WPPostObject}
	 */
	getChannelById(id) {
		const channelIndex = Tools.getIndexInArrayByProperty(this._channelList, 'ID', id);
		return this._channelList[channelIndex];
	}

	getTotalSelectedChannelSize(type = 'all') {
		let count = 0;

		if (type === 'all') {
			for (const key in this.selectedChannels) {
				count += this.selectedChannels[key].size;
			}
		} else if (type !== 'pre-selected') {
			for (const key in this.selectedChannels) {
				if (key === type || (type !== key && type === 'a-la-carte' && key === 'others'))
					count += this.selectedChannels[key].size;
			}
		} else {
			if(this.selectedBundles){
				this.selectedBundles.forEach(bundleID => {
					const bundle = this._$channelsService.getBundleById(bundleID);
					
					if ('channels' in bundle)
						count += bundle.channels.length;
				});
			}

		}
		
		return count;
	}

	getSelectedChannels(type, asArray = false) {
		const bundledChannels = this.bundledChannelIDs;
		const filtered = Array.from(this._selectedChannels[type]).filter(channelID =>
			!this.isInBundle(channelID, bundledChannels)
		);

		if (asArray)
			return filtered;
		else
			return new Set(filtered);
	}

	getAllSelectedChannels(asArray = false, getObjects = false, excludeBase = false) {
		let { base, 'a-la-carte': aLaCarte, premium, others } = this.selectedChannels;
		let allChannels = ([...Array.from(aLaCarte), ...Array.from(premium), ...Array.from(others)]);

		if (!excludeBase) {
			allChannels = [...Array.from(base), ...allChannels];
		}
		
		if (getObjects)
			allChannels = allChannels.map(channelID => this.getChannelById(channelID));

		if (asArray)
			return allChannels;
		else
			return new Set(allChannels);
	}

	addBundle(bundleID) {
		const bundle = this._$channelsService.getBundleById(bundleID);

		if (bundle === null) {
			console.warn(`Cannot find bundle matching ID ${ bundleID }.`);
			return Promise.reject();
		}

		return new Promise((resolve, reject) => {
			const bundleChannels = bundle.channels.map(channelID => this.getChannelById(channelID));
			const ratioValidation = this._originChecker.check([
				...this.getAllSelectedChannels(true, true),
				...bundleChannels
			], null, TVOrderService.SKIP_RATIO_VALIDATION);
			
			if (ratioValidation.isValid) {
				this._selectedBundles.clear();
				this._selectedBundles.add(bundleID);

				if (bundle.isFeatured) {
					CookiesService.setCookies({name: 'hasFeaturedBundle', value: true});
					TVOrderService.$stepsManager.STEP_MIN_VALUE = TVOrderService.STEP_MIN_VALUE = 0;
				} else {
					CookiesService.setCookies({name: 'hasFeaturedBundle', value: false});
					TVOrderService.$stepsManager.STEP_MIN_VALUE = TVOrderService.STEP_MIN_VALUE = 10;
				}

				this._triggerChange('bundles');

				resolve();
			} else {
				reject();
			}
		});
	}

	removeBundle(bundleID) {
		if ( !this._selectedBundles.has(bundleID) ) {
			this._selectedBundles.clear();
			this._onDeletedBundle();
			return Promise.resolve();
		}

		return new Promise((resolve, reject) => {
			const bundle = this._$channelsService.getBundleById(bundleID);
			const bundleChannels = bundle.channels.map(channelID => this.getChannelById(channelID));
			const ratioValidation = this._originChecker.check([
				...this.getAllSelectedChannels(true, true),
				...bundleChannels
			], null, TVOrderService.SKIP_RATIO_VALIDATION);

			if (ratioValidation.isValid) {
				this._selectedBundles.delete(bundleID);
				this._onDeletedBundle();
				resolve();
			} else {
				reject();
			}
		});
	}

	getSelectedBundle() {
		if (this.selectedBundles.size) {
			const bundleID = Array.from(this.selectedBundles)[0];
			return this._$channelsService.getBundleById(bundleID);
		}
		return null;
	}

	clearBundles() {
		this._selectedBundles.clear();
	}

	isInBundle(id, selectedBundles = this.bundledChannelIDs) {
		return ~selectedBundles.indexOf(id);
	}

	channelIsFromFeaturedBundle(channelID) {
		let output = false;

		if (typeof channelID !== 'number') channelID = parseInt(channelID);

		if(this.selectedBundles){
			this._selectedBundles.forEach(bundleID => {
				const bundle = this._$channelsService.getBundleById(bundleID);
	
				if (!bundle.isFeatured) {
					return;
				} else {
					for (let i = 0; i < bundle.channels.length; i++) {
						if (bundle.channels[i] === channelID)
							output = true;
					}
				}
			});
		}

		return output;
	}
	//#endregion

	//#region Private methods
	_subscribeToChange() {
		this._originChecker.ratioValidationChange$.subscribe(
			this._onOriginValidationChange.bind(this)
		);
	}

	_onOriginValidationChange(validation) {
		if (!validation.isValid) {
			let alertMessage = this._originChecker.getErrorMessage();

			if (this._$ratioAlertModal.length && this._$ratioMessageContainer.length) {
				this._$ratioMessageContainer.html(alertMessage);
				this._$ratioAlertModal.modal({show: true});
			} else {
				alert(alertMessage);
			}
		}
	}

	_onDeletedBundle() {
		TVOrderService.$stepsManager.STEP_MIN_VALUE = TVOrderService.STEP_MIN_VALUE = 10;
		CookiesService.setCookies({name: 'hasFeaturedBundle', value: false});
		this._triggerChange('bundles');
	}

	_filterOverflowingChannels() {
		const channels = this._selectedChannels;
		const carteChannels = this.getSelectedChannels('a-la-carte');
		const carteChannelsArray = Array.from(carteChannels);
		let   output;

		if (carteChannelsArray.length <= this.currentStep && carteChannelsArray.length >= TVOrderService.STEP_MIN_VALUE) {
			output = output = {
				'base': new Set(this._$channelsService.getAll('base', true)),
				'a-la-carte': carteChannels,
				'others': new Set(),
				'premium': channels['premium']
			};
		} else if (carteChannelsArray.length < TVOrderService.STEP_MIN_VALUE) {
			output = {
				'base': new Set(this._$channelsService.getAll('base', true)),
				'a-la-carte': new Set(),
				'others': carteChannels,
				'premium': channels['premium']
			};
		} else {
			const over = carteChannelsArray.length - this.currentStep;
			const sortedChannels = this._sortChannelsByCost(carteChannelsArray);
			const cheapestChannels = sortedChannels.splice(0, over);

			output = {
				'base': new Set(this._$channelsService.getAll('base', true)),
				'a-la-carte': new Set(sortedChannels),
				'others': new Set(cheapestChannels),
				'premium': channels['premium']
			};
		}
		return output;
	}

	_sortChannelsByCost(channels) {
		// return channels.sort((channelA, channelB) => {
		// 	const costA = this.getChannelById(channelA).cost;
		// 	const costB = this.getChannelById(channelB).cost;

		// 	if (costA > costB)
		// 		return 1;
		// 	else if (costA < costB)
		// 		return -1;
		// 	else
		// 		return 0;
		// });

		return channels.sort((channelA, channelB) => {
			const chanA = this.getChannelById(channelA);
			const chanB = this.getChannelById(channelB);


			const costA = chanA.cost - chanA.extraCost;
			const costB = chanB.cost - chanB.extraCost;
			if (costA > costB)
				return 1;
			else if (costA < costB)
				return -1;
			else
				return 0;
		});
	}

	_addChannelID(id, type, validate = true) {
		id = parseInt(id);
		if (isNaN(id)) return;
		
		const allSelected = [...this.getAllSelectedChannels(true, true), this.getChannelById(id)];
		const ratioValidation = validate ? this._originChecker.check(allSelected, null, TVOrderService.SKIP_RATIO_VALIDATION) : {isValid: true};
		
		if (ratioValidation.isValid) {
			this._selectedChannels[type].add(id);
			return true;
		} else {
			return false;
		}
	}

	_removeChannelID(id, type, validate = true) {
		id = parseInt(id);
		if (isNaN(id)) return;

		const allSelected = this.getAllSelectedChannels(true, true).filter(channel => channel.ID != id);
		const ratioValidation = validate ? this._originChecker.check(allSelected, null, TVOrderService.SKIP_RATIO_VALIDATION) : {isValid: true};

		if (ratioValidation.isValid) {
			this._selectedChannels[type].delete(id);
			return true;
		} else {
			return false;
		}
	}


	// PRIVATE METHODS //
	/**
	 * @private
	 * Retrieves the selectedChannels from localStorage to see if a saved
	 * value is stored. If we find it, we set the selectedChannels value with a
	 * new Set from the retrieved JSON array from localStorage.
	 */
	_updateFromLocalStorage() {
		const channelsStorageValue = this._getFromLocalStorage('channels');
		const bundlesStorageValue = this._getFromLocalStorage('bundles');

		if (channelsStorageValue) {
			if (channelsStorageValue['a-la-carte'] && channelsStorageValue['a-la-carte'].length)
				this._selectedChannels['a-la-carte'] = Tools.convertArrayToSet(channelsStorageValue['a-la-carte']);
			
			if (channelsStorageValue['premium'] && channelsStorageValue['premium'].length)
				this._selectedChannels['premium'] = Tools.convertArrayToSet(channelsStorageValue['premium']);
		}

		if (bundlesStorageValue && bundlesStorageValue.length) {
			const bundle = this._$channelsService.getBundleById(bundlesStorageValue[0]);

			if (bundle) {
				this._selectedBundles = new Set([bundlesStorageValue[0]]);

				if (bundle.isFeatured) {
					TVOrderService.$stepsManager.STEP_MIN_VALUE = TVOrderService.STEP_MIN_VALUE = 0;
					CookiesService.setCookies({name: 'hasFeaturedBundle', value: true});
				} else {
					TVOrderService.$stepsManager.STEP_MIN_VALUE = TVOrderService.STEP_MIN_VALUE = 10;
				}
			} else {
				LocalStorage.set('selectedBundles', []);
			}
		}
	}

	_cleanLocalStorage() {
		const channelsStorageValue = this._getFromLocalStorage('channels');
		const allChannels = this._$channelsService.getAll('all', true);

		/*if (!this._isValidLocalStorageValue(channelsStorageValue)) {
			LocalStorage.delete('selectedChannels');
			LocalStorage.delete('selectedBundles');

			console.log(
				'%c Invalid localStorage value, localStorage cleared.',
				'background: #000; color: #FFF; font-size: 12px; padding: 5px;'
			);
			return;
		}*/

		for (const key in channelsStorageValue) {
			const currentCategory = channelsStorageValue[key];

			if(currentCategory){
				currentCategory.forEach((item, i) => {
						if (!allChannels.has(item) || typeof item !== 'number')
						channelsStorageValue[key].splice(i, 1);
				});
			}

		}

		/*
		if (JSON.stringify(channelsStorageValue) !== this._getFromLocalStorage('channels', true)) {
			this._setToLocalStorage('channels', channelsStorageValue);
			console.log(
				'%c Removed selected unexisting channel',
				'background: #000; color: #FFF; font-size: 12px; padding: 5px;'
			);
		}
		*/
	}

	_isValidLocalStorageValue(value) {
		const mandatoryKeys = ['a-la-carte', 'base', 'others'];
		if (value === null || value === undefined || typeof value !== 'object' || !Tools.objectHasKeys(value, mandatoryKeys))
			return false;
		
		for (const key in value) {
			const currentCategory = value[key];

			if (!(currentCategory instanceof Array))
				return false;
		}
		return true;
	}

	/**
	 * @private
	 * Will trigger the next method on selectedChannelsChange$ BehaviorSubject
	 * and pass to it the selectedChannels value in order to update its subscribers.
	 * 
	 * @return {void}
	 */
	_triggerChange(type = 'channels') {
		this._setToLocalStorage(type);
		
		this.selectedChannelsChange$.next(this.selectedChannels);
		//if (type === 'bundles'){
			this.selectedBundlesChange$.next();
			this.$promotionsService.getDuoPromotion();
		//}
		const decodeur = LocalStorage.get('decodeur');
		this.$promotionsService.getEquipementInternetPromotion();
		if(decodeur != null){
			this.$promotionsService.promoDecodeur(decodeur.qty, decodeur.buyRent);

		}
	}

	/**
	 * @private
	 * Sets the selectedChannels's value into localStorage in order to keep the value
	 * if the user reloads or leaves the page.
	 * 
	 * @return {void}
	 */
	_setToLocalStorage(type, data) {
		if (type === 'channels')
			LocalStorage.set('selectedChannels', (data || this._formatChannelsForLocalStorage()));
		else if (type === 'bundles')
			LocalStorage.set('selectedBundles', Tools.convertSetToArray(this.selectedBundles));
	}

	/**
	 * @private
	 * Parses the selectedChannels's value from localStorage in order to keep the value
	 * if the user reloads or leaves the page.
	 * 
	 * @return {void}
	 */
	_getFromLocalStorage(type, asString = false) {
		if (type === 'channels' || type === 'bundles')
			return LocalStorage.get((type === 'channels' ? 'selectedChannels' : 'selectedBundles'), asString);
	}

	_formatChannelsForLocalStorage() {
		const output = {...this._selectedChannels};

		for (const key in output) {
			const currentValue = output[key];

			if (currentValue instanceof Set)
				output[key] = Tools.convertSetToArray(currentValue);
		}
		return output;
	}

	/**
	 * @private
	 * @return {number}
	 */
	_hasParent(channel) {
		return channel.post_parent !== 0;
	}

	_logRequestCount() {
		console.clear();

		clearTimeout(this._callCountDebounce);
		setTimeout(() => {this.callCount = 0;}, 1000);
	}
	//#endregion
}