{{#if (ifCond (ifCond downloadSpeed '!==' null) '&&' (ifCond uploadSpeed '!==' null))}}
{{#unless (ifCond (isNaN downloadSpeed) '||' (isNaN uploadSpeed))}}
<div class="QualifiedSpeeds">
	<div class="QualifiedSpeeds__container">
		<div class="QualifiedSpeeds__col QualifiedSpeeds__col--title">
			<h2 class="QualifiedSpeeds__title">
				{{ translate 'Max. selon' 'Max. in' }}
				<span class="QualifiedSpeeds__title-bold">{{ translate 'ta localité' 'your location' }}</span>
			</h2>
		</div>
		<div class="QualifiedSpeeds__col QualifiedSpeeds__col--content">
			<ul class="QualifiedSpeeds__speed-list">
				{{#if (ifCond downloadSpeed '!==' null)}}
				<li class="QualifiedSpeeds__speed-item">
					<svg class="QualifiedSpeeds__speed-icon">
						<use xlink:href="#icon-ic_download"></use>
					</svg>
					<span class="QualifiedSpeeds__speed-title" aria-hidden="true">
						<span class="QualifiedSpeeds__speed-number">{{ translateNum downloadSpeed }}</span> Mbit/s
					</span>
					<span class="sr-only">{{ downloadSpeedmbitsLabel }}</span>
				</li>
				{{/if}}

				{{#if (ifCond uploadSpeed '!==' null)}}
				<li class="QualifiedSpeeds__speed-item">
					<svg class="QualifiedSpeeds__speed-icon">
						<use xlink:href="#icon-ic_upload"></use>
					</svg>
					<span class="QualifiedSpeeds__speed-title" aria-hidden="true">
						<span class="QualifiedSpeeds__speed-number">{{ translateNum uploadSpeed }}</span> Mbit/s
					</span>
					<span class="sr-only">{{ uploadSpeedmbitsLabel }}</span>
				</li>
				{{/if}}
			</ul>
		</div>
	</div>
</div>
{{/unless}}
{{/if}}