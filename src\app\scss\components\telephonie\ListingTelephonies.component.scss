.ListingTelephonies {
    &__title {
        text-transform: uppercase;
        font: 900 45px/50px $f-primary;
        margin-bottom: 30px;
        @media (max-width: 991px) {
            font: 900 26px/32px $f-primary;
        }
        @media (max-width: 767px) {
            font: 900 22px/24px $f-primary;
            margin-bottom: 40px;
        }

    }
    &__notice {
        font: 400 12px/16px $f-primary;
        margin-bottom: 100px;
        @media (max-width: 991px) {
            margin-bottom: 70px;
        }
        @media (max-width: 767px) {
            margin-bottom: 40px;
        }
    }

    &__listing {
        padding-top: 100px;
        padding-bottom: 100px;
        @media (max-width: 991px)  {
            padding-top: 70px;
            padding-bottom: 70px;
        }
        @media (max-width: 767px)  {
            padding-top: 40px;
            padding-bottom: 40px;
        }
    }

    &__red-link{
        color: #BE2323;
    }
}

.ListingTelephonies, .PostCardItem {
    &__forfaitwrapper {
        min-height: 548px;
        box-shadow: 0 2px 4px 0 rgba(#000, 0.5);
        width: 100%;
        border-radius: 5px;
        background: $c-white;
        .ButtonEffect {
            position: absolute;
            left: 50%;
            bottom: -30px;
            transform: translateX(-50%);
            @media (max-width: 991px) {
                bottom: -20px;
            }
        }
        @media (max-width: 767px) {
            margin-bottom: 140px;
            &--last {
                margin-bottom: 30px;
            }
        }
    }
    &__top {
        min-height: 203px;
        padding: 40px;
        background: $c-grey;
        width: 100%;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        p {
            color: $c-white;
            text-transform: uppercase;
            margin-top: 20px;
            font: 900 45px/40px $f-primary;
            position: relative;
            margin: 0;
            text-align: center;
            max-width: 380px;
            @media (max-width: 991px) {
                font: 900 35px/30px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 30px/35px $f-primary;
                padding: 0 15px;

            }
            .is-doodled {
                display: inline-block;
            }
            .is-doodled--underlined {
                &:after {
                    top: auto;
                    bottom: -12px;
                    height: 9px;
                    margin-top: -10px;
                }
            }
            .arrowed {
                position: absolute;
                right: 0;
                bottom: -36px;
                text-transform: none;
                font: 400 12px/16px $f-secondary;

                &:before {
                    width: 20px;
                    height: 20px;
                    background: url("../../../img/Icon/drawing-arrow04-white.svg") center center no-repeat;
                    background-size: 100%;
                    content: "";
                    position: absolute;
                    display: inline-block;
                    left:-24px;
                    top: -6px;
                    transform: rotate(-15deg);
                }

                @media (max-width: 400px) {
                    right: 15px;
                }
            }
        }
    }
    &__bottom {
        padding: 30px;
        ul {
            li {
                a {
                    color: $c-primary;
                    transition: $t-primary;
                    text-decoration: underline;
                    &:hover {
                        color: $c-grey;
                    }
                }
            }
        }
    }
}

.PostCardItem {
    position: relative;
    display: inline-block;
    padding: 58px 0 30px;

    &__forfaitwrapper {
        position: relative;
    }

    &__top {
        padding: 0 30px;
        @media (max-width: 600px) {
            height: 150px;
        }
        @media (max-width: 400px) {
            padding: 0;
        }

        p {
            max-width: none;
        }

        .arrowed.arrowed {
            left: 50%;
            bottom: -30px;
            transform: translateX(-50%);
            white-space: nowrap;

            sup {
                top: 0em;
                font-size: 16px;
            }
        }
    }

    &__bottom {
        text-align: left;
        padding-bottom: 40px;

        @media (max-width: 1024px) {
            padding: 15px;
            padding-bottom: 30px;
        }

        @media (max-width: 400px) {
            padding: 20px 15px 30px;
        }
    }

    &__title {
        &--long {
            font-size: 22px;
            line-height: 1.2;
            display: inline-block !important;
        }

        &--extra-long {
            font-size: 19px;
            line-height: 1.2;
            display: inline-block !important;
        }
    }

    &__btn {
        width: 50%;

        &:after {
            background-color: $c-medium-grey;
        }
        &--disabled {
            pointer-events: none;
        }
    }

    .RedCircle {
        width: 116px;
        height: 116px;
        top: -58px;

        &__type {
            font-size: 12px;
        }

        &__price {
            font: 900 32px/1 $f-primary;
        }
    }
}

.RedCircle {
    width: 130px;
    height: 130px;
    background: url('../../../img/back-red-circle.png') center center no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -65px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    &__price  {
        position: relative;
        border-top: 1px solid $c-white;
        color: $c-white;
        margin-top: 5px;
        padding-top: 5px;
        font: 900 36px/36px $f-primary;
        width: 80px;
        sup {
            position: relative;
            top: -13px;
            font: 900 12px $f-primary;
        }
    }
    &__mois {
        position: absolute;
        font: 400 8px/12px $f-secondary;
        left: 2px;
        bottom: -8px;
        width: 40px;

    }
    &__type {
        color: $c-white;
        font: 600 15px $f-primary;
    }
}

.single-telephonies {
    .SinglePrice {
        min-height: auto;
        box-sizing: border-box;
        padding-top: 55px;
        padding-bottom: 55px;
        flex-direction: column;

        .Listing__recap {
            flex: auto;
        }
        @media (max-width: 991px) {
            min-height: auto;
            padding: 20px 0;
            margin-top: 40px;
        }
        .ButtonEffect {
            @media (max-width:991px) {
                margin-top: 10px;
            }
        }
        .Listing__prix {
            span {
                top: inherit;
                bottom: 25px;
            }
        }
    }
}

.ListingTelephoniees__wrapper-even {
    padding-right: 35px;
    margin-top: 150px;
    @media (max-width: 767px) {
        padding-right: 15px;
    }
}
.ListingTelephoniees__wrapper-odd {
    padding-left: 35px;
    margin-top: 150px;
    @media (max-width: 767px) {
        padding-left: 15px;
    }
}
.ListingTelephoniees__wrapper-first-row {
    margin-top: 0px;
}
