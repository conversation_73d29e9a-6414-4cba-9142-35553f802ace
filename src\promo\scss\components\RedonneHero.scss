.RedonneHero {
	padding: 14px 0 61px;
	background: {
		color: $c-primary;
		image: url('../../img/ebox-bg--red.jpg');
		size: 400px;
	};

	@media (max-width: 799px) {
		padding-top: 0;
		padding-bottom: 67px;
	}

	&__title {
		position: relative;
		font-size: 48px;
		font-weight: 900;
		line-height: 1.3;
		margin-bottom: 47px;

		&-part {
			color: #FFF;

			.red {
				color: $c-primary-dark;
			}
		}

		.doodle-img {
			bottom: 66px;
			right: 192px;
			width: 83px;
			height: 80px;
			transform: rotate(6deg);
	
			@include applyAtRoot('html:lang(en)') {
				right: 82px;
			}
		}

		@media (min-width: 960px) {
			@include applyAtRoot('html:lang(en)') {
				font-size: 61px;
			}
		}
		@media (max-width: 1085px) {
			padding-right: 116px;

			.doodle-img {
				right: 16px;
				bottom: auto;
				top: 50%;
				transform: translateY(-50%) rotate(6deg);
				
				@include applyAtRoot('html:lang(en)') {
					right: 16px;
				}
			}
		}
		@media (max-width: 799px) {
			font-size: 32px;
			
			br {
				display: none;
			}
		}
		@media (max-width: 440px) {
			padding-right: 0;
			margin-bottom: 19px;
			font-size: 10.09vw;
			
			.doodle-img {
				position: relative;
				margin-left: 25px;
				width: 80px;
				height: 54px;
				bottom: auto;
				right: auto;
				transform: rotate(-7deg);
			}
			
			@include applyAtRoot('html:lang(en)') {
				margin-bottom: 35px;
				font-size: 10.68vw;

				.doodle-img {
					position: absolute;
					right: -10px;
					top: 49px;
					bottom: auto;
					transform: rotate(16deg);
				}
			}
		}
		@media (max-width: 379px) {
			@include applyAtRoot('html:lang(en)') {
				margin-bottom: 35px;

				br {
					display: block;
				}
			}
		}
	}	

	&__info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;

		@media (max-width: 799px) {
			flex-direction: column;
			margin: -15px auto;
			max-width: 350px;
		}
		@media (max-width: 400px) {
			max-width: 250px;
		}
	}

	&__info-col {
		display: inline-block;

		&:first-child {
			position: relative;
			flex: 0 0 28.2%;

			&:after {
				content: '=';
				position: absolute;
				top: 50%;
				right: 0;
				font: 900 48px/1 $f-primary;
				color: $c-primary-dark;
				transform: translateY(-50%);
			}

			.doodle-img {
				width: 66px;
				height: 80px;
				right: 36px;
				bottom: 32px;
				transform: rotate(27deg);

				@media (max-width: 960px) {
					width: 65px;
					height: 46px;
					right: auto;
					left: 132px;
					bottom: 32px;
				}
				@media (max-width: 799px) {
					left: auto;
					right: 37px;
					bottom: 46px;

					@include applyAtRoot('html:lang(fr)') {
						right: 26px;
						bottom: 58px;
						transform: rotate(24deg);
					}
				}
			}

			@media (max-width: 799px) {
				max-width: 250px;
				margin-right: auto;
			}
		}

		&:nth-child(3) {
			flex: 0 0 30.9%;
		}

		@include applyAtRoot('html:lang(en)') {
			&:first-child {
				flex: 0 0 26.2%;
			}
	
			&:nth-child(3) {
				flex: 0 0 36%;
			}
		}

		@media (max-width: 840px) {
			&:first-child {
				flex: 0 0 210px;

				@include applyAtRoot('html:lang(en)') {
					flex: 0 0 210px;
				}
			}
		}
		@media (max-width: 799px) {
			flex-basis: auto !important;
			padding: 15px 0;
			width: 100%;
		}
	}

	&__info-title {
		font-size: 26px;
		line-height: 1.2;
		font-weight: 700;
		color: #FFF;

		&--small {
			font-size: 22px;
		}

		@media (max-width: 960px) {
			font-size: 20px;

			&--small {
				font-size: 16px;
			}
		}
		
		@include applyAtRoot('html:lang(fr)') {
			@media (max-width: 799px) {
				font-size: 25px;
	
				&--small {
					font-size: 25px;
				}
			}
		}

		@include applyAtRoot('html:lang(en)') {
			@media (max-width: 799px) {
				font-size: 21px;
	
				&--small {
					font-size: 21px;
				}
			}
		}
	}

	&__counter {
		color: #FFF;
		
		&-title {
			color: #FFF;
			font: 900 14px/1 $f-primary;
			text-transform: uppercase;
			margin-top: -30px;
			margin-bottom: 7px;

			@media (max-width: 799px) {
				margin-top: 0;
			}
		}

		&-number {
			position: relative;
			display: inline-block;
			font: 700 60px $f-primary;
			width: 75px;
			height: 85px;
			line-height: 85px;
			text-align: center;

			&:not(:last-child) {
				margin-right: 2px;
			}

			&:after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: {
					repeat: no-repeat;
					position: left top;
					size: 100% 100%;
				}
			}

			&:nth-child(1) {
				&:after {
					background-image: url('../../img/counter-box1.svg');
				}
			}
			&:nth-child(2) {
				&:after {
					background-image: url('../../img/counter-box2.svg');
				}
			}
			&:nth-child(3) {
				&:after {
					background-image: url('../../img/counter-box3.svg');
				}
			}
			&:nth-child(4) {
				&:after {
					background-image: url('../../img/counter-box4.svg');
				}
			}

			@media (max-width: 960px) {
				font-size: 50px;
				width: 58px;
				height: 72px;
				line-height: 72px;
			}
		}
	}
}