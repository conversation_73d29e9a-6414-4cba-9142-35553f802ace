import { Directive } from '@core/directive.js';

export class CloseButtonDirective extends Directive {
	public static selector: string = '[close-button]';

	//#region Host's attributes
	public attrs: {
		storageKey: string;
		closeElSelector: string;
	};
	//#endregion

	//#region Private properties
	private closingEls: NodeListOf<HTMLElement> = document.querySelectorAll(
		this.attrs.closeElSelector
	);
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLElement) {
		super(host, [
			{name: 'close-storage-key', as: 'storageKey', required: true},
			{name: 'close-elements-selector', as: 'closeElSelector', required: true}
		]);
		this.onInit();
	}
	//#endregion

	//#region Private methods
	private onInit(): void {
		this.bindEvents();
		this.updateElementsVisibility();
	}

	private bindEvents(): void {
		this.host.addEventListener('click', this.onHostClick.bind(this));
	}

	private onHostClick(): void {
		this.setStorageVal();
		this.updateElementsVisibility();
	}

	private updateElementsVisibility(): void {
		const hasClosed: boolean = this.getStorageVal();

		if (hasClosed) {
			this.hideElements();
		} else {
			this.showElements();
		}
	}

	private hideElements(): void {
		for (let i: number = 0; i < this.closingEls.length; i++) {
			this.closingEls[i].style.display = 'none';
		}
	}

	private showElements(): void {
		for (let i: number = 0; i < this.closingEls.length; i++) {
			this.closingEls[i].style.display = 'block';
		}
	}

	private setStorageVal(): void {
		window.sessionStorage.setItem(
			`close-btn-item__${ this.attrs.storageKey }`,
			'closed'
		);
	}

	private getStorageVal(): boolean {
		return window.sessionStorage.getItem(`close-btn-item__${ this.attrs.storageKey }`) === 'closed';
	}
	//#endregion
}