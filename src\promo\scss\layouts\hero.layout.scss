.hero {
	overflow: hidden;
	background: {
		color: $c-primary;
		image: url('../../img/ebox-bg--red.jpg');
		size: 400px;
	};
	

	&__main-container {
		width: calc(100% - 160px);

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			width: 100%;
			padding: 0 50px;
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			padding: 16px 7px 0;
		}
	}
	
	&__title-container {
		font-size: 0;

		@media (max-width: 450px) {
			width: 85%;
			max-width: 500px;
		}
	}

	&__title-row {
		margin: 0 -74px 0 -25px;

		&--mobile {
			display: none;
		}

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			margin: 0;
		}

		@media (max-width: 450px) {
			display: none;

			&--mobile {
				display: block;
			}
		}
	}

	&__title-img {
		display: inline-block;
		vertical-align: middle;
		max-width: 100%;

		&--en {
			display: none;
			margin-bottom: -30px;
		}

		@include fromRootSelector(':lang(en_CA)') {
			display: none;

			&--en {
				display: block;
			}
		}
	}

	&__deal-list {
		display: flex;
		justify-content: space-between;

		&.carousel-active {
			overflow: hidden;
		}

		.owl-dots {
			width: 160px;
			padding: 0;
		}
	}
	
	&__deal-item {
		position: relative;
		display: inline-block;
		margin-top: 90px;
		text-align: left;

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			margin-top: 40px;
		}

		&:before, &:after {
			content: '';
			position: absolute;
			display: block;
			background: {
				repeat: no-repeat;
			}

			@media (max-width: map-get($breakpoints, carouselTablet)) {
				content: none;
			}
		}

		&:after {
			bottom: 100%;
			width: 100%;
			height: 90px;
			background: {
				image: url('../../img/arrow-down-center.svg');
				position: 27px 25px;
				size: 35px;
			};
		}

		&:not(.hero__deal-item--dynamic-doodles) {
			&:nth-child(1) {
				&:before {
					right: 90%;
					width: 115px;
					height: 110px;
					background: {
						image: url('../../img/lui-yest-pas-cher.svg');
					};

					@include fromRootSelector(':lang(en_CA)') {
						background-image: url('../../img/for-tight-budgets.svg');
					}
				}

				&:after {
					background: {
						image: url('../../img/arrow-down-left.svg');
						position: center 20px;
						size: 30px;
					};
				}
			}

			&:nth-child(2) {
				&:before {
					top: 14px;
					left: 86%;
					width: 136px;
					height: 110px;
					background: {
						image: url('../../img/gros-vendeur.svg');
					};

					@include fromRootSelector(':lang(en_CA)') {
						background-image: url('../../img/this-one-s-popular.svg');
						top: 10px;
					}
				}
			}

			&:nth-child(3) {
				&:before {
					top: 10px;
					left: 80%;
					width: 136px;
					height: 110px;
					background: {
						image: url('../../img/la-coche-au-dessus.svg');
					};

					@include fromRootSelector(':lang(en_CA)') {
						background-image: url('../../img/a-step-up.svg');
					}
				}

				&:after {
					background: {
						image: url('../../img/arrow-down-right.svg');
						position: right 40px top 4px;
						size: 22px;
					};
				}
			}
		}


		/* Rotating randomly the "per-month" icon
		to give a more natural and sketchy look */
		@for $i from 1 through 3 {
			&:nth-child(#{ $i }) {
				.deal__per-month-icon {
					transform: rotate(#{random(12) - 6}deg);
				}
			}
		}
	}
}