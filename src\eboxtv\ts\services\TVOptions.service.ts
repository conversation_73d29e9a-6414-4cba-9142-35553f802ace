import { LocalStorage } from '@common/ts/services/LocalStorage';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
export class TVOptionsService {
	//#region Singleton distribution
	static instance: TVOptionsService;
	static getInstance(): TVOptionsService {
		if (!TVOptionsService.instance) {
			TVOptionsService.instance = new TVOptionsService;
		}

		return TVOptionsService.instance;
	}
	//#endregion

	//#region Private properties
	private optionList: EBTVAvailableOptions = wpLocalizedVars.eboxtvOptions;

	private selectedOptions: EBTVSelectedOptions = this.getStoredOptions() || {
		cloudSpace: null
	};
	//#endregion

	//#region Observables
	private optionChangeSubject$ = new BehaviorSubject(this.selectedOptions);

	public optionChange$ = this.optionChangeSubject$.asObservable();
	//#endregion

	//#region Public methods
	public selectOption(name: keyof EBTVOptions, id: string | number): void {
		this.selectedOptions[name] = this.getOption(name, id);
		this.optionChangeSubject$.next(this.selectedOptions);
		this.storeOptions();
	}

	public getOption(name: keyof EBTVOptions, id: string | number): EBTVOption {
		return this.optionList[name].find(opt => opt.ID === id);
	}

	public getSelectedOptions(): EBTVSelectedOptions {
		return this.selectedOptions;
	}
	//#endregion

	//#region Private methods
	private getStoredOptions(): EBTVSelectedOptions {
		return LocalStorage.get('eboxTVOptions');
	}

	private storeOptions(): void {
		LocalStorage.set('eboxTVOptions', this.selectedOptions);
	}
	//#endregion
}

export interface EBTVOption {
	ID: number;
	title: string;
	price: number;
	sku?: Text;
}

interface EBTVOptions {
	cloudSpace: any;
}

export interface EBTVAvailableOptions extends EBTVOptions {
	cloudSpace: EBTVOption[];
}

export interface EBTVSelectedOptions extends EBTVOptions {
	cloudSpace: EBTVOption;
}
