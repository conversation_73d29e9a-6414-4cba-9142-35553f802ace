import { AutoscrollDirective } from '@common/ts/directives/autoscroll.directive';

export class AutoscrollToInputDirective extends AutoscrollDirective {
	public static selector: string = '[autoscroll-to-input]';

	constructor(host: HTMLElement) {
		super(host);
		this.onScrollToTargetComplete = this.onScrollToTargetComplete.bind(this);
	}

	protected onScrollToTargetComplete(): void {
		super.onScrollToTargetComplete();
		this.$targetElement[0].focus();
	}
}
