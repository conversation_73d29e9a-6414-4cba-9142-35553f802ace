<?xml version="1.0" encoding="UTF-8"?>
<svg width="82px" height="72px" viewBox="0 0 82 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 48.2 (47327) - http://www.bohemiancoding.com/sketch -->
    <title>Module / Flag / Diagonal - Black</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Symbol" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Module-/-Flag-/-Diagonal---Black">
            <polygon id="Path-2-Copy" fill="#1F1F1F" points="78 72 82 72 78 65"></polygon>
            <polygon id="Path-2-Copy-2" fill="#1F1F1F" points="0 4 7 4 0 0"></polygon>
            <polygon id="Rectangle" fill="#343434" points="82 72 0 0 34 0 82 42.8108108"></polygon>
            <path d="M33.164,31.772 L33.164,23.828 C33.164,23.708 33.26,23.6 33.392,23.6 L36.128,23.6 C37.604,23.6 38.816,24.8 38.816,26.252 C38.816,27.74 37.604,28.952 36.14,28.952 L34.4,28.952 L34.4,31.772 C34.4,31.892 34.292,32 34.172,32 L33.392,32 C33.26,32 33.164,31.892 33.164,31.772 Z M34.4,27.776 L36.056,27.776 C36.884,27.776 37.58,27.104 37.58,26.24 C37.58,25.424 36.884,24.8 36.056,24.8 L34.4,24.8 L34.4,27.776 Z M39.908,31.772 L39.908,27.428 C39.908,27.308 40.016,27.2 40.136,27.2 L40.484,27.2 C40.568,27.2 40.664,27.272 40.688,27.368 L40.82,27.776 C40.904,27.656 41.348,27.08 42.224,27.08 C42.68,27.08 43.232,27.2 43.1,27.524 L42.812,28.112 C42.752,28.244 42.632,28.268 42.524,28.22 C42.452,28.184 42.272,28.136 42.116,28.136 C41.42,28.136 41.084,28.64 41.036,28.724 L41.036,31.772 C41.036,31.976 40.904,32 40.712,32 L40.136,32 C40.016,32 39.908,31.892 39.908,31.772 Z M43.58,29.588 C43.58,28.232 44.66,27.08 46.004,27.08 C47.348,27.08 48.428,28.232 48.428,29.588 C48.428,30.968 47.348,32.12 46.004,32.12 C44.66,32.12 43.58,30.968 43.58,29.588 Z M44.648,29.588 C44.648,30.416 45.248,31.088 46.004,31.088 C46.76,31.088 47.36,30.416 47.36,29.588 C47.36,28.784 46.76,28.124 46.004,28.124 C45.248,28.124 44.648,28.784 44.648,29.588 Z M49.628,31.772 L49.628,27.428 C49.628,27.308 49.736,27.2 49.856,27.2 L50.192,27.2 C50.3,27.2 50.36,27.26 50.396,27.344 L50.528,27.752 C50.636,27.644 51.188,27.08 52.112,27.08 C52.808,27.08 53.312,27.356 53.66,27.848 C53.792,27.716 54.488,27.08 55.4,27.08 C56.864,27.08 57.38,28.112 57.38,29.36 L57.38,31.772 C57.38,31.892 57.284,32 57.152,32 L56.48,32 C56.348,32 56.252,31.892 56.252,31.772 L56.252,29.312 C56.252,28.556 55.904,28.124 55.256,28.124 C54.524,28.124 54.104,28.64 54.02,28.712 C54.044,28.82 54.056,29.048 54.056,29.288 L54.056,31.772 C54.056,31.892 53.948,32 53.828,32 L53.192,32 C53.06,32 52.964,31.892 52.964,31.772 L52.964,29.312 C52.964,28.544 52.616,28.124 51.944,28.124 C51.224,28.124 50.816,28.688 50.756,28.844 L50.756,31.772 C50.756,31.892 50.648,32 50.528,32 L49.856,32 C49.736,32 49.628,31.892 49.628,31.772 Z M58.52,29.588 C58.52,28.232 59.6,27.08 60.944,27.08 C62.288,27.08 63.368,28.232 63.368,29.588 C63.368,30.968 62.288,32.12 60.944,32.12 C59.6,32.12 58.52,30.968 58.52,29.588 Z M59.588,29.588 C59.588,30.416 60.188,31.088 60.944,31.088 C61.7,31.088 62.3,30.416 62.3,29.588 C62.3,28.784 61.7,28.124 60.944,28.124 C60.188,28.124 59.588,28.784 59.588,29.588 Z" id="✏️-Text" fill="#FFFFFF" transform="translate(48.266000, 27.860000) rotate(-318.000000) translate(-48.266000, -27.860000) "></path>
        </g>
    </g>
</svg>