/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { Tools } from '@common/ts/services/Tools.service';
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { CoreTools } from '@core/helpers';
import { PromotionsService } from '../services/promotions.service';
export class CommandeTelephoniqueDirective extends Directive {

    // INSTANCE DU SERVICE D PRDRE
    _$OrderService = OrderOnlineService.getInstance()
    _$PromoService = PromotionsService.getInstance()

    //SELECTEUR DE LA DIRECTIVE
    static selector = '[commandeTelephonique]';

    $form;
    $valid = true;
    // $options = {};
    // $modemRent = document.getElementById('modemRent');
    // $modemBuy = document.getElementById('buyModem');
    // $selectedModem = '';
    // $selectedRouter = '';
    selections = {};

    equipements = [];

    modem = {};
    router = {};


    sectionVOIP = document.querySelector('.voip');
    sectionVOIPErreur = document.getElementById('VOIPListe');
    sectionForfait = document.getElementById('forfaitListe');
    voipDevice = ''
    internetPlan = {};

    btnValider = document.querySelector('.order-summary__navbutton');
    btnValiderMobile = document.querySelector('.eboxtv-navbutton');
    tvStepTrigger = document.getElementById("tv-trigger");
    isTvAvailable = typeof (this.tvStepTrigger) != 'undefined' && this.tvStepTrigger != null;


    // listModem = document.getElementById('modemList');
    // listRouter = document.getElementById('routerList');

    constructor(host) {
        super(host, []);
        this._onInit();
    }

    _onInit() {
        let stepValidation = this._$OrderService.getStepValidation();

        if (this.isTvAvailable && !stepValidation.tv) {
            if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
        } else {
            this._$OrderService.initInternetPlan();

            this.internetPlan = this._$OrderService._retInternetOrder();
            this.$form = document.forms['formTelephonie'];
            if(this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'){
                this.equipements = {}
                this._$OrderService.saveEquipementPhone({});
                this.sectionVOIP.remove();
            }
            else{
                this.voipDevice = this.$form.elements['choixVoip'][0];
            }
            this._$OrderService.initPhonePlan();

            this.selections = this._$OrderService._retPhoneOrder();
            this.equipements = this._$OrderService._retPhoneEquipement();
            this.initForm();


            this.$form.addEventListener('change', function (evt) {
                this.changeForm(evt.target);

            }.bind(this))

            this.btnValider.addEventListener('click', function (evt) {
                evt.preventDefault();
                this.validateForm();
                
                this._$OrderService.setStepValidation('phone', this.$valid);
                const adr = LocalStorage.get('qualificationAddresse');
                if(adr.pc.toLowerCase() == 'on'){
                    this._$OrderService.setStepValidation('tv', this.$valid);
                }
                if(this.$valid){
                    window.location.href = evt.currentTarget.href;

                }
            }.bind(this))

            
            this.btnValiderMobile.addEventListener('click', function (evt) {
                evt.preventDefault();
                this.validateForm();
                
                this._$OrderService.setStepValidation('phone', this.$valid);
                const adr = LocalStorage.get('qualificationAddresse');
                if(adr.pc.toLowerCase() == 'on'){
                    this._$OrderService.setStepValidation('tv', this.$valid);
                }
                if(this.$valid){
                    window.location.href = evt.currentTarget.href;
                }
            }.bind(this))
        }
    }

    changeForm(input) {
        if (input.name == 'phonePlan') {
            this.toggleSelectedPhonePlan(input, 'three-blocs__items--selected');
        }
        else if (input.name == 'choixVoip') {
            this.toggleVoip(input, 'three-blocs__items--selected');
        }

        // If we choose no service and there is already an equipment, we delete it.
        if(this.selections.type == 'none'){
            if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){
                document.getElementById('radio10').checked = true;
                this.toggleVoip(document.getElementById('radio10'), 'three-blocs__items--selected');
            } 

        }

        this.toggleSelections();

        // Save
        this._$OrderService.savePhoneService(this.selections);
        this._$OrderService.saveEquipementPhone(this.equipements);


    }

    toggleSelectedPhonePlan(input, selectedClass) {
        this.sectionForfait.classList.remove('error')
        for (let i = 0; i < this.$form.elements[input.name].length; i++) {
            const element = this.$form.elements[input.name][i]
            const parent = element.parentElement.parentElement;
            if (element.checked) {
                parent.classList.add(selectedClass);
                if (element.getAttribute('data-type') === 'Interurbains' || element.getAttribute('data-type') === 'Line with long distance') {
                    this.selections = {
                        type: element.getAttribute('data-type'),
                        planCode: element.value,
                        lineCode: this.$form.elements['phonePlan'][0].value,
                        planPrice: element.getAttribute('data-price'),
                    }
                }
                else if (element.getAttribute('data-type') === 'Ligne standard' || element.getAttribute('data-type') === 'Standard line') {
                    this.selections = {
                        type: element.getAttribute('data-type'),
                        planCode: '',
                        lineCode: element.value,
                        planPrice: element.getAttribute('data-price'),
                    }
                }
                else {
                    this.selections = {
                        type: 'none',
                        planCode: 'none',
                        lineCode: 'none',
                        planPrice: '0.00',
                    }
                }
            }
            else {
                parent.classList.remove(selectedClass);
            }
        }
        this._$PromoService.getEquipementInternetPromotion();
    }

    toggleVoip(input, selectedClass) {
        if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){
            this.sectionVOIPErreur.classList.remove('error');
            this.equipements = []
            for (let i = 0; i < this.$form.elements[input.name].length; i++) {
                const element = this.$form.elements[input.name][i]
                const parent = element.parentElement.parentElement;
                if (element.checked) {
                    parent.classList.add(selectedClass);
                    this.router = {
                        buyRent: element.value,
                        price: element.getAttribute('data-price'),
                        code: element.getAttribute('data-sku'),
                        sit: element.getAttribute('data-sit'),
                        discount: element.getAttribute('data-sit-discount'),
                        qty: 1,
                        service: 'voip',// a confirmer
                        type: 'grandStream' // a confirmer
                    }
                }
                else {
                    parent.classList.remove(selectedClass);
                }
            }

            this.equipements.push(this.router);
        }
    }


    initForm() {
        if (this.selections.type) {
            this.toggleSelections();
        }

        if (this.equipements.length > 0) {

            let input = ''
            switch (this.equipements[0].buyRent) {
                case 'own':
                    input = this.$form.elements['choixVoip'][2];
                    input.checked = true;
                    break;
                case 'buy':
                    input = this.$form.elements['choixVoip'][1];
                    input.checked = true;
                    break;
                case 'rent':
                    input = this.$form.elements['choixVoip'][0];
                    input.checked = true;
                    break;

                default:
                    break;
            }

            this.toggleVoip(input, 'three-blocs__items--selected')
        }
    }

    toggleSelections() {
        let input = '';
        switch (this.selections.type) {
            case 'none':
                input = this.$form.elements['phonePlan'][2];
                input.checked = true;
                this.sectionVOIP.classList.add('hidden');
                if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){
                    this.voipDevice.required = false;
                }
                break;
            case 'Interurbains':
            case 'Line with long distance':
                input = this.$form.elements['phonePlan'][1];
                input.checked = true;
                this.sectionVOIP.classList.remove('hidden');
                if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){

                    this.voipDevice.required = true;
                }
                break;
            case 'Ligne standard':
            case 'Standard line':
                input = this.$form.elements['phonePlan'][0];
                input.checked = true;
                this.sectionVOIP.classList.remove('hidden');
                if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){

                    this.voipDevice.required = true;
                }
                break;
            default:
                break;
        }

        this.toggleSelectedPhonePlan(input, 'three-blocs__items--selected');
    }


    validateForm() {
        this.$valid = true;
        if (this.$form.elements['phonePlan'].value.trim() !== '') {
            this.sectionForfait.classList.remove('error');
            if (this.$form.elements['phonePlan'].value == 'none') {
                // this.$valid = true;
            }
            else {
                if(this.internetPlan.cableTech != 'DSL' && this.internetPlan.technology != 'DSL'){
                    if (this.$form.elements['choixVoip'].value.trim() !== '') {
                        this.sectionVOIPErreur.classList.remove('error');
                        this.$valid = true;
                    }
                    else {
                        this.sectionVOIPErreur.classList.add('error');
                        this.scrollTo(this.sectionVOIPErreur);
                        this.$valid = false;
                    }
                }
                else{
                    this.$valid = true;
                }


            }
        }
        else {
            this.sectionForfait.classList.add('error');
            this.scrollTo(this.sectionForfait);
            this.$valid = false
        }
    }

    scrollTo(el) {
        el.scrollIntoView({ behavior: "smooth" });
    }

}