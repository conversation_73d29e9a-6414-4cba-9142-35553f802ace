import { Directive, AttributeRequest } from '@core/directive';

export class ReturnToReferer extends Directive {
    static selector: string = '[return-to-referer]';

    attrs: {
        href?: string;
        urlSuffix?: string;
    };

    constructor(host: HTM<PERSON><PERSON>, childAttrRequests: AttributeRequest[] = []) {
        super(host, [
            'href',
            ...childAttrRequests
        ]);

		this.switchHref();
    }

    private switchHref(): void {
        if ( this.attrs.href ) {
            const isSameDomain: boolean = !!document.referrer.match(location.origin);

            if (isSameDomain) {
                this.attrs.href = document.referrer;
            }
        } else {
            this.attrs.href = document.referrer;
        }
    }
}