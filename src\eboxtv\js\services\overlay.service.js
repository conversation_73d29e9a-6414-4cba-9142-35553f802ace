/* global jQuery */
import { Subject } from 'rxjs/Subject';
import { TweenLite } from 'gsap/TweenLite';
import { Power2 } from 'gsap/EasePack';
import { Tools } from '@common/ts/services/Tools.service';

class OverlayService {
	_host  = document.getElementById('overlay');
	_$host = jQuery(this._host);
	_scrollBlocked = false;
	_requesters = [];


	// PUBLIC PROPERTIES //
	SHOW_DURATION  = 0.3;
	HIDE_DURATION  = 0.25;

	overlayChange$ = new Subject();

	isShown = false;


	// INIT //
	constructor() {
		if (!this._host) return;
		this._init();
	}
	_init() {
		this._hideOnLoad();
		this._bindEvents();
	}

	_bindEvents() {
		this._$host.on('click', this._onHostClick.bind(this));
	}

	_onHostClick(evt) {
		if (evt.target === this._host)
			this._emitChange('click');
	}
	
	// PUBLIC PROPERTIES //
	show({time = this.SHOW_DURATION, transparent = false, requestingElement, blockScroll = false, injectInParent = document.body}) {
		/* Since requestingElement is madatory, we throw an error if 
		it's undefined. */
		if (requestingElement === undefined)
			throw new Error('OverlayService.hide() has been called without a requestingElement.');

		/* Adding the requesting element to _requesters Array. */
		this._addRequester(requestingElement);

		this._emitChange('showing');

		this._setZIndex(requestingElement);
		this._display();

		if (blockScroll)
			this._blockScroll();

		this._injectInParent(injectInParent);

		TweenLite.to(this._host, time, {
			alpha: transparent ? 0 : 1,
			ease: Power2.easeInOut,
			onComplete: this._emitChange.bind(this, 'opened')
		});
		this.isShown = true;
	}

	hide({requestingElement, time = this.HIDE_DURATION}) {
		/* Replacing the overlay at root of the DOM for the 
		zIndex to take effect. */
		this._replaceInBody();

		/* Removing the requesting element from the _requesters
		array. */
		this._removeRequester(requestingElement);
		
		/* If there are still requesters, we set the zIndex of the overlay
		element according to the last requestingElement in the Array and 
		stop here waiting for other requestingElements to request the 
		overlay to hide until no requester's left in the Array. */
		if (this._requesters.length) {
			this._setZIndex(this._requesters[this._requesters.length - 1]);
			return;
		}

		this._emitChange('closing');

		if (this._scrollBlocked)
			this._unBlockScroll();

		TweenLite.to(this._host, time, {
			alpha: 0,
			ease: Power2.easeInOut,
			onComplete: () => {
				this._unDisplay();
				this._resetZIndex();
				this._emitChange('closed');
			}
		});
		this.isShown = false;
	}

	setDuration(openDuration = 0.5, closeDuration = 0.5) {
		this.SHOW_DURATION = openDuration;
		this.HIDE_DURATION = closeDuration;
	}


	// PRIVATE METHODS //
	_hideOnLoad() {
		this._unDisplay();
	}

	_emitChange(evt) {
		this.overlayChange$.next(evt);
	}

	_blockScroll() {
		Tools.blockScroll();
		this._scrollBlocked = true;
	}

	_unBlockScroll() {
		Tools.unBlockScroll();
		this._scrollBlocked = false;
	}

	_display() {
		TweenLite.set(this._host, {display: ''});
	}

	_unDisplay() {
		TweenLite.set(this._host, {display: 'none'});
	}

	_setZIndex(requestingElement) {
		let index = 0;
		if (requestingElement && requestingElement instanceof HTMLElement) {
			index = parseInt(getComputedStyle(requestingElement).getPropertyValue('z-index'));
		}
		this._host.style.zIndex = index - 1;
	}

	_resetZIndex() {
		this._host.style.zIndex = '';
	}

	_addRequester(requestingElement) {
		const requesterIndex = this._requesters.indexOf(requestingElement);

		if (requesterIndex === -1)
			this._requesters.push(requestingElement);
	}

	_removeRequester(requestingElement) {
		const requesterIndex = this._requesters.indexOf(requestingElement);

		if (requesterIndex !== -1)
			this._requesters.splice(requesterIndex, 1);
	}

	_injectInParent(parent) {
		if (this._host.parentNode !== parent && parent instanceof HTMLElement)
			parent.appendChild(this._host);
	}

	_replaceInBody() {
		if (this._host.parentNode !== document.body)
			document.body.appendChild(this._host);
	}
}

export const overlayProvider = new OverlayService();