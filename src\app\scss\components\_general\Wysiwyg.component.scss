.Wysiwyg {
    ul {
        margin: 20px 0 20px;
        li {
            @include checklistItem;
            &.disabled {
                //color: #c7c7c7;
                background: url('../../../img/Icon/ic_not-valid-red.svg') left top no-repeat;
                background-size: 16px 16px;
                a {
                    color: #c7c7c7;
                    cursor: text;
                    &:hover {
                        cursor: text;
                        color: #c7c7c7;
                    }
                }
            }
        }
        &.pointer {
            li {
                background: url('../../../img/Icon/ic_pin-grey.svg') left top no-repeat;
                background-size: 22px 22px;
            }
        }
    }
    h5.description {
        margin-top: 60px;
    }

    img {
        max-width: 100%;
        height: auto;
    }
    a {
        text-decoration: underline;
    }
    ol {
        //margin: 20px 0 20px;
        //list-style-position: inside;
    }
}


.single-post {
   .Wysiwyg {
        p {
            a {
                &:hover {
                    text-decoration: underline;
                }
            }
        }
        .container-video {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
            iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }
        
   } 
}

.footerCity {
    margin: 0;
	li {
		display: inline;
		&:after {
			content:"|";
			display: inline-block;
			margin: 0 10px;
		}
		&:last-child {
			&:after {
				display: none;
			}
		}
	}
}

.nocheck {
    li {
        background: none!important;
        padding-left: 0!important;
        border-bottom: 1px solid #CCC!important;
        padding-bottom: 20px!important;
        padding-top: 20px!important;
        img {
            display: block!important;
            
            margin-bottom: 20px!important;
        }
        
    }
}