/* global jQuery */
import { Directive } from '@core/directive';

export class RemoveClassOnClick extends Directive {
	static selector = '[remove-class-on-click]';

	_$remoteElement = jQuery(this.attrs.dataRemoteElementSelector);

	constructor(host) {
		super(host, [
			{name: 'remove-class-on-click', as: 'className', required: true},
			{name: 'data-remote-element-selector', required: true}
		]);

		this._init();
	}

	_init() {
		this._bindEvents();
	}

	_bindEvents() {
		this.$host.on('click', this._onHostClick.bind(this));
	}

	_onHostClick() {
		this._$remoteElement.removeClass(this.attrs.className);
	}
}