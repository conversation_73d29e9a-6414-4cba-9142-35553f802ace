/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../../js/services/TVOrder.service';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { ChannelsService } from '../../js/services/channels.service';
import { PriceCalcService } from '../../js/services';
import { CoreTools } from '@core/helpers';
import { OriginRatioChecker } from '@eboxtv/js/services/OriginRatioChecker.service';
import { EBTVSelectedOptions, TVOptionsService } from '../services/TVOptions.service';

export class FinalOrderButtonDirective extends Directive {
	public static selector: string = '[final-order-button]';

	public static translateChannelType(keyName: string) {
		const translations: any = {
			'base': CoreTools.translate('Chaînes de base', 'Basic channels'),
			'a-la-carte': CoreTools.translate('Ensemble de chaînes', 'À la carte channels'),
			'others': CoreTools.translate('Chaînes à l\'unité', 'Single channels'),
			'premium': CoreTools.translate('Chaînes premium', 'Premium channels')
		};
		return translations[keyName];
	}

	public attrs: {
		formURL: string;
	};

	//#region Private properties
	private $tvOrder: TVOrderService = TVOrderService.getInstance();

	private $tvOptions: TVOptionsService = TVOptionsService.getInstance();

	private $channels: ChannelsService = ChannelsService.getInstance();

	private $priceCalc: PriceCalcService = PriceCalcService.getInstance();

	private $originChecker: OriginRatioChecker = OriginRatioChecker.getInstance();

	private $ratioAlertModal: JQuery = jQuery('#ratioAlertModal');

	private $ratioMessageContainer: JQuery = jQuery('#ratioAlertMessage', this.$ratioAlertModal);
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLElement) {
		super(host, [
			{name: 'final-order-href', as: 'formURL', required: true}
		]);
		this.onInit();
	}
	//#endregion

	//#region Private methodes
	private onInit(): void {
		this.bindEvents();
	}

	private bindEvents(): void {
		this.$host.on('click', this.onHostClick.bind(this));
	}

	private onHostClick(evt: Event): void {
		evt.preventDefault();
		
		const ratioCheck = this.$originChecker.check(this.$tvOrder.getAllSelectedChannels(true, true) as any[]);
		
		if (ratioCheck.isValid) {
			const commandeObject: Commande = this.buildCommandeObject();
			const today: Date = new Date();
			const tomorrow: Date = new Date(today.setDate(today.getDate() + 1));

			CookiesService.setCookies({
				name: 'finalTVOrder',
				value: commandeObject,
				expires: tomorrow
			});

			location.href = this.attrs.formURL;
		} else {
			this.onRatioError();
		}
	}

	private buildCommandeObject(): Commande {
		const forfait: any = CookiesService.getCookies('ebtv__tvPackage', true);
		const output: Commande = {
			forfaitID: forfait.ID,
			chaine_incluses: this.$tvOrder.getSelectedChannels('base', true) as number[],
			bundles: this.buildBundleObjects(),
			groups: this.buildChannelObject(),
			total: this.$priceCalc.totals.total,
			decoderCost: 'wpLocalizedVars' in window ? (window as any).wpLocalizedVars.eboxtvOptions.receiver.cost : 0,
			options: this.$tvOptions.getSelectedOptions()
		};

		return output;
	}

	private buildBundleObjects(): Bundle[] {
		const selectedBundles: number[] = Array.from(this.$tvOrder.selectedBundles);

		return selectedBundles.map(bundleID => {
			const { name, cost, channels, isFeatured } = this.$channels.getBundleById(bundleID);

			return {
				id: bundleID,
				name,
				cost,
				channelsIDs: channels,
				isFeatured
			};
		});
	}

	private buildChannelObject(): ChannelGroup[] {
		const selectedChannels: any = {...this.$tvOrder.selectedChannels};
		const totals = this.$priceCalc.totals;
		const output: ChannelGroup[] = [];

		for (const channelType in selectedChannels) {
			const channels: number[] = Array.from(selectedChannels[channelType])
				.filter(channelID => !this.$tvOrder.isInBundle(channelID as number)) as number[];

			if (channelType === 'base' || channels.length === 0) continue;

			output.push({
				channelsIDs: channels,
				name: FinalOrderButtonDirective.translateChannelType(channelType),
				total: totals.channels[channelType as keyof TVOrderService['selectedChannels']],
				qty: channels.length,
				type: channelType
			});
		}

		return output;
	}

	private mapChannels(channelType: string, channelID: number|any): MappedChannels {
		if (typeof channelID === 'object') channelID = channelID.ID;

		const currentChannel = this.$tvOrder.getChannelById(channelID);
		const returnObject: MappedChannels = {
			name: currentChannel.post_title,
			type: FinalOrderButtonDirective.translateChannelType(channelType),
		};

		if (currentChannel.children) {
			returnObject.children = (Object as any).values(currentChannel.children)
				.map(this.mapChannels.bind(this, channelType));
		}

		if (channelType === 'a-la-carte' || channelType === 'others') {
			returnObject.in_bundle = channelType === 'a-la-carte';
			returnObject.extra_cost = channelType === 'a-la-carte' && currentChannel.extraCost ? currentChannel.extraCost : 0;
		}

		return returnObject;
	}

	private onRatioError(): void {
		let alertMessage: string = this.$originChecker.getErrorMessage();

		if (this.$ratioAlertModal.length && this.$ratioMessageContainer.length) {
			this.$ratioMessageContainer.html(alertMessage);
			(this.$ratioAlertModal as any).modal({show: true});
		} else {
			alert(alertMessage);
		}
	}
	//#endregion
}

export interface Commande {
	forfaitID: number;
	chaine_incluses: number[]; // IDs des chaînes de base
	bundles: Bundle[];
	groups: ChannelGroup[];
	total: number;
	decoderCost: number;
	options: EBTVSelectedOptions;
}

export interface Bundle {
	id: number;
	channelsIDs: number[];
	name: string;
	cost: number;
	isFeatured: boolean;
}

export interface ChannelGroup {
	name: string;
	type: string;
	channelsIDs: number[];
	total: number;
	qty: number;
}

export interface MappedChannels {
	name: string;
	type: string;
	children?: number[];
	in_bundle?: boolean;
	extra_cost?: number;
}