.ListingForfaits {
    &__listing {
        //padding-top: 100px;
        padding-bottom: 100px;
        @media (max-width: 991px)  {
            //padding-top: 70px;
            padding-bottom: 70px;
        }
        @media (max-width: 767px)  {
            //padding-top: 40px;
            padding-bottom: 40px;
        }
    }
    h1 {
        margin: 0 0 60px;
        @media (max-width: 991px)  {
            margin: 0 0 40px;
        }
        @media (max-width: 767px)  {
            margin: 0 0 30px;
        }
    }

    &__listing-container {
        display: flex;
        flex-wrap: wrap;
    }

    &__PostCardItem {
        width: 31.33%;
        margin: 0 3% 100px 0;
        float: left;
        span.block {
            display: block
        }
        ul.listing {
            li {
                margin: 0 0 20px;
                padding: 0;
                background: none;
            }
        }
        @media (max-width: 991px)  {
            width: 47%;
            margin: 0 1.5% 50px;
            &:nth-child(odd) {
                margin-right: 0;
            }
            &:nth-child(even) {
                margin-left: 0;
            }
        }
        @media (max-width: 767px)  {
            margin: 0 1.5% 30px;
        }
        @media (max-width: 600px)  {
            width: 100%;
            margin: 0 0 30px;
        }
    }
    .PostCardItem__forfaitwrapper {
        @media (max-width: 767px)  {
            margin-bottom: 0;
        }
        @media (max-width: 600px)  {
            margin-bottom: 0;
            min-height: 0;
        }
    }
    .PostCardItem__top {
        p {
            margin: 40px 0 0 0;
            font-size: 35px;
            line-height: 40px;
            .is-doodled--underlined {
                &:after {
                    margin-top: -2px;
                }
            }
            @media (max-width: 767px)  {
                font-size: 30px;
                line-height: 35px;
            }
        }
        @media (max-width: 600px)  {
            height: 180px;
        }
    }
    .PostCardItem__bottom {
        min-height: 443px;
        padding: 23px;
        img {
            max-width: 100%;
            @media (max-width: 600px)  {
                margin: 0 auto;
                display: block;
            }
        }
        @media (max-width: 991px)  {
            min-height: 420px;
            padding: 18px
        }
        @media (max-width: 600px)  {
            min-height: 0;
        }
    }
    .RedCircle {
        &__price {
            border-top: none;
            border-bottom: 1px solid $c-white;
            margin: 0 0 5px;
            padding: 0 0 5px;
            white-space: nowrap;
        }
        &__taxo {
            font:12px/18px $f-primary;
            color: $c-white
        }
    }
    .Divider {
        display:inline-block;
    }
    .ThreeCalltos {
        background: none;
    }
}

.PostCardItem {
    &__nomarginright {
        margin: 0 0 100px;
        @media (max-width: 991px)  {
            margin: 0 1% 50px;
        }
    }
}

#choice_6_34_0,
#choice_6_33_0 {
    margin-top: 0!important;
}