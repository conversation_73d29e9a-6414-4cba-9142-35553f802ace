/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { PromotionsService } from '../services/promotions.service';
import { Tools } from '@common/ts/services/Tools.service';
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";
import { CookiesService } from '@common/ts/services/Cookies.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { min } from 'rxjs/operator/min';
export class CommandeInternetDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance()
    $tvOrderService = TVOrderService.getInstance();
    $promotionsService = PromotionsService.getInstance();
    //SELECTEUR DE LA DIRECTIVE
	static selector = '[commandeInternet]';

	$form;
	$valid = true;
    $options = {};
    $modemRent = document.getElementById('modemRent');
    $modemBuy = document.getElementById('buyModem');
    $selectedModem = '';
    $selectedModemRent = ''
    $selectedRouter = '';
    $selections = {

    };

    equipements  = [];

    modem = {};
    router = {};

    modemRentPrice = 0;
    modemBuyPrice = 0;


    messageOpenModem = document.getElementById('openModem');
    messageOpenModemTop = document.getElementById('openModemTop');


    routerRentPrice = document.forms['internetForm'].elements['routerRent'].value; //PRIX DE LOCATION DU ROUTEUR
    routerBuyPrice = document.forms['internetForm'].elements['routerBuy'].value; //PRIX D'ACHAT DU ROUTEUR
    routerOpenDiscount = document.forms['internetForm'].elements['routerOpenBoxDiscount'].value; //RABAIS SUR LES BOITES OUVERTE

    routerSku = document.forms['internetForm'].elements['routerSkuBuy'].value; // SKU D'ACHAT DU ROUTEUR
    routerSkuRent = document.forms['internetForm'].elements['sku_code_location_routeur'].value; // SKU DE LOCATION DU ROUTEUR
    routerSIT = document.forms['internetForm'].elements['routerSIT'].value; // SIT DU ROUTEUR

    routerOpen = document.forms['internetForm'].elements['routerOpenBox'].value; // EST CE QUE LA BOITE DU ROUTEUR EST UNE BOITE OUVERTE


    listModem = document.getElementById('modemList');
    listRouter = document.getElementById('routerList');


    sitModem = '';
    sitDiscountModem = '';
    modemOpenBox = '';

    sectionModem = document.getElementById('modemList');
    sectionRouter = document.getElementById('routerList');
    sectionForfait = document.getElementById('forfaitList');
    region = document.getElementsByClassName('commande-internet')[0].getAttribute('data-region');


    valid = true;

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        this._$OrderService.initInternetPlan();
        if(this.region == 'ontario'){
            CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});
        }

        // Setter une variable essentiel au channel
        var ebtvPackage = CookiesService.getCookies('ebtv__tvPackage');
        if(ebtvPackage == null){
            CookiesService.setCookies({
                name: 'ebtv__tvPackage',
                value: '%7B%22ID%22:7073,%22type%22:%22payant%22,%22name%22:%22EBOX%20TV%22%7D'
            });
        }

        // Setter le LocalStorage eboxTvOption à vide si la région est québec et que eboxTvOption est vide.
        if(this.region == 'quebec'){
            var eboxTvOptions = LocalStorage.get('eboxTVOptions');
            if(eboxTvOptions == null){
                LocalStorage.set('eboxTVOptions', {});
            }
        }

        this.$form = document.forms['internetForm'];
        this.$selections = this._$OrderService._retInternetOrder();
        this.equipements = this._$OrderService._retInternetEquipement();
        if(this.$selections.technology == 'DSL'){
            this.listRouter.classList.add('hidden');
        }
        
        if( (this.$selections.cableTech == 'DSL' && this.region == 'quebec') || this.$selections.technology == 'v-cable' ){
            jQuery('.entente-mois-text').hide();
            jQuery('.zero-par-mois').hide();
            jQuery('.asterix').hide();
        }else{
            jQuery('.entente-mois-text').show();
            jQuery('.zero-par-mois').show();
            jQuery('.asterix').show();
        }

        this.initForm();
        document.querySelector('.order-summary__navbutton').addEventListener('click', function(evt){
            evt.preventDefault();
            this.validateForm();
            this._$OrderService.setStepValidation('internet', this.valid);
            if(this.valid){
                window.location.href = evt.currentTarget.href;
            }
            else{
            }
        }.bind(this))

        //bouton mode tab et mb
        document.querySelector('.eboxtv-navbutton').addEventListener('click', function(evt){
            evt.preventDefault();
            this.validateForm();
            this._$OrderService.setStepValidation('internet', this.valid);
            if(this.valid){
                window.location.href = evt.currentTarget.href;
            }
            else{
            }
        }.bind(this))
        this.$form.addEventListener('change', function(evt){
            this.changeForm(evt.target);
            this._$OrderService.initCart();
        }.bind(this))
	}

    changeForm(input){

        this.equipements = [];
        if(input.id.includes('speed')){
            this.sectionForfait.classList.remove('error');
            this.toggleSelectedInternet(input, 'three-blocs__items--selected');
            this.$selections.forfait = input.value;
            this.$selections.technology = this.$form.elements['technology'].value;
            this.$selections.cableTech = input.getAttribute('data-technology');
            this.$selections.planPrice = input.getAttribute('data-price');
            this.$selections.title = input.getAttribute('data-title');
            this.$selections.downloadSpeed = input.getAttribute('data-speed');
            this.$selections.prixADSL = input.getAttribute('data-promoADSL');
        }
        else if(input.id.includes('modem')){
            this.sectionModem.classList.remove('error');
            this.toggleModem(input, 'three-blocs__items--selected');
            this.$selections.modem = input.value;
            this.modem = {
                type : 'modem',
                service : 'internet',
                qty : 1,
                code : this.$selectedModem,
                codeRent : this.$selectedModemRent,
                buyRent : this.$selections.modem,
                buyPrice : this.modemBuyPrice,
                rentPrice :this.modemRentPrice,
                openBox : this.modemOpenBox,
                sit: this.sitModem,
                rebate : this.sitDiscountModem
            }
        }
        else if(input.id.includes('router')){
            if(this.$selections.cableTech != 'DSL' && this.$selections.cableTech != 'DSL' ){
                this.sectionRouter.classList.remove('error');
                this.toggleModem(input, 'three-blocs__items--selected')
                this.$selections.router = input.value;
                this.router = {
                    type : 'router',
                    service : 'internet',
                    qty : 1,
                    code : this.routerSku,
                    codeRent : this.routerSkuRent,
                    buyRent : this.$selections.router,
                    buyPrice : this.routerBuyPrice,
                    rentPrice :this.routerRentPrice,
                    openBox : this.routerOpen,
                    sit: this.routerSIT,
                    rebate : this.routerOpenDiscount
                }
            }
            else{
                this.router = {}
                
            }


        }

        if( (this.$selections.cableTech == 'DSL' && this.region == 'quebec') || this.$selections.technology == 'v-cable' ){
            jQuery('.entente-mois-text').hide();
            jQuery('.zero-par-mois').hide();
            jQuery('.asterix').hide();
        }else{
            jQuery('.entente-mois-text').show();
            jQuery('.zero-par-mois').show();
            jQuery('.asterix').show();
        }

        
        

        if(this.$selections.cableTech == 'DSL' || this.$selections.technology == 'DSL' ){
            document.querySelectorAll('#modemList h4')[0].innerHTML = CoreTools.translate('Sélectionne ton modem-routeur', 'Select your modem-router');
            document.querySelectorAll('input[name="choixRouter"]').forEach(el => {
                el.checked = false;
                const parent = el.parentElement.parentElement;
                parent.classList.remove('three-blocs__items--selected');
                this.listRouter.classList.add('hidden');
            });
        }
        else{
            document.querySelectorAll('#modemList h4')[0].innerHTML = CoreTools.translate('Sélectionne ton modem', 'Select your modem');
            const myArray = Array.from(this.listModem.classList)
            if(!myArray.includes('hidden')){
                if(this.$selections.technology != 'DSL'){
                    this.listRouter.classList.remove('hidden');
                }
                else{
                    this.listRouter.classList.add('hidden');
                }
            }

        }

        Object.keys(this.modem).length !== 0 ? this.equipements[0] = this.modem : '';
        Object.keys(this.router).length !== 0 ? this.equipements[1] = this.router : '';

        this._$OrderService.saveInternetService(this.$selections);
        this._$OrderService.saveEquipementInternet(this.equipements);
        this.$promotionsService.getEquipementInternetPromotion();
    }

    toggleSelectedInternet(input, selectedClass){

        var phoneLocal = LocalStorage.get('phonePlan') !== null ? LocalStorage.get('phonePlan') : '';
        this.listModem.classList.remove('hidden');

        for (let i = 0; i < this.$form.elements[input.name].length; i++) {
            const element = this.$form.elements[input.name][i]
            this.loopInElement(element, selectedClass, phoneLocal, element);
        }

        if(document.querySelectorAll('.commandeChoixInternet input').length == 1){
            const el = document.querySelectorAll('.commandeChoixInternet input')[0]
                el.parentElement.parentElement.parentElement.classList.add('three-blocs__items--selected');
                this.loopInElement(el, selectedClass, phoneLocal, el);

        }


    }


    loopInElement(element, selectedClass, phoneLocal, input){
        const parent = element.parentElement.parentElement.parentElement;
        if(element.checked){
            parent.classList.add(selectedClass);
            this.$selectedModem = element.getAttribute('data-modem');
            this.$selectedModemRent = element.getAttribute('data-rent-modem-sku');

            this.modemRentPrice = element.getAttribute('data-location-modem');

            if(phoneLocal &&Object.keys(phoneLocal).length !== 0 && phoneLocal.type != 'none' && input.getAttribute('data-technology') == 'DSL'){
                element.setAttribute('data-buy-modem', '160.00')
                element.setAttribute('data-sitprice', '16.00')
            }
            this.modemBuyPrice = element.getAttribute('data-buy-modem');

            var finalPrice = this.modemBuyPrice;
            if(input.getAttribute('data-open') == 'activer'){
                this.messageOpenModem.classList.remove('hidden');
                this.messageOpenModemTop.classList.remove('hidden');

                
                let rabaisBoite = input.getAttribute('data-sitprice');

                this.sitDiscountModem = rabaisBoite;
                this.modemOpenBox = input.getAttribute('data-open');
                this.sitModem = input.getAttribute('data-sit');

                finalPrice = parseFloat(this.modemBuyPrice) - parseFloat(rabaisBoite);
                finalPrice = Math.round(finalPrice * 100) / 100;
                finalPrice = finalPrice.toFixed(2);
            }
            else{
                this.messageOpenModem.classList.add('hidden');
                this.messageOpenModemTop.classList.add('hidden');
            }

            var ischecked = false;
            document.querySelectorAll('input[name="choixModem"]').forEach(el => {
                if(el.checked == true){
                    ischecked = true;
                }
            });
            if(ischecked){
                if(this.$selections.modem){
                    this.$form.elements['choixModem'].forEach(element => {
                        if(element.value == this.$selections.modem){
                            this.$selections.modem = element.value;
                            this.modem = {
                                type : 'modem',
                                service : 'internet',
                                qty : 1,
                                code : this.$selectedModem,
                                codeRent : this.$selectedModemRent,
                                buyRent : this.$selections.modem,
                                buyPrice : this.modemBuyPrice,
                                rentPrice :this.modemRentPrice,
                                openBox : this.modemOpenBox,
                                sit: this.sitModem,
                                rebate : this.sitDiscountModem
                            }
                            this.equipements[0] = this.modem;
    
                        }
                    });
    
                }
    
                if(input.getAttribute('data-technology') == 'DSL'){
                    this.router = {};
                }
                else {
                    var ischeckedRouteur = false;

                        document.querySelectorAll('input[name="choixRouter"]').forEach(el => {
                            if(el.checked == true){
                                ischeckedRouteur = true;
                            }
                        });
                        if(ischeckedRouteur){
                            this.router = {
                                type : 'router',
                                service : 'internet',
                                qty : 1,
                                code : this.routerSku,
                                codeRent : this.routerSkuRent,
                                buyRent : this.$selections.router,
                                buyPrice : this.routerBuyPrice,
                                rentPrice :this.routerRentPrice,
                                openBox : this.routerOpen,
                                sit: this.routerSIT,
                                rebate : this.routerOpenDiscount
                            }
                        }
                        else{
                            this.router = {};
                        }

                }
            }
            else{
                this.modem = {};
                this.router = {};
            }

            Object.keys(this.modem).length !== 0 ? this.equipements[0] = this.modem : '';
            Object.keys(this.router).length !== 0 ? this.equipements[1] = this.router : '';
            this._$OrderService.saveEquipementInternet(this.equipements);

            this.formatPriceHTML(this.createPriceText(this.modemRentPrice), this.$modemRent);
            this.formatPriceHTML(this.createPriceText(finalPrice), this.$modemBuy);

        }
        else{
            parent.classList.remove(selectedClass);
        }
    }

    toggleModem(input, selectedClass){
        if(this.$selections.technology != 'DSL'){
            if(this.$selections.cableTech && this.$selections.cableTech != 'DSL'){
                this.listRouter.classList.remove('hidden');
            }
            else{
                this.listRouter.classList.add('hidden');

            }
        }

        for (let i = 0; i < this.$form.elements[input.name].length; i++) {
            const element = this.$form.elements[input.name][i]
            const parent = element.parentElement.parentElement;
            if(element.checked){
                parent.classList.add(selectedClass);
            }
            else{
                parent.classList.remove(selectedClass);
            }
        }
    }




    createPriceText(price){
        let prices = ''
        if(typeof(price) != 'number'){
            prices =  price.split('.');
            prices.length == 1 ? prices = price.split(','): ''
        }
        else{
            prices = price;
        }
        return prices;
    }

    formatPriceHTML(price, element){


        let sup = document.createElement('sup');
        let span = document.createElement('SPAN');
        sup.textContent = price[1] ? price[1] : '00';
        sup.textContent = sup.textContent;
        if(element != document.getElementById('buyModem')){
            span.textContent = CoreTools.translate('par mois', 'per month');

        }
        span.classList.add(CoreTools.translate('fr', 'en'));
        element.innerHTML = ''
        if(CoreTools.lang == 'en'){
            element.append('$')
        }
        else{
            sup.textContent = sup.textContent + '$';
        }
        element.append(price[0]);
        element.append(sup);
        element.append(span);

    }


    initForm(){
        //ici
        if(this.$selections.cableTech == 'DSL' || this.$selections.technology == 'DSL') {
            document.querySelectorAll('#modemList h4')[0].innerHTML = CoreTools.translate('Sélectionne ton modem-routeur', 'Select your modem-router');

        }
        if(document.querySelectorAll('.commandeChoixInternet input').length == 1){
            var finalPrice = 0;
            this.modemRentPrice = document.querySelectorAll('.commandeChoixInternet input')[0].getAttribute('data-location-modem');

            this.modemBuyPrice = document.querySelectorAll('.commandeChoixInternet input')[0].getAttribute('data-buy-modem');
            finalPrice = this.modemBuyPrice;
            const inputOne = document.querySelectorAll('.commandeChoixInternet input')[0];
            this.modemOpenBox =inputOne.getAttribute('data-open');
            this.sitModem = inputOne.getAttribute('data-sit');
            this.sitDiscountModem = inputOne.getAttribute('data-sitprice');
            if(inputOne.getAttribute('data-open') == 'activer'){
                this.modemOpenBox =inputOne.getAttribute('data-open');
                this.sitModem = inputOne.getAttribute('data-sit');
                this.sitDiscountModem = inputOne.getAttribute('data-sitprice');
                var phoneLocal = LocalStorage.get('phonePlan') !== null ? LocalStorage.get('phonePlan') : '';
                if(phoneLocal &&Object.keys(phoneLocal).length !== 0 && phoneLocal.type != 'none' && inputOne.getAttribute('data-technology') == 'DSL'){
                    inputOne.setAttribute('data-buy-modem', '160.00')
                    inputOne.setAttribute('data-sitprice', '16.00')
                    if(inputOne.getAttribute('data-technology') == 'DSL'){
                        document.querySelectorAll('#modemList h4')[0].innerHTML = CoreTools.translate('Sélectionne ton modem-routeur', 'Select your modem-router');
                    }
                    this.modemOpenBox =inputOne.getAttribute('data-open');
                    this.sitModem = inputOne.getAttribute('data-sit');
                    this.sitDiscountModem = inputOne.getAttribute('data-sitprice');

                    this.modemBuyPrice = document.querySelectorAll('.commandeChoixInternet input')[0].getAttribute('data-buy-modem');
                    this.modem.rebate = inputOne.getAttribute('data-sitprice');
                    this.modem.sit = inputOne.getAttribute('data-sit');
                    this.modem.openBox = inputOne.getAttribute('data-open');
                    
                    // this._$OrderService.saveEquipementInternet(this.equipements);
                }

                finalPrice = parseFloat(this.modemBuyPrice) - parseFloat(inputOne.getAttribute('data-sitprice'));
                finalPrice = finalPrice + '.00';
            }
            this.formatPriceHTML(this.createPriceText(this.modemRentPrice), this.$modemRent);
            this.formatPriceHTML(this.createPriceText(finalPrice), this.$modemBuy);
            this.$promotionsService.getEquipementInternetPromotion();

        }


        if(this.$selections.forfait && document.getElementById('speed-' + this.$selections.forfait) != null){
            let input = document.getElementById('speed-' + this.$selections.forfait);
            if(document.getElementById('minTv')){
                // Si minTV est actif
                var minVitesse = document.getElementById('minTv').value;
                while(!document.querySelectorAll("[data-speed='" + minVitesse + "']")[0]){
                    minVitesse = parseInt(minVitesse) + 5;
                }
                
                input = document.querySelectorAll("[data-speed='" + minVitesse + "']")[0];
                input.checked = true;
                this.changeForm(input);

                //Trouvé le div parent et scroller vers lui
                input.parentElement.parentElement.parentElement.scrollIntoView({
                    behavior: "smooth",
                    block: 'center',
                    inline: 'center'
                });

            }else{
                // Sinon on get le forfait selectionné
                input = document.getElementById('speed-' + this.$selections.forfait);
                input.checked = true;
            }


            if(document.querySelectorAll('.commandeChoixInternet input').length != 1){
                this.toggleSelectedInternet(input, 'three-blocs__items--selected')
            }
            else{
                const el = document.querySelectorAll('.commandeChoixInternet input')[0]
                if(el.checked){
                    el.parentElement.parentElement.parentElement.classList.add('three-blocs__items--selected');
                }
            }
            this.listModem.classList.remove('hidden');
            if(this.equipements[0]){
                if(this.$selections.cableTech && this.$selections.cableTech != 'DSL'){
                    this.listRouter.classList.remove('hidden');
                }
            }
            else{
                this.listRouter.classList.add('hidden');
            }

            if(this.$selections.modem && this.$selections.modem.trim() !== ''){
                this.$form.elements['choixModem'].value = this.$selections.modem;
                for (let i = 0; i < this.$form.elements['choixModem'].length; i++) {
                    const input = this.$form.elements['choixModem'][i];
                    input.checked ? this.changeForm(input) : '';
                }
            }
            if(this.$selections.router && this.$selections.router.trim() !== ''){
                this.$form.elements['choixRouter'].value = this.$selections.router;
                for (let i = 0; i < this.$form.elements['choixRouter'].length; i++) {
                    const input = this.$form.elements['choixRouter'][i];
                    input.checked ? this.changeForm(input) : '';
                }
            }
        }
        else{
            this.listModem.classList.add('hidden');
            this.listRouter.classList.add('hidden');

        }
    }

    validateForm(){
        if(this.$selections.cableTech && this.$selections.cableTech != 'DSL'){
            if(this.$form.elements['downloadSpeed'].value.trim() !== ''){
                this.sectionForfait.classList.remove('error');
                if(this.$form.elements['choixModem'].value.trim() !== ''){
                    this.listModem.classList.remove('error');
                    if(this.$form.elements['choixRouter'].value.trim() !== ''){
                        this.listRouter.classList.remove('error');
                        this.valid = true;
                    }
                    else{
                        this.valid = false;
                        this.listRouter.classList.add('error');
                        this.scrollTo(this.listRouter);
                    }
                }
                else{
                    this.valid = false;
                    this.listModem.classList.add('error');
                    this.scrollTo(this.listModem);
                }
            }
            else{
                this.valid = false;
                this.sectionForfait.classList.add('error');
                this.scrollTo(this.sectionForfait);
            }
        }
        else{
            if(this.$form.elements['downloadSpeed'].value.trim() !== ''){
                this.sectionForfait.classList.remove('error');
                if(this.$form.elements['choixModem'].value.trim() !== ''){
                    this.listModem.classList.remove('error');
                        this.valid = true;
                }
                else{
                    this.valid = false;
                    this.listModem.classList.add('error');
                    this.scrollTo(this.listModem);
                }
            }
            else{
                this.valid = false;
                this.sectionForfait.classList.add('error');
                this.scrollTo(this.sectionForfait);
            }
        }



    }

    scrollTo(el) {
        el.scrollIntoView({behavior: "smooth"});
    }
}
