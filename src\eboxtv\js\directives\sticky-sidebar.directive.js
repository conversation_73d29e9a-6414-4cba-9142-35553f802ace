import { Directive } from '@core/directive';
import { ViewChangeDetector } from '@common/ts/services//ViewChangeDetector.service';
import StickySidebar from 'sticky-sidebar/dist/sticky-sidebar';

export class StickySidebarDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[sticky-sidebar]';

	$changeDetector = ViewChangeDetector.getInstance();

	_updateInterval;

	/**
	 * @type {StickySidebar}
	 */
	_stickyInstance = new StickySidebar(this.host, this.attrs.options);

	/**
	 * @type {Subscription}
	 */
	_viewChangeSub;


	// INIT //
	constructor(host) {
		super(host, [
			{name: 'sticky-options', as: 'options', type: 'eval', default: {}}
		]);
		this._init();
	}
	_init() {
		this._bindEvents();
		this._startIntervalCheck();
		this._subscribeToViewChange();
		this._subscribeToheightChange();
	}

	_startIntervalCheck() {
		this._updateInterval = setInterval(() => {
			if ('requestIdleCallback' in window) {
				window.requestIdleCallback(this._stickyInstance.updateSticky.bind(this), {timeout: 500});
			} else {
				this._stickyInstance.updateSticky();
			}
		}, 1000);
	}

	_bindEvents() {
		jQuery(window).on('resize', this._onViewChange.bind(this));

	}

	_subscribeToViewChange() {
		this._viewChangeSub = this.$changeDetector.viewChange$.subscribe(
			this._onViewChange.bind(this)
		);
	}

	_subscribeToheightChange() {
		const resizeObserver = new ResizeObserver(
			this._onViewChange.bind(this)
		);
		  resizeObserver.observe(document.body);
	}

	_onViewChange() {

		this._stickyInstance.updateSticky();
	}
}