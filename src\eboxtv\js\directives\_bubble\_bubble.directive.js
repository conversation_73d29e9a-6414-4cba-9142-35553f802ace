/* global jQuery */
import { Directive } from '@core/directive';
import { TweenLite } from 'gsap/TweenLite';
import { Power2, Back } from 'gsap/EasePack';
import { $bubbleServiceProvider } from './_bubble.service';
import { overlayProvider } from '../../services/overlay.service';
import { Tools } from '@common/ts/services/Tools.service';
import { CoreTools } from '@core/helpers/CoreTools';

export class _BubbleDirective extends Directive {
	static selector = '[data-bubble]';

	// READ-ONLY //
	OPEN_DURATION  = 0.25;
	CLOSE_DURATION = 0.1;
	DISTANCE_Y_TRAVEL = -15;
	lastActiveElement;


	// PRIVATE PROPERTIES //
	_$triggerElement;
	_$closerElement  = this.attrs.closerSelector ? jQuery(this.attrs.closerSelector, this.host) : null;
	_wrapperElement  = this.attrs.wrapperSelector ? this.host.querySelector(this.attrs.wrapperSelector) : null;
	_overlaySub;
	_bubbleSub;
	_mouseleaveDebounce;

	/* Services */
	_$bubbleService = $bubbleServiceProvider;
	_$overlay = overlayProvider;

	/* Aliases */
	_isOpenedValue = false;
	id = 'bubble__' + Tools.generateRdmString({length: 16});
	
	
	// GETTERS AND SETTERS //
	get isOpened() {
		return this._isOpenedValue;
	}
	set isOpened(val) {
		if (val !== this._isOpenedValue) {
			this._isOpenedValue = val;
			this._updateStatus(val);
		}
	}

	get mediaMatchForClick() {
		//return window.matchMedia('(max-width: 1024px)').matches;
		return false;
	}
	
	// INIT //
	constructor(host) {
		super(host, [
			{name: 'data-trigger-selector', as: "triggerSelector", required: true},
			{name: 'data-trigger-relation', as: "triggerRelation", default: 'sibling'},
			{name: 'data-has-overlay', as: "hasOverlay", type: 'eval', default: true},
			{name: 'data-is-fixed', as: "isFixed", type: 'eval', default: false},
			{name: 'data-is-tooltip', as: "isTooltip", type: 'eval', default: false},
			{name: 'data-center-on-mobile', as: 'centerOnMobile', type: 'eval', default: false},
			{name: 'data-wrapper-selector', as: 'wrapperSelector'},
			{name: 'data-closer-selector', as: 'closerSelector'}
		]);
		this._init();

		this.$host.attr('data-bubble-id', this.id);
	}
	_init() {
		this._getTriggerElement();
		this._setInitialStyles();
		this._bindEvents();
		this._initSubscriptions();
	}

	onAppDestroy() {
		this._unbindEvents();
		this._unsubscribeAll();
	}


	// PUBLIC METHODS //
	open() {
		this._isOpenedValue = true;

		this._displayHost();
		this._replacePositionFromWindow();
		
		if (this.attrs.hasOverlay && this.mediaMatchForClick) {
			this._$overlay.show({
				time: this.OPEN_DURATION,
				requestingElement: this.host,
				blockScroll: true
			});
		}

		this._hideSummaryTab();

		TweenLite.to(this.host, this.OPEN_DURATION, {
			y: 0,
			alpha: 1,
			ease: Back.easeOut
		});
	}

	close() {
		this._isOpenedValue = false;

		if (this._$overlay.isShown && this.attrs.hasOverlay) {
			this._$overlay.hide({
				time: this.CLOSE_DURATION,
				requestingElement: this.host
			});
		}

		this._showSummaryTab();

		TweenLite.to(this.host, this.CLOSE_DURATION, {
			y: this.DISTANCE_Y_TRAVEL,
			alpha: 0,
			onComplete: this._hideHost.bind(this)
		});
	}


	// PRIVATE METHODS //
	_getTriggerElement() {
		if (this.attrs.triggerRelation === 'sibling')
			this._$triggerElement = jQuery(this.host).siblings(this.attrs.triggerSelector);
		else if (this.attrs.triggerRelation === 'global')
			this._$triggerElement = jQuery(this.attrs.triggerSelector);
		else
			this._$triggerElement = jQuery(this.attrs.triggerSelector, this.$host.parent);
	}

	_setInitialStyles() {
		const styles = {
			x: '-50%',
			y: this.DISTANCE_Y_TRAVEL,
			alpha: 0,
			display: 'none'
		};

		if (this.attrs.isFixed) {
			styles.position = 'fixed';
		}
			
		TweenLite.set(this.host, styles);
	}
	
	_bindEvents() {
		this._$triggerElement.on(`click.${ this.id }`, this._onTriggerClick.bind(this));

		if (this._$closerElement && this._$closerElement.length) {
			this._$closerElement.on(`click.${ this.id }`, this._onCloserClick.bind(this));
		}

		if (this.attrs.isFixed || this.attrs.isTooltip) {
			jQuery(window).on(`scroll.${ this.id }`, this._onWindowScroll.bind(this));
		}

		jQuery(window).on(`resize.${ this.id }`, this._onResize.bind(this));
	}

	_unbindEvents() {
		this._$triggerElement.off(`click.${ this.id }`);
		this._$closerElement.off(`click.${ this.id }`);
		jQuery(window).off(`resize.${ this.id }`);
	}

	_initSubscriptions() {
		this._subscribeToBubbleChange();

		if (this.attrs.hasOverlay)
			this._subscribeToOverlayChange();
	}

	_unsubscribeAll() {
		if (this._overlaySub)
			this._overlaySub.unsubscribe();
		
		if (this._bubbleSub)
			this._bubbleSub.unsubscribe();
	}

	_subscribeToOverlayChange() {
		this._overlaySub = this._$overlay.overlayChange$.subscribe(
			this._onOverlayChange.bind(this)
		);
	}

	_subscribeToBubbleChange() {
		this._bubbleSub = this._$bubbleService.currentBubbleChange$.subscribe(
			this._onBubbleChange.bind(this)
		);
	}

	_onOverlayChange(evt) {
		if (this.mediaMatchForClick && this.attrs.hasOverlay && evt === 'click')
			this._$bubbleService.clearBubbles();
	}

	_onWindowScroll() {
		if (this._$bubbleService.currentBubble === this) {
			this._$bubbleService.clearBubbles();
		}
	}

	_onResize() {
		if (!this.mediaMatchForClick) {
			if (!this.attrs.isFixed) {
				this.host.style.position = '';
				this.host.style.top = '';
				this.host.style.left = '';
				this.host.style.right = '';
				this.host.style.bottom = '';
			}

			if (this.isOpened && this.attrs.hasOverlay && this._$overlay.isShown) {
				this._$overlay.hide({
					time: 0,
					requestingElement: this.host
				});
			}
		}
	}

	_onBubbleChange(currentBubble) {
		this.isOpened = currentBubble === this;
	}

	_onTriggerClick(evt) {
		// if (!this.mediaMatchForClick) return;

		evt.preventDefault();
		evt.stopPropagation();

		this._setCurrentBubble();
	}

	_setCurrentBubble() {
		if (this._$bubbleService.currentBubble === this)
			this._$bubbleService.clearBubbles();
		else
			this._$bubbleService.setCurrentBubble(this);
	}

	/**
	 * This method does 2 things:
	 * * Repositionning the host elements which contains the bubble AND the arrow beneath it;
	 * * Repositionning the actual bubble alone horizontally so that the arrow stays above 
	 * the trigger element to replace the bubble if it overflows from the viewport.
	 */
	_replacePositionFromWindow() {
		const mediaMatchForClick = this.mediaMatchForClick;
		const { isFixed, isTooltip, centerOnMobile } = this.attrs;
		const fixedParent = CoreTools.hasAncestorWithStyle(this.host, 'position', 'fixed');
		const transformedParent = CoreTools.hasAncestorWithStyle(this.host, 'transform', 'none', true);
		const specialParent = fixedParent ? fixedParent : transformedParent;

		if (isTooltip) {
			this.$host.removeClass('Bubble--is-bottom');
			TweenLite.set(this.host, {clearProps: 'top, left, right, bottom'});
			this.host.style.top = '';
			this.host.style.left = '';
			this.host.style.right = '';
			this.host.style.bottom = '';
		}

		if (!isFixed && !this.mediaMatchForClick) {
			this.host.style.position = '';
			this.host.style.top = '';
			this.host.style.left = '';
			this.host.style.right = '';
			this.host.style.bottom = '';
		}

		// Positioning the host element
		if (isFixed && !specialParent) {
			if (mediaMatchForClick && !isTooltip) {
				TweenLite.set(this.host, {
					left: (window.innerWidth / 2),
					bottom: (window.innerHeight / 2) - (this.host.clientHeight / 2)
				});
			} else {
				const triggerElement = this._$triggerElement[0];
				const triggerClientRect = triggerElement.getBoundingClientRect();
				const { left, bottom } = triggerClientRect;

				TweenLite.set(this.host, {
					left: left + (triggerElement.clientWidth / 2),
					bottom: window.innerHeight - bottom
				});
			}
		} else if (isFixed && specialParent) {
			let { left, bottom } = CoreTools.getPositionRelativeToParent(
				this._$triggerElement[0], specialParent
			);

			left += this._$triggerElement.width() * 0.5;

			TweenLite.set(this.host, {left, bottom});
		}

		// Positioning the wrapper element
		if (!centerOnMobile || (centerOnMobile && !mediaMatchForClick)) {
			const wrapperClientRect = this._wrapperElement.getBoundingClientRect();
			const {top, right, bottom, left} = wrapperClientRect;
			const sidePadding = 10;

			if (right > window.innerWidth)
				TweenLite.set(this._wrapperElement, {right: (right - window.innerWidth) + sidePadding});
			else if (left < 0)
				TweenLite.set(this._wrapperElement, {left: (left * -1) + sidePadding});

			if (isTooltip && specialParent) {
				const triggerElement = this._$triggerElement[0];
				const { top: triggerTop, right: triggerRight } = triggerElement.getBoundingClientRect();
				const rightPosition = ((window.innerWidth - triggerRight) - this.host.clientWidth) + (triggerElement.clientWidth / 2);

				if (top < 0) {
					const topPosition = triggerTop + triggerElement.clientHeight + sidePadding;

					TweenLite.set(this.host, { top: topPosition });
					this.$host.addClass('Bubble--is-bottom');
				} else if (fixedParent) {
					TweenLite.set(this.host, { top: triggerTop - (this.host.clientHeight - triggerElement.clientHeight) });
				}
				
				TweenLite.set(this.host, {
					left: 'auto',
					right: rightPosition
				});
			}

		} else if (mediaMatchForClick && (!isFixed || isTooltip)) {
			TweenLite.set(this.host, {
				position: 'fixed',
				left: (window.innerWidth / 2),
				bottom: (window.innerHeight / 2) - (this.host.clientHeight / 2)
			});
		}
	}

	_onCloserClick(evt) {
		evt.preventDefault();
		this._$bubbleService.clearBubbles();
	}

	_toggleIsOpened() {
		this.isOpened = !this.isOpened;
	}

	_updateStatus(val = this._isOpenedValue) {
		if (val)
			this.open();
		else
			this.close();
	}

	_displayHost() {
		TweenLite.set(this.host, {clearProps: 'display'});
	}

	_hideHost() {
		TweenLite.set(this.host, {display: 'none'});

		if (this._wrapperElement)
			TweenLite.set(this._wrapperElement, {clearProps: 'right, left'});
	}

	_hideSummaryTab() {
		if (!this.mediaMatchForClick) return;

		const orderSummaryElement = document.querySelector('.order-summary');

		if (orderSummaryElement)
			orderSummaryElement.classList.add('order-summary--tab-closed');
	}

	_showSummaryTab() {
		if (!this.mediaMatchForClick) return;

		const orderSummaryElement = document.querySelector('.order-summary');

		if (orderSummaryElement)
			orderSummaryElement.classList.remove('order-summary--tab-closed');
	}
}