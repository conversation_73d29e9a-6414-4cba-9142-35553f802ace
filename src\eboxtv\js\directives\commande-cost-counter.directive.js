import { CommandeOrderCounterDirective } from './commande-order-counter.directive';
import { Tools } from '@common/ts/services/Tools.service';
import { CoreTools } from '@core/helpers/CoreTools';
import { OrderOnlineService } from "@eboxtv/js/services/orderOnline.service";
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";

export class CommandeCostCounterDirective extends CommandeOrderCounterDirective {
	static selector = '[commande-cost-counter]';
	$orderOnline = OrderOnlineService.getInstance();
	$tvOrder = TVOrderService.getInstance();

	monthCost = 0;
	uniqueCost = 0;

	constructor(host) {
		super(host, [
			{name: 'include-receiver', type: 'eval', default: false},
			{name: 'include-options', type: 'eval', default: false},
			{name: 'cost-type', default: 'channels'},
			{name: 'raise-decimals', type: 'eval', default: false},
			{name: 'no-commas', type: 'eval', default: false}
		]);
		this.$orderOnline.cartMonthlyPrice.subscribe((price) => {
			const selectedChannels = this.$tvOrder.selectedChannels;
			this.monthCost = price;
			this._onOrderChange(selectedChannels);
		})

		this.$orderOnline.cartUniquePrice.subscribe((price) => {
			const selectedChannels = this.$tvOrder.selectedChannels;
			this.uniqueCost = price
			this._onOrderChange(selectedChannels);
		})
		this.__init();
	}

	__init() {
		// Subscribing to TVOrderService.selectedChannelsChange$.
		this._suscribeToTotalChange();

		// Rendering initial value.
		this._render();
	}

	/**
	 * @override
	 */
	_suscribeToTotalChange() {
		this._orderChangeSub = this._$priceCalc.totalsChange$.subscribe(
			this._onTotalChange.bind(this)
		);
	}

	_onOrderChange(selectedChannels) {
		const {'a-la-carte': cardChannels, others, premium, base} = selectedChannels;

		this._render(base.size + cardChannels.size + others.size + this._$TVOrder.bundledChannelIDs.length, premium.size);
	}

	/**
	 * @override
	 */
	_onTotalChange(totalObject) {
		if (this.attrs.costType === 'total') {
			this._count = totalObject.total;
		} else {
			if (this.attrs.costType.match(/\./)) {
				this._count = CoreTools.readObjectFromString(this.attrs.costType, totalObject);
			} else {
				this._count = totalObject[this.attrs.costType][this.attrs.orderType];
			}
		}
	}

	_getTotalObjectValue(totalObject, selector) {
		
	}


	/**
	 * 
	 * @param {any[]} channels 
	 */
	_calcFullCost(channels = []) {
		let cost = 0;

		channels.forEach(currentSet => {
			cost += this._calcSetCost(currentSet);
		});
		return cost;
	}


	/**
	 * 
	 * @param {Set<any>} group 
	 */
	_calcSetCost(set = new Set()) {
		let cost = 0;

		set.forEach(currentID => {
			const currentChannel = this._$TVOrder.getChannelById(currentID);
			if (currentChannel && currentChannel.post_parent == 0 && 'cost' in currentChannel)
				cost += currentChannel.cost;
		});
		return cost;
	}

	/**
	 * @override
	 */
	_render() {
		const { raiseDecimals, noCommas, includeReceiver, includeOptions } = this.attrs;

		const lang = Tools.lang;
		let   cost = this._count;
		if (includeReceiver) {
			cost += PriceCalcService.RECEIVER_COST;
		}

		if (includeOptions && this._$priceCalc.totals.options) {
			cost += Object.values(this._$priceCalc.totals.options).reduce((acc, opt) => acc + opt, 0);
		}


		cost += this.monthCost;
		cost += this.uniqueCost;
		const formattedCost = CommandeCostCounterDirective._formatPrice(cost, lang, raiseDecimals, noCommas);
		if (lang === 'fr')
			this.host.innerHTML = formattedCost;
		else
			this.host.innerHTML =  '$' + formattedCost;
	}

	static _formatPrice(price = 0, lang = 'fr', raiseDecimals = false, noCommas = false) {
		const cost = parseFloat(price).toFixed(2);
		const costInt = Math.floor(cost);
		let   costFloats = Tools.zeroPad((cost - costInt) * 100);

		if (raiseDecimals)
			costFloats = `<sup>${noCommas ? '' : (lang === 'fr' ? ',' : '.')}${ costFloats }${lang === 'fr' ? '$' : ''}</sup>`;
		else
			costFloats = `${noCommas ? '' : (lang === 'fr' ? ',' : '.')}${ costFloats }${lang === 'fr' ? '$' : ''}`;

		return costInt + costFloats;
	}
}