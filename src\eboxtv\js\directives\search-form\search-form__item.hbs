<div class="channel{{#if (not canModify) }} channel--disabled{{/if}}{{#if (eqBool channel.status false)}} channel--unavailable{{/if}} channel--{{channel.channel_type}}"
     channel-select
	 channel-type="{{ channel.channel_type }}"
	 channel-selected-class="channel--selected"
	 change-value-of=".channel__add-checkbox"
	 channel-origin="{{ channel.origin }}"
	 true-title="{{ removeChannel }} {{ channel.post_title }}"
	 false-title="{{ addChannel }} {{ channel.post_title }}"
	 channel-id="{{{getParentID channel}}}"
	 is-dummy="{{#if canModify }}false{{else}}true{{/if}}"
	 click-disabled="{{#if (eqBool channel.status false)}}true{{else}}false{{/if}}"
	>

	{{#if (eqBool channel.status false) }}
	<div class="channel__banner">
		<div class="channel__banner-wrapper">{{translate 'À venir' 'Coming soon'}}</div>
	</div>
	{{else}}
		{{#if highlightPremiums }}
			{{#if (eqBool channel.channel_type 'premium') }}
			<div class="channel__banner channel__banner--premium">
				<div class="channel__banner-wrapper">Premium</div>
			</div>
			{{/if}}
		{{/if}}
	{{/if}}

	<div class="channel__logo-container" data-accessify>
		<div role="button" tabindex="0" class="channel__logo-wrapper">
			{{#if (eqBool channel.channel_type 'premium') }}
			<img src="{{ channel.logo }}" alt="Premium - {{ channel.post_title }}" class="channel__logo">
			{{else}}
			<img src="{{ channel.logo }}" alt="{{ channel.post_title }}" class="channel__logo">
			{{/if}}

			<div class="channel__add-overlay">
				<svg class="channel__action-icon channel__action-icon--add">
					<use xlink:href="#icon-tv-add"></use>
				</svg>

				<svg class="channel__action-icon channel__action-icon--check">
					<use xlink:href="#icon-tv-check"></use>
				</svg>
				<p class="channel__add-title">
					{{translate 'Ajouter' 'Add' }}
				</p>
				<input type="checkbox" class="channel__add-checkbox"/>
			</div>

		</div>

		<div class="channel__price-container">
			<h6 class="channel__price">{{ channel.cost }}$</h6>
		</div>
	</div>

	{{#if (eqBool channel.channel_type 'premium') }}
	<div class="channel__details-btn" role="button" aria-label="Details - Premium - {{ channel.post_title }}" tabindex="0">
	{{else}}	
	<div class="channel__details-btn" role="button" aria-label="Details - {{channel.post_title }}" tabindex="0">
	{{/if}}
		<svg class="channel__details-btn-icon">
			<use xlink:href="#icon-tv-info--full"></use>
		</svg>
		{{translate 'Détails' 'Details' }}
	</div>

	<div class="channel__details Bubble"
		data-bubble
		data-trigger-relation="sibling"
		data-trigger-selector=".channel__details-btn"
		data-wrapper-selector=".Bubble__bubble"
		data-closer-selector=".Bubble__close-btn"
		data-is-fixed="true"
		tabindex="-1">

		<div class="Bubble__bubble">
			<div class="Bubble__wrapper">
				<div class="Bubble__corner-container">
					<img src="{{ locationOrigin }}/wp-content/themes/ebox2018/img/origin-flags/origin-flag__{{#if (eq channel.origin 'other') }}EU{{else toUpperCase channel.origin }}{{/if}}.png"
						 alt=""
						 class="Bubble__origin-flag" />

					<button type="button" class="Bubble__close-btn">
						<svg class="Bubble__close-icon">
							<use xlink:href="#icon-close"></use>
						</svg>
					</button>
				</div>

				<h6 class="Bubble__title">
					{{ channel.post_title }}
					<span class="Bubble__position">{{ channel.position }}</span>
				</h6>

				{{#if channel.cost}}
				<div class="Bubble__pricing-container">
					<p class="Bubble__pricing">{{{convertedPrice channel.cost}}} {{ byUnit }}</p>
					{{#if channel.extraCost }}
					<p class="Bubble__pricing">+ {{{convertedPrice channel.extraCost}}} {{ addToCost }}</p>
					{{/if}}
				</div>
				{{/if}}

				<div class="Bubble__content">
					<h6 class="Bubble__content-title">{{translate 'Description : ' 'Description : ' }}</h6>
					
					<div class="Bubble__content-container">{{{translate channel.content channel.content_EN}}}</div>
				</div>

				{{#if channel.hasFeatures }}
				<div class="Bubble__features">
					<h6 class="Bubble__features-title">{{translate 'Fonction(s) : ' 'Function(s) : ' }}</h6>

					<ul class="Bubble__features-list">
						
						{{#if (ifCond channel.has_replay '!==' false)}}
						<li class="Bubble__feature-item">
							<svg class="Bubble__feature-icon">
								<use xlink:href="#icon-ic_replay"></use>
							</svg>
							<span class="Bubble__feature-title">{{translate 'Rejouer' 'Replay' }}</span>
						</li>
						{{/if}}

						{{#if (ifCond channel.has_rattrapage '!==' false)}}
						<li class="Bubble__feature-item">
							<svg class="Bubble__feature-icon">
								<use xlink:href="#icon-ic_catch-up"></use>
							</svg>
							<span class="Bubble__feature-title">{{translate 'Rattrapage' 'Catch-up' }}</span>
						</li>
						{{/if}}

						{{#if (ifCond channel.has_go '!==' false)}}
						<li class="Bubble__feature-item">
							<svg class="Bubble__feature-icon">
								<use xlink:href="#icon-ic-application"></use>
							</svg>
							<span class="Bubble__feature-title">{{translate 'Application de la chaîne' 'Channel App' }}</span>
						</li>
						{{/if}}

						{{#if (ifCond channel.has_vod '!==' false)}}
						<li class="Bubble__feature-item">
							<svg class="Bubble__feature-icon">
								<use xlink:href="#icon-ic_video"></use>
							</svg>
							<span class="Bubble__feature-title">{{translate 'Vidéo sur demande' 'Video on demand' }}</span>
						</li>
						{{/if}}
					</ul>
				</div>
				{{/if}}

				{{#if (ifCond canModify '&&' (ifCond (ifCond channel.status '!==' false) '&&' (ifCond channel.status '!=' 0))) }}
				<button type="button" class="Bubble__add-btn">
					<div class="Bubble__add-container Bubble__add-container--add">
						<svg class="Bubble__add-icon">
							<use xlink:href="#icon-tv-add"></use>
						</svg>
						<span class="Bubble__add-btn-title">{{translate 'Ajouter' 'Add' }}</span>
					</div>

					<div class="Bubble__add-container Bubble__add-container--remove">
						<svg class="Bubble__add-icon">
							<use xlink:href="#icon-tv-check"></use>
						</svg>
						<span class="Bubble__add-btn-title">{{translate 'Ajouté' 'Added'}}</span>
					</div>
				</button>
				{{/if}}
			</div>
		</div>
	</div>
</div>