// Hollow black and white buttons
.eboxtv-button {
	display: inline-block;
	color: $c-white;
	border: 2px solid $c-white;
	background: $c-grey;
	appearance: none;
	border-radius: 6px;
	text-transform: uppercase;
	font: 700 16px/22px $ebtv-f-primary;
	text-align: center;
	padding: 12px 10px;
	transition: all 0.1s linear;
	cursor: pointer;

	@media (min-width: #{map-get($ebtvBreakpoints, tabletLandscape) + 1}) {
		&:hover, &:focus {
			color: $c-grey;
			background: $c-white;
		}
	}
}


// Red buttons with arrow
.eboxtv-navbutton {
	position: relative;
	display: flex;
	color: $c-white;
	height: 60px;
	border-radius: 6px;
	cursor: pointer;
	border: none;
	appearance: none;
	transform: translateY(0);
	transition: all 0.2s $cubic;

	&__title {
		font: 700 16px/22px $ebtv-f-primary;
		text-transform: uppercase;
		padding: 0 30px 0 0;
	}

	&__icon-container, &__title {
		height: 100%;
		display: inline-flex;
		vertical-align: middle;
		align-items: center;
	}

	&__icon-container {
		padding: 0 0 0 15px;
		border-left: 1px solid $c-primary-opacity;
		transition: border 0.2s $cubic;
	}

	&__icon {
		display: inline-block;
		width: 20px;
		height: 20px;
		fill: currentColor;
	}

	&:hover, &:focus {
		transform: translateY(-7px);

		.eboxtv-navbutton__icon-container {
			border-color: #FFF;
		}
	}

	&--float-right {
		float: right;
	}
}



