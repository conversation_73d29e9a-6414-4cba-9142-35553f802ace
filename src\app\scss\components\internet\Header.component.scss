.InternetHeader {
    box-sizing: border-box;
    min-height: 280px;
    background: $c-black;
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 100px;
    &__title {
        padding: 8px 0 0 20px;
        font: 900 50px/55px $f-primary;
        color: $c-white;
        text-transform: uppercase;
        position: relative;
        &::before {
            display: none;
        }
        @media (max-width:1024px) {
            font: 900 40px/45px $f-primary;
            //min-height: 210px;
            margin-bottom: 40px;
        }
        @media (max-width:767px) {
            //min-height: 90px;
            margin-bottom: 40px;
            font: 900 22px/25px $f-primary;
        }
    }
    &__explications {
        margin-top: 10px;
        padding-left: 20px;
        color: $c-white;
        font: 400 12px/16px $f-primary;
        max-width: 50%;
        @media (max-width: 1024px) {
            max-width: 60%;
            margin-top: 0;
        }
        @media (max-width:767px) {
            //display: none;
        }
    }
    &__laptop {
        display: none;
        width: 507px;
        height: 291px;
        position: absolute;
        // background: url('../../../img/header-internet-laptop.png') center center no-repeat;
        background: url('../../../img/header-internet-laptop.png') center center no-repeat;
        background-size: 96%;
        position: absolute;
        right: 0;
        top: 0;
        &--telephonie {
            width: 181px;
            height: 278px;
            position: absolute;
            top: 70px;
            right: -40px;
            background: url('../../../img/header-internet-telephonie.png') center center no-repeat;
            p {
                width: 130px;
                right: -77px!important;
            }
            @media (max-width: 1999px) {
                top: -20px;
                right: 80px;
            }
            @media (max-width: 1199px) {
                top: 0;
                right: 100px;
            }
            @media (max-width: 1024px) {
                display: none!important;
            }
        }
        &--promotions {
            width: 439px;
            height: 293px;
            position: absolute;
            right: -40px;
            top: 20px;
            background: url('../../../img/home-forfait-device2x.png') center center no-repeat;
            background-size: 100%;
            p {
                &:before {
                    display: none!important;
                }
            }
            @media (max-width: 1024px) {
                width: 200px!important;
                height: 133px!important;
                top: 90px!important;
                right: -80px!important;
            }
        }
        @media (max-width: 1024px) {
            width: 350px;
            height: 196px;
            top: 0px;
        }
        @media (max-width:767px) {
            display: none;
        }
        &--forfaits {
            width: 439px;
            height: 293px;
            position: absolute;
            right: -40px;
            top: 20px;
            background: url('~@app/img/home-forfait-device.webp') center center no-repeat;
            background-size: 100%;
            &.forfaits-en {
                background: url('~@app/img/home-forfait-device.webp') center center no-repeat;
                background-size: 100%;
                @media (max-width: 1024px) {
                    width: 350px;
                    height: 196px;
                    top: 0px;
                }
                @media (max-width:767px) {
                    display: none;
                }
            }
            p {
                &:before {
                    display: none!important;
                }
            }
            @media (max-width: 1024px) {
                width: 200px!important;
                height: 133px!important;
                top: 90px!important;
                right: -80px!important;
            }
        }
        @media (max-width: 1024px) {
            width: 350px;
            height: 196px;
            top: 0px;
        }
        @media (max-width:767px) {
            display: none;
        }
        p {
            font: 300 16px/20px $f-secondary;
            color: $c-medium-grey;
            max-width: 205px;
            text-align: center;
            position: absolute;
            bottom: -50px;
            right: 80px;
            @media (max-width:991px) {
                font: 300 12px/20px $f-secondary;
                right: 10px;
            }

            &:before {
                width: 40px;
                height: 40px;
                background: url("../../../img/Icon/drawing-arrow02.svg") center center no-repeat;
                background-size: 70%;
                content: "";
                position: absolute;
                display: inline-block;
                left:-40px;
                top: -20px;
                @media (max-width:991px) {
                    width: 30px;
                    height: 30px;
                    left: -22px;
                    top: -14px;
                }
            }
        }
    }
    &--packages {
        //min-height: 280px;
        background-position: center bottom;
        .InternetHeader__laptop--forfaits {
            @media (max-width: 1024px) {
                width: 200px!important;
                top: 70px!important;
                right: 50px!important;
            }
        }
        @media (max-width: 1024px) {
            min-height: 196px;
            background-position: -745px 100%;
        }
        @media (max-width: 767px) {
            background-position: center top;
        }
    }
    @media (max-width:1919px) {
        background-size: 1920px 280px;
    }
    @media (max-width:1024px) {
        min-height: 296px;
        padding-top: 30px;
        background-size: 1920px 280px;
        background-position: center top -60px;
    }
    @media (max-width: 767px) {
        min-height: 90px;
        padding-top: 20px;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
}
.wrap-doodle {
    display: flex;
    flex-direction: row;
    svg {
        display: none;
        fill: $c-white;
        width: 100px;
        height: 100px;
        @media (max-width:991px) {
            width: 80px;
            height: 80px;
        }
        @media (max-width: 767px) {
            width: 50px;
            height: 50px;
        }
    }
    @media (max-width:767px) {

    }
}

.accountRight {
    display: flex;
    align-items: center;
    position: relative;
    @media(max-width: 1200px){
        margin-right: 48px;
    }
    @media(max-width: 360px){
        margin-right: 32px;
    }

}
.accountCircle {
    margin-left: 10px;
    height: 24px;
    @media (max-width: 1200px) {
        display: none;
    }
    svg {
        width: 24px;
        height: 24px;
        fill: $c-primary;
        transition: all 0.2s $cubic;
    }
    &:hover {
        svg {
            fill: $c-grey;
        }
    }
}
