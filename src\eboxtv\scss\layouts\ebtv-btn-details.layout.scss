.ebtv-btn-details  {
    position: absolute;
    display: flex;
    align-items: center;
    left: 160px;
    bottom: 100%;
    background: url('~@common/img/ebox-texture-background--red.jpg') center center repeat;
    border: 0;
    border-radius: 5px 5px 0 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    font: 700 16px/22px $ebtv-f-primary;
    color: $c-white;
    padding: 8px 28px;
    text-transform: uppercase;
    &__icon {
        width: 25px;
        height: 25px;
        margin-left: 8px;
        fill: $c-white;
        transform: rotate(-180deg);
    }

    @media (max-width: 1440px) {
        left: 60px;
    }
    @media (max-width: map-get($ebtvBreakpoints, laptop)) {
        left: 30px;
    }
    @media (max-width: 991px) {
        right: 30px;
        left: auto;
        flex-direction: row-reverse;
        &__icon {
            transform: rotate(-270deg);
            margin-left: 0;
            margin-right: 5px;
            width: 20px;
            height: 20px;
            display: none;
        }
    }
    @media (max-width: 450) {
        line-height: 30px;
    }
}
.ebtv-resume__ebtv-btn-details--toggled {
    .ebtv-btn-details__icon {
        transform: rotate(0);
    }
}