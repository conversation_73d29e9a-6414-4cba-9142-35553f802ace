/* global jQuery */
import { Directive } from '@core/directive';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';
export class footerRedirectionDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE


	//SELECTEUR DE LA DIRECTIVE
	static selector = '[data-footer-redirection]';
    langue = this.host.getAttribute('data-langue');
    province = this.host.getAttribute('data-province');

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {

        this.host.addEventListener('click', function(evt){
            evt.preventDefault();
            
            const expirationDate = new Date();
            const expirationDateEbox = new Date();
            expirationDate.setDate(expirationDate.getDate() - 3);
            expirationDateEbox.setDate(expirationDate.getDate() + 30);
            CookiesService.setCookies({
                name: 'eboxQualification',
                value: false,
                expires: expirationDate
            });	
            CookiesService.setCookies({
                name: 'CommandeStarted',
                value: 'oui',
                expires: expirationDate
            });	
            
            CookiesService.setCookies({
                name: 'isTvPackageSelected',
                value: 'no',
                expires: expirationDate
            });		
            
            CookiesService.setCookies({
                name: 'eboxlangue',
                value: this.langue,
                expires: expirationDateEbox
            });		

            CookiesService.setCookies({
                name: 'eboxprovince',
                value: this.province,
                expires: expirationDateEbox
            });

            localStorage.clear();
            window.location.href = evt.currentTarget.href;
        }.bind(this))
    }


    // _onHostClick(evt) {

    // }

}
