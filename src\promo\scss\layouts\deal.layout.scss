.deal {
	position: relative;
	z-index: 1;

	&__title {
		font: 900 30px/28px $f-primary;
		color: $c-primary-dark;
		margin-bottom: 30px;
		text-align: center;
	}

	&__body {
		color: $c-white;
	}

	&__price-container {
		margin-bottom: 20px;
		&:after {
			content: '';
			position: absolute;
			top: 40px;
			right: 0;
		}
	}

	&__price-int, &__price-float-container {
		display: inline-block;
		font-weight: 900;
		letter-spacing: 1px;
		vertical-align: middle;
		text-align: center;
	}

	&__price-float-container {
		margin-left: -3px;
		margin-top: -10px;
	}

	&__price-int {
		font-size: 98px;
		line-height: 80px;
	}
	
	&__price-float, &__price-dollar {
		font-size: 39px;
		line-height: 1;
		// margin-bottom: 6px;
		// padding-top: 5px;
	}

	&__price-float {
		display: block;
	}

	&__price-dollar {
		display: inline-block;
		font-weight: 900;

		@include fromRootSelector(':lang(en_CA)') {
			vertical-align: middle;
		}
	}

	&__per-month-icon {
		display: block;
		width: 63px;
		height: 15px;
		fill: $c-primary-dark;
		margin-left: 5px;

		@include fromRootSelector(':lang(en_CA)') {
			height: 19px;
		}
	}

	&__feature-list {
		margin-bottom: 30px;
	}

	&__feature-item {
		font: 500 16px/24px $f-primary;
	}

	&__doodle {
		position: absolute;
		width: 115px;
		height: 110px;
		background: {
			size: contain;
			repeat: no-repeat;
			position: center;
		};

		&--top-left {
			top: 0;
			right: 100%;
		}

		&--top-right {
			top: 0;
			left: 100%;
		}

		&--bottom-right {
			bottom: 0;
			left: 100%;
		}

		&--bottom-left {
			bottom: 0;
			right: 100%;
		}
	}
}