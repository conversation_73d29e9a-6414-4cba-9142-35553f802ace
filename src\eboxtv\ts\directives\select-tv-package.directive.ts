import { SetCookieDirective } from '@common/js/directives/set-cookie.directive';
import { TVOrderService } from '@eboxtv/js/services';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { OrderOnlineService } from "@eboxtv/js/services/orderOnline.service";
import { CoreTools } from '@core/helpers';
import { PromotionsService } from '@eboxtv/js/services/promotions.service';
import { DecodeurDirective } from '@eboxtv/js/directives/decodeur.directive';
import { LocalStorage } from '@common/ts/services/LocalStorage';
export class SelectTVPackage extends SetCookieDirective {
	public static selector: string = '[select-tv-package]';

	private tvOrder: TVOrderService = TVOrderService.getInstance();
	private orderService = OrderOnlineService.getInstance();
	private promotionsService = PromotionsService.getInstance();

	private hasTV: 'true' | 'false';
	private valid: boolean;

	public attrs: {
		packageID: any;
		redirectTo?: string;
		href?: string;
	};

	constructor(host: HTMLAnchorElement) {
		super(host, [
			{ name: 'select-tv-package', as: 'packageID', default: null },
			'redirect-to'
		]);
		let stepValidation : any = this.orderService.getStepValidation();
        if (!stepValidation.internet) {
            if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
        } else {

			var currentBundle = this.tvOrder.getSelectedBundle();
			var { packageID } = this.attrs;
			var element = <HTMLInputElement>this.host;

			var hasTV = CookiesService.getCookies('hasTV');
			var isTvPackageSelected = CookiesService.getCookies('isTvPackageSelected');

			if (isTvPackageSelected == 'true') {
				if (currentBundle != null && packageID == currentBundle.ID && element.type == 'radio') {
					this.host.setAttribute('checked', 'checked');
					this.host.parentElement.parentElement.parentElement.classList.add('TVForfaitItem__active');
					if (document.getElementsByClassName("recepteur_tele")[0] != undefined) {
						document.getElementsByClassName("recepteur_tele")[0].classList.remove('hidden');
						document.getElementsByClassName("voip__infonuagique")[0].classList.remove('hidden');
					}

				}

				if (currentBundle == null && packageID == null && hasTV == 'true' && element.type == 'radio') {
					this.host.setAttribute('checked', 'checked');
					document.getElementsByClassName("recepteur_tele")[0].classList.remove('hidden');
					document.getElementsByClassName("voip__infonuagique")[0].classList.remove('hidden');

				}

				if (currentBundle == null && packageID == 'no' && hasTV == 'false' && element.type == 'radio') {
					this.host.parentElement.parentElement.classList.add('TVForfaitItem__active');
					this.host.setAttribute('checked', 'checked');
				}
			} else {

				if (packageID == null) {

				}

				// CookiesService.setCookies({
				// 	name: 'hasTV',
				// 	value: true
				// });
			}
		}


		['.order-summary__navbutton', '.eboxtv-navbutton'].forEach((classNamme) => {
			jQuery(classNamme).on('click', (evt: any) => {
				evt.preventDefault();
				this.hasTV = CookiesService.getCookies('hasTV');
				if (this.hasTV == 'false') {
					let packageInput : any = document.querySelectorAll('input[name="downloadSpeed"]');
					packageInput = Array.from(packageInput)
					for (let i = 0; i < packageInput.length ; i++) {
						const el = packageInput[i];
						if(el.checked == true){
							this.valid = true;
						}
					}
					

					this.orderService.setStepValidation('tv', this.valid);
					if(this.valid){
					window.location.href = CoreTools.translate('/commande-telephonie', '/telephony-order');
					}
				} else {
					this.validateForm();
					this.orderService.setStepValidation('tv', this.valid);
					if (this.valid) {
						window.location.href = evt.currentTarget.href;
					}
				}
			});
		});




		jQuery('#noTVLink').on('click', () => {
			this.orderService.setStepValidation('tv', true);
			window.location.href = CoreTools.translate('/commande-telephonie','/telephony-order');
		});
	}

	validateForm() {
		if (this.hasTV == 'true') {
			let decodeurValid = localStorage.getItem('decodeur');
			let nuagiqueValid = localStorage.getItem('eboxTVOptions');
			if (!decodeurValid) {
				this.scrollTo(document.querySelectorAll('.recepteur_tele')[0]);
				document.querySelectorAll('.recepteur_tele')[0].classList.add('error');
			}
			else {
				document.querySelectorAll('.recepteur_tele')[0].classList.remove('error');
			}

			if (!nuagiqueValid) {
				document.querySelectorAll('.voip__infonuagique')[0].classList.add('error');
				this.scrollTo(document.querySelectorAll('.voip__infonuagique')[0]);

			}
			else {
				document.querySelectorAll('.voip__infonuagique')[0].classList.remove('error');
			}
			this.valid = nuagiqueValid && decodeurValid ? true : false;
		}
	}

	setCookie(): void {
		let { packageID } = this.attrs;

		if (packageID == 'no') {
			CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});

			if (document.getElementsByClassName("recepteur_tele")[0] != undefined) {
				document.getElementsByClassName("recepteur_tele")[0].classList.add('hidden');
				document.getElementsByClassName("voip__infonuagique")[0].classList.add('hidden');
			}

			packageID = null;
		} else {
			CookiesService.setCookies({
				name: 'hasTV',
				value: true
			});

			document.getElementsByClassName("recepteur_tele")[0].classList.remove('hidden');
			document.getElementsByClassName("voip__infonuagique")[0].classList.remove('hidden');


		}

		if (packageID === null) {
			this.tvOrder.removeBundle();
			CookiesService.setCookies({
				name: 'hasFeaturedBundle',
				value: false
			});
		} else {
			this.tvOrder.addBundle(packageID);

			const currentBundle: any = this.tvOrder.getSelectedBundle();

			if (currentBundle) {
				CookiesService.setCookies({
					name: 'hasFeaturedBundle',
					value: currentBundle.isFeatured
				});
			}
		}

		// Cookie pour savoir que l'utilisateur à selectionné une option
		CookiesService.setCookies({
			name: 'isTvPackageSelected',
			value: true
		});

		this.orderService.initCart();
	}

	_onHostClick(evt: any) {
		if(evt.target != document.getElementById('noTVLink')){
			var element = <HTMLScriptElement>this.host;
			if (element.type != 'radio') {
				evt.preventDefault();
			}
	
			if(evt.target.id == 'downloadSpeed'){
				evt.target.parentElement.parentElement.classList.add('TVForfaitItem__active');
			}
			else{
				document.getElementById('downloadSpeed').parentElement.parentElement.classList.remove('TVForfaitItem__active');
			}
			if(evt.target.id != 'uniqueID'){
				jQuery('input[name=' + evt.target.name + ']').parent().parent().parent().removeClass('TVForfaitItem__active');
				jQuery('input[name=' + evt.target.name + ']:checked').parent().parent().parent().addClass('TVForfaitItem__active');
			}
	
			// Setting cookie
			this.setCookie();
			const decodeur = LocalStorage.get('decodeur');
			this.promotionsService.getInternetPromotion();
			this.promotionsService.getEquipementInternetPromotion();
			if(decodeur != null){
				this.promotionsService.promoDecodeur(decodeur.qty, decodeur.buyRent);
			}
	
			this.orderService.initCart();
			// If the host has a href, we redirect the user to that href.
			this.redirect();
		}
		else{
			evt.preventDefault();
			CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});
			const decodeur = LocalStorage.get('decodeur');
			this.promotionsService.getInternetPromotion();
			this.promotionsService.getEquipementInternetPromotion();
			if(decodeur != null){
				this.promotionsService.promoDecodeur(decodeur.qty, decodeur.buyRent);
			}
		}


	}

	redirect() {
		let href;

		if (this.attrs.redirectTo) {
			href = this.attrs.redirectTo;
		} else if (this.attrs.href) {
			href = this.attrs.href;
		}

		if (href !== undefined) {
			location.href = href;
		}
	}

	scrollTo(el: any) {
		el.scrollIntoView({ behavior: "smooth" });
	}
}