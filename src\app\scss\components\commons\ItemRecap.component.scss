.ItemRecap {
	&--has-special-promo-image {
		.ItemRecap {
			&__from {
				margin-bottom: 8px;
			}

			&__price-wrapper {
				justify-content: space-between;
				padding-bottom: 12px;
			}

			&__price {
				font: 900 37px/1 $f-primary;

				span {
					top: 21px;
					right: -3px;
					font: 400 7px/1 $f-secondary;

					@include applyAtRoot('html:lang(en-CA)') {
						right: -5px;
					}

					sup {
						font: 400 4px/1 $f-secondary;
						top: 0px;
					}
				}

				&:after {
					content: '';
					position: absolute;
					display: inline-block;
					border-radius: 0.5px;
					width: 1px;
					height: 46px;
					background: #979797;
					top: 50%;
					left: 90%;
					margin-left: 25px;
					transform: translateY(-50%) rotate(17deg)
				}
			}
	
			&__special-promo-image {
				width: 49px;
				height: auto;
			}
		}
	}
}