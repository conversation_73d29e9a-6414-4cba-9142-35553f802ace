# 📚 DOCUMENTATION COMPLÈTE - SITE WEB EBOX

## 🎯 INTRODUCTION ET CONTEXTE

Le site web EBOX est une plateforme e-commerce complexe développée pour un fournisseur de services Internet, télévision et téléphonie au Canada (Québec et Ontario). Le projet combine une architecture WordPress moderne avec un système JavaScript/TypeScript avancé utilisant des directives personnalisées pour gérer l'expérience utilisateur de commande en ligne.

### Technologies Principales
- **Backend** : WordPress 5.x avec thème personnalisé
- **Frontend** : TypeScript/JavaScript ES6+, SCSS, Bootstrap 3.3.7
- **Build System** : Webpack 3.6.0, Gulp 4.x, NPM
- **Librairies** : GSAP 1.19.1, jQuery, RxJS 5.5.6, Owl Carousel
- **Outils** : Babel, PostCSS, ImageMin, BrowserSync

---

## 🏗️ ARCHITECTURE GÉNÉRALE DU PROJET

### Structure des Dossiers Racine
```
ebox-site-web/
├── src/                    # Code source TypeScript/JavaScript/SCSS
├── wordpress/              # Installation WordPress complète
├── webpack.config.js       # Configuration Webpack
├── gulpfile.js            # Tâches Gulp pour images
├── package.json           # Dépendances NPM
├── tsconfig.json          # Configuration TypeScript
└── postcss.config.js      # Configuration PostCSS
```

### Système de Build Multi-Environnements

Le projet utilise un système de build sophistiqué avec trois environnements :

**1. Development (dev)**
- Webpack en mode watch avec source maps
- BrowserSync pour le rechargement automatique
- Assets non minifiés pour le debugging
- Base URL : https://beta3.ebox.ca

**2. Staging**
- Build optimisé avec compression
- Images copiées sans optimisation lourde
- Tests de performance et fonctionnalités

**3. Production**
- Minification complète des assets
- Optimisation avancée des images (WebP, compression)
- Compression Gzip des fichiers
- Cache busting avec hashes

### Configuration Webpack Détaillée

Le fichier `webpack.config.js` définit trois points d'entrée principaux :

```javascript
entry: {
    'app': [
        './src/_common/scss/common.scss',
        './src/app/scss/app-styles.scss',
        './src/app/ts/app.ts'
    ],
    'eboxtv': [
        './src/_common/scss/common.scss',
        './src/eboxtv/scss/styles.scss',
        './src/eboxtv/js/app.js'
    ],
    'promo': [
        './src/_common/scss/common.scss',
        './src/promo/scss/styles.scss',
        './src/promo/ts/app.ts'
    ]
}
```

**Alias de Résolution** :
- `@core` : Framework de directives personnalisées
- `@common` : Assets partagés entre modules
- `@app` : Module application principale
- `@eboxtv` : Module télévision/commande
- `@bootstrap` : Bootstrap SASS

---

## 📁 MODULE COMMON (_common/)

Le module `_common` constitue le cœur technique du projet, fournissant l'infrastructure partagée.

### Structure Détaillée
```
_common/
├── js/
│   ├── core/              # Framework de directives
│   │   ├── bootstrap.js   # Classe App principale
│   │   ├── directive.js   # Classe Directive de base
│   │   └── base.js        # Utilitaires DOM
│   ├── directives/        # Directives communes
│   └── jQuery.isWithin.js # Extensions jQuery
├── scss/
│   ├── helpers/           # Mixins et variables
│   ├── layouts/           # Layouts partagés
│   └── common.scss        # Point d'entrée SCSS
└── ts/
    ├── services/          # Services TypeScript
    └── directives/        # Directives TypeScript
```

### Framework de Directives Personnalisées

Le système de directives est inspiré d'Angular mais adapté pour WordPress :

```javascript
// Classe App - Bootstrap principal
export class App {
    constructor({services = [], directives = [], runs = [], rootElement = document}) {
        this._services = services;
        this._directives = directives;
        this._rootElement = rootElement;
    }

    bootstrap(useRequestIdleCallback = true) {
        // Initialisation des services
        this._initServices();
        // Initialisation des directives
        this._initDirectives();
        // Exécution des fonctions de démarrage
        this._initRuns();
    }
}
```

**Classe Directive de Base** :
```javascript
export class Directive {
    static selector = '[directive-name]';

    constructor(host, attributes = []) {
        this.host = host;
        this.attrs = this._parseAttributes(attributes);
        this._init();
    }

    _init() {
        // Logique d'initialisation
    }

    onAppInit() {
        // Appelé après l'initialisation complète de l'app
    }
}
```

### Services Communs Critiques

**1. ViewChangeDetector Service**
- Détecte les changements de viewport
- Gère les breakpoints responsive
- Optimise les performances avec throttling

**2. Tools Service**
- Utilitaires de manipulation DOM
- Fonctions de validation
- Helpers de formatage

**3. CoreTools**
- Gestion de l'internationalisation
- Détection de langue (fr/en)
- Utilitaires de traduction

---

## 🏠 MODULE APP (app/)

Le module App gère le site principal EBOX avec les pages institutionnelles et commerciales.

### Structure et Responsabilités
```
app/
├── scss/
│   ├── components/        # Composants UI spécifiques
│   ├── layouts/           # Layouts de pages
│   └── app-styles.scss    # Point d'entrée
├── ts/
│   ├── directives/        # Directives spécifiques à l'app
│   ├── services/          # Services métier
│   └── app.ts             # Bootstrap de l'application
└── img/                   # Images spécifiques au module
```

### Directives Principales du Module App

**1. QualificationDirective**
- Gère la qualification d'adresse des utilisateurs
- Intégration avec l'API de géolocalisation
- Validation de l'éligibilité aux services

**2. TimelineDirective**
- Affiche la progression dans le parcours de commande
- Gestion des étapes actives/complétées
- Animation des transitions

**3. ValidationController**
- Validation en temps réel des formulaires
- Gestion des messages d'erreur
- Intégration avec les APIs de validation

### Bootstrap de l'Application App

```javascript
const EboxApp = new App({
    directives: [
        QualifiedSpeedsComponent,
        ToggleClassOnClickDirective,
        ValidationController,
        TimelineDirective,
        // ... autres directives
    ],
    trailers: [
        popUpListing  // Fonctions exécutées après initialisation
    ]
}).bootstrap(false);
```

---

## 📺 MODULE EBOXTV (eboxtv/)

Le module EboxTV est le plus complexe, gérant tout le parcours de commande en ligne.

### Architecture Détaillée
```
eboxtv/
├── js/
│   ├── app.js             # Point d'entrée principal
│   ├── directives/        # 40+ directives spécialisées
│   │   ├── channel-select.directive.js
│   │   ├── order-counter.directive.js
│   │   ├── personal-info.directive.js
│   │   ├── sticky-sidebar.directive.js
│   │   └── numerique-cart.directive.js
│   ├── services/          # Services métier
│   │   ├── orderOnline.service.js
│   │   ├── TVOrder.service.js
│   │   └── qualification.service.js
│   └── polyfills/         # Compatibilité navigateurs
├── scss/
│   ├── components/        # Composants UI complexes
│   ├── layouts/           # Layouts de commande
│   └── styles.scss        # Point d'entrée SCSS
└── fonts/                 # Polices spécifiques

### Services Critiques du Module EboxTV

**1. OrderOnlineService (Singleton)**
```javascript
export class OrderOnlineService {
    static getInstance() {
        if (!OrderOnlineService._instance) {
            OrderOnlineService._instance = new OrderOnlineService();
        }
        return OrderOnlineService._instance;
    }

    // Gestion de l'état de la commande
    _personalInfoFormStorage(form) {
        // Sauvegarde des informations personnelles
    }

    sendForm() {
        // Envoi de la commande vers l'API
        return fetch('/api/order', {
            method: 'POST',
            body: this._buildFormData()
        });
    }

    getStepValidation() {
        // Validation des étapes de commande
        return {
            internet: this._validateInternet(),
            tv: this._validateTV(),
            phone: this._validatePhone()
        };
    }
}
```

**2. TVOrderService**
- Gestion spécifique des commandes télévision
- Calcul des prix avec promotions
- Gestion des chaînes et forfaits

### Directives Clés du Parcours de Commande

**1. PersonalInfoDirective**
```javascript
export class PersonalInfoDirective extends Directive {
    static selector = '[personal-info]';

    constructor(host) {
        super(host);
        this._$OrderService = OrderOnlineService.getInstance();
        this.form = document.forms['personalInfo'];
        this._initFormValidation();
    }

    validateForm() {
        // Validation complète du formulaire
        this.$valid = this._validateAllFields();
        this._updateSubmitButton();
    }

    submitForm(event) {
        event.preventDefault();
        if (this.$valid) {
            this._$OrderService.sendForm().then(response => {
                if (response.RESPONSE.status === 'OK') {
                    window.location.href = '/confirmation';
                }
            });
        }
    }
}
```

**2. StickySidebarDirective**
```javascript
export class StickySidebarDirective extends Directive {
    static selector = '[sticky-sidebar]';

    constructor(host) {
        super(host, [
            {name: 'sticky-options', as: 'options', type: 'eval', default: {}}
        ]);
        this._stickyInstance = new StickySidebar(this.host, this.attrs.options);
        this._subscribeToViewChange();
    }

    _subscribeToViewChange() {
        this._viewChangeSub = this.$changeDetector.subscribe(() => {
            this._stickyInstance.updateSticky();
        });
    }
}
```

**3. CommandeCostCounterDirective**
```javascript
export class CommandeCostCounterDirective extends CommandeOrderCounterDirective {
    static selector = '[commande-cost-counter]';

    constructor(host) {
        super(host, [
            {name: 'cost-type', as: 'costType', default: 'month'},
            {name: 'raise-decimals', as: 'raiseDecimals', type: 'boolean'},
            {name: 'no-commas', as: 'noCommas', type: 'boolean'}
        ]);
        this._subscribeToOrderChanges();
    }

    _updateDisplay() {
        const cost = this._calculateCost();
        const formattedCost = this._formatCurrency(cost);
        this.host.innerHTML = formattedCost;
    }

    _formatCurrency(amount) {
        if (this.attrs.raiseDecimals) {
            const [integer, decimal] = amount.toFixed(2).split('.');
            return `${integer}<sup>${decimal}$</sup>`;
        }
        return `${amount.toFixed(2)}$`;
    }
}
```

---

## 🛒 SYSTÈME DE COMMANDE EN LIGNE - ANALYSE DÉTAILLÉE

### Parcours Utilisateur Complet

**Étape 1 : Qualification d'Adresse**
- Saisie de l'adresse par l'utilisateur
- Validation via API de géolocalisation
- Vérification de l'éligibilité aux services
- Redirection selon la disponibilité

**Étape 2 : Sélection Internet**
- Affichage des forfaits disponibles selon la zone
- Calcul des prix avec promotions actives
- Sélection du forfait et options (modem, installation)
- Sauvegarde dans localStorage

**Étape 3 : Sélection Télévision (Québec uniquement)**
- Choix entre forfaits prédéfinis et à la carte
- Sélection des chaînes individuelles
- Calcul dynamique des prix
- Gestion des promotions (ex: 3 mois gratuits)

**Étape 4 : Sélection Téléphonie**
- Options de téléphonie résidentielle
- Fonctionnalités additionnelles
- Intégration avec les autres services

**Étape 5 : Sticky Cart (Présent sur toutes les étapes)**
- Affichage permanent du total
- Actions rapides (modifier, ajouter, supprimer)
- Navigation entre les étapes
- Mise à jour en temps réel

### Composant Sticky Cart - Analyse Technique

**Structure HTML** :
```html
<div class="VirageNum__sticky-cart" data-cartNumerique>
    <div class="container">
        <div class="VirageNum__price-details-content">
            <button class="details-title" aria-expanded="false">
                <span>Panier</span>
            </button>
            <div class="detail-items" style="display:none">
                <ul>
                    <li class="internetPrice">
                        <p>Internet</p>
                        <div class="sticky-cart-action-container">
                            <a class="sticky-cart-action-btn" href="/forfaits-internet/">
                                <svg class="icon icon-ic-edit-info2022"></svg>
                            </a>
                            <div class="sticky-cart-action-price">
                                <span class="detail-price"></span>
                            </div>
                        </div>
                    </li>
                    <!-- Répété pour TV et Téléphonie -->
                </ul>
            </div>
        </div>
        <div class="VirageNum__price-container">
            <a href="tel:18443233269">1-844-323-EBOX (3269)</a>
            <div class="sticky-cart-price-container">
                <span class="sticky-cart-price"><sup></sup></span>
                <span class="sticky-cart-bymonth">par mois</span>
            </div>
            <a class="ButtonEffect sticky-cart-btn" href="[next-step]">
                Continuer
            </a>
        </div>
    </div>
</div>
```

**Styles SCSS Critiques** :
```scss
.VirageNum__sticky-cart {
    background: #BE2323;  // Rouge EBOX
    box-shadow: 0px 2px 14px 0px rgba(0,0,0,0.5);
    position: fixed;
    bottom: 0px;
    z-index: 999;
    left: 0;
    width: 100%;

    .sticky-cart-price {
        font: 700 44px/44px "BrandonGrotesque", sans-serif;

        sup {
            font-size: 16px;
            top: -1.5em !important;
        }
    }

    .ButtonEffect {
        color: #BE2323;
        background: white;

        &:hover {
            color: white !important;
            background: transparent;
        }
    }
}
```

**JavaScript de Mise à Jour des Prix** :
```javascript
// Dans numerique-cart.directive.js
_updateStickyCart() {
    const orderSummary = this._$OrderService.getOrderSummary();

    orderSummary.items.forEach(item => {
        if (item.title === "Sous-total") {
            const price = item.cost.toFixed(2).split('.');
            const integer = price[0];
            const decimal = price[1];

            document.querySelector('.sticky-cart-price').innerHTML = integer;
            document.querySelector('.sticky-cart-price sup').textContent = decimal + '$';

            // Mise à jour pour les lecteurs d'écran
            document.querySelector('.sticky-cart-price-sr-only').innerHTML =
                `Total: ${integer} dollars ${decimal} cents`;
        }
    });
}
```

---

## 👤 PAGE INFORMATIONS PERSONNELLES - ANALYSE COMPLÈTE

### Structure du Template PHP

**Fichier** : `commande-info-personnelle.php`

```php
<?php
/* Template Name: Informations personnelles */
get_header();

$qualification = getQualification();
$env = env_query();

// Vérification de l'éligibilité
if (!isset($qualification) || !isset($qualification->details)) {
    wp_redirect("/");
    exit;
}

// Redirection si déjà client
if ($qualification->details->isClient == 1) {
    wp_redirect("/" . $env['province'] . "/passerelle-vers-espace-client/");
    exit();
}
?>

<div class="personnalInfos">
    <?php get_template_part('templates/parts/timeline/timeline', null,
          array('stepId' => 5, 'hidden' => "")); ?>

    <div class="container d-flex">
        <div class="order__wrapper">
            <div class="col-md-9">
                <form name="personalInfo" method="POST" action="./script/personal_info.php">
                    <!-- Section Code Promo -->
                    <div class="promoCode">
                        <h2><?php _e("CODE PROMO", "onlineOrder") ?></h2>
                        <div class="form-group">
                            <input type="text" name="promoCode" id="promoCode"
                                   placeholder="<?php _e('Code promo', 'onlineOrder') ?>">
                            <button type="button" id="applyPromoCode">
                                <?php _e('Appliquer', 'onlineOrder') ?>
                            </button>
                        </div>
                    </div>

                    <!-- Informations Utilisateur -->
                    <div class="userInfos">
                        <div class="userInfos__coords">
                            <div class="form-group">
                                <input type="text" name="userFirstName" required>
                                <label><?php _e("Prénom", "onlineOrder") ?></label>
                                <label class="form-error">
                                    <?php _e("Tu dois inscrire un prénom", "onlineOrder") ?>
                                </label>
                            </div>
                            <!-- Autres champs... -->
                        </div>
                    </div>

                    <div class="btnSubmit">
                        <button id="submitBtn" personal-info type="button">
                            <?php _e("Valider", "onlineOrder") ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Résumé de commande -->
        <?php include('templates/parts/commande-summary/order-summary.php'); ?>
    </div>
</div>
```

### Validation JavaScript Avancée

**Directive PersonalInfoDirective - Méthodes Clés** :

```javascript
validateForm() {
    this.$valid = true;
    const requiredFields = ['userFirstName', 'userLastName', 'userEmail', 'userPhone'];

    requiredFields.forEach(fieldName => {
        const field = this.form[fieldName];
        const isValid = this._validateField(field);

        if (!isValid) {
            this.$valid = false;
            this._showFieldError(field);
        } else {
            this._hideFieldError(field);
        }
    });

    // Validation spéciale pour l'âge
    if (!this._validateAge()) {
        this.$valid = false;
        this._showAgeError();
    }

    // Validation de l'adresse
    if (!this._validateAddress()) {
        this.$valid = false;
        this._showAddressError();
    }

    this._updateSubmitButton();
}

_validateField(field) {
    switch (field.name) {
        case 'userEmail':
            return this._validateEmail(field.value);
        case 'userPhone':
            return this._validatePhone(field.value);
        case 'userPostalCode':
            return this._validatePostalCode(field.value);
        default:
            return field.value.trim().length > 0;
    }
}

_validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

_validatePhone(phone) {
    // Validation pour numéros canadiens
    const phoneRegex = /^(\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;
    return phoneRegex.test(phone);
}

submitForm(event) {
    event.preventDefault();
    this.validateForm();

    if (this.$valid) {
        // Affichage du spinner
        jQuery('.full-screen-spinner-background').show();

        // Sauvegarde des données
        this._$OrderService._personalInfoFormStorage(this.form);

        // Envoi vers l'API
        this._$OrderService.sendForm().then(response => {
            jQuery('.full-screen-spinner-background').hide();

            if (response && response.RESPONSE.status === 'OK') {
                // Redirection vers paiement
                window.location.href = '/paiement';
            } else {
                this._handleSubmissionError(response);
            }
        }).catch(error => {
            jQuery('.full-screen-spinner-background').hide();
            this._handleSubmissionError(error);
        });
    }
}
```

---

## ✅ PAGE DE CONFIRMATION - ANALYSE DÉTAILLÉE

### Template PHP de Confirmation

**Fichier** : `confirmation-commande.php`

```php
<?php
/* Template Name: Confirmation de commande */
get_header();

// Récupération des données de paiement depuis le cookie
$response = json_decode(stripslashes($_COOKIE['PaymentResponseObject']));
?>

<div class="commande-merci">
    <div class="container">
        <div class="col-md-12">
            <!-- Icône de célébration -->
            <img class="paperplane"
                 src="<?php echo get_stylesheet_directory_uri();?>/img/Icon/ic-fireworks.svg"
                 alt="Célébration">

            <!-- Titre principal -->
            <p class="ThreeCalltos__title" role="heading" aria-level="1">
                <?php if(ICL_LANGUAGE_CODE == 'fr'): ?>
                    WOW, MERCI!
                <?php else: ?>
                    WOW, THANK YOU!
                <?php endif; ?>
            </p>

            <!-- Informations de commande -->
            <p class="commande-merci__infos">
                <?php if(ICL_LANGUAGE_CODE == 'fr'): ?>
                    Numéro de commande : <?php echo $response->INFO->order_id; ?>
                    <br>
                    Numéro de compte : <?php echo $response->INFO->code_client; ?>
                <?php else: ?>
                    Order Number: <?php echo $response->INFO->order_id; ?>
                    <br>
                    Account Number: <?php echo $response->INFO->code_client; ?>
                <?php endif; ?>
            </p>

            <!-- Messages de confirmation -->
            <p class="ThreeCalltos__subtitle">
                <?php if(ICL_LANGUAGE_CODE == 'fr'): ?>
                    Nous avons bien reçu ta commande. Merci d'avoir choisi EBOX!
                <?php else: ?>
                    We've received your order. Thanks for choosing EBOX!
                <?php endif; ?>
            </p>

            <p class="ThreeCalltos__subtitle">
                <?php if(ICL_LANGUAGE_CODE == 'fr'): ?>
                    Dans les prochaines minutes, tu recevras un courriel de confirmation
                    avec tous les détails, incluant les prochaines étapes pour
                    l'activation de tes services.
                <?php else: ?>
                    In just a few minutes, you'll get a confirmation email guiding
                    you through the activation process.
                <?php endif; ?>
            </p>
        </div>
    </div>
</div>

<?php get_footer(); ?>
```

### Styles SCSS de la Page de Confirmation

```scss
.commande-merci {
    padding: 70px 0px;

    .col-md-12 {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .paperplane {
        margin-bottom: 60px;
        height: 100px;
        width: 100px;
    }

    .ThreeCalltos__title {
        font-size: 55px;
        padding-bottom: 25px;
        line-height: 65px;
        text-align: center;
        color: $c-primary;
        font-weight: 700;

        @media(max-width: 767px) {
            font-size: 40px;
            line-height: 50px;
        }
    }

    .ThreeCalltos__subtitle {
        font-size: 20px;
        margin-bottom: 70px;
        text-align: center;
        line-height: 36px;
        max-width: 600px;

        @media(max-width: 991px) {
            font-weight: 300;
            font-size: 18px;
        }
    }

    .commande-merci__infos {
        text-align: center !important;
        font-family: "BrandonGrotesque", sans-serif !important;
        font-weight: 700 !important;
        font-size: 20px !important;
        margin-bottom: 35px !important;
        line-height: 30px !important;
        color: $c-primary;

        @media(max-width: 767px) {
            font-size: 18px !important;
        }
    }
}
```

---

## 🎨 SYSTÈME DE STYLES ET RESPONSIVE DESIGN

### Architecture SCSS Modulaire

**Structure des Styles Communs** :
```
_common/scss/
├── helpers/
│   ├── _variables.scss     # Variables globales
│   ├── _mixins.scss        # Mixins réutilisables
│   ├── _functions.scss     # Fonctions SCSS
│   └── _breakpoints.scss   # Points de rupture
├── layouts/
│   ├── _grid.scss          # Système de grille
│   ├── _typography.scss    # Typographie
│   └── _forms.scss         # Styles de formulaires
└── common.scss             # Point d'entrée
```

**Variables SCSS Critiques** :
```scss
// Couleurs principales EBOX
$c-primary: #BE2323;        // Rouge EBOX
$c-secondary: #343434;      // Gris foncé
$c-white: #FFFFFF;
$c-black: #000000;
$c-grey: #626262;
$c-grey-faded: #C7C7C7;

// Typographie
$font-primary: "BrandonGrotesque", sans-serif;
$font-secondary: "Gloria Hallelujah", cursive;

// Breakpoints personnalisés
$breakpoints: (
    mobile: 599px,
    tablet: 768px,
    desktop: 1024px,
    large: 1200px
);

// Z-index système
$z-index: (
    sticky-cart: 999,
    modal: 1000,
    tooltip: 1001
);
```

**Mixins Essentiels** :
```scss
// Mixin pour les boutons EBOX
@mixin button-ebox($bg-color: $c-primary, $text-color: white) {
    background: $bg-color;
    color: $text-color;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-family: $font-primary;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background: darken($bg-color, 10%);
        transform: translateY(-2px);
    }

    &:disabled {
        background: $c-grey-faded;
        cursor: not-allowed;
        transform: none;
    }
}

// Mixin pour les animations de chargement
@mixin loading-spinner($size: 40px, $color: $c-primary) {
    .lds-roller {
        display: inline-block;
        position: relative;
        width: $size;
        height: $size;

        div {
            animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            transform-origin: #{$size / 2} #{$size / 2};

            &:after {
                content: " ";
                display: block;
                position: absolute;
                width: #{$size / 10};
                height: #{$size / 10};
                border-radius: 50%;
                background: $color;
                margin: #{-$size / 20} 0 0 #{-$size / 20};
            }
        }
    }
}

// Responsive breakpoints
@mixin respond-to($breakpoint) {
    @if map-has-key($breakpoints, $breakpoint) {
        @media (max-width: map-get($breakpoints, $breakpoint)) {
            @content;
        }
    }
}
```

### Système de Grille Personnalisé

```scss
.container {
    max-width: 1170px;
    margin: 0 auto;
    padding: 0 15px;

    @include respond-to(tablet) {
        padding: 0 20px;
    }

    @include respond-to(mobile) {
        padding: 0 15px;
    }
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;

    &-md-9 {
        flex: 0 0 75%;
        max-width: 75%;

        @include respond-to(tablet) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    &-md-3 {
        flex: 0 0 25%;
        max-width: 25%;

        @include respond-to(tablet) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
}
```

---

## 🔧 SYSTÈME DE MODULES WORDPRESS

### Structure des Modules PHP

**Fichier Principal** : `modules/_modules.php`
```php
<?php
// Helpers utilitaires
include_once 'helpers/_helpers.php';

// Configuration du thème
include_once 'inc/setup.php';
include_once 'inc/css.php';
include_once 'inc/js.php';

// Système de mailing
include_once 'mailing.php';

// Composants réutilisables
include_once 'components/_components.php';
include_once 'components/wp_bem_menu.php';
include_once 'components/pagination.php';

// Menus et navigation
include_once 'menus/menus.php';

// Shortcodes personnalisés
include_once 'shortcodes/_shortcodes.php';
?>
```

### Helpers PHP Critiques

**Fonction de Qualification** :
```php
function getQualification() {
    if (isset($_COOKIE['qualification'])) {
        return json_decode(stripslashes($_COOKIE['qualification']));
    }
    return null;
}

function env_query() {
    $current_url = $_SERVER['REQUEST_URI'];
    $langue = (strpos($current_url, '/en/') !== false) ? 'en' : 'fr';
    $province = 'quebec'; // Défaut

    if (strpos($current_url, '/ontario/') !== false ||
        strpos($current_url, '/on/') !== false) {
        $province = 'ontario';
    }

    return [
        'langue' => $langue,
        'province' => $province,
        'secteur' => 'residentiel',
        'fournisseur' => 'ebox'
    ];
}
```

**Gestion des Assets** :
```php
// inc/css.php
function ebox_enqueue_styles() {
    $version = wp_get_theme()->get('Version');

    // Styles principaux selon la page
    if (is_page_template('ebox-tv-landing.php') ||
        is_page_template('commande-tv-*.php')) {
        wp_enqueue_style('eboxtv-styles',
            get_template_directory_uri() . '/assets/eboxtv/eboxtv.bundle.css',
            [], $version);
    } else {
        wp_enqueue_style('app-styles',
            get_template_directory_uri() . '/assets/app/app.bundle.css',
            [], $version);
    }
}
add_action('wp_enqueue_scripts', 'ebox_enqueue_styles');

// inc/js.php
function ebox_enqueue_scripts() {
    $version = wp_get_theme()->get('Version');

    // Scripts communs
    wp_enqueue_script('common-scripts',
        get_template_directory_uri() . '/assets/common-scripts.js',
        ['jquery'], $version, true);

    // Scripts spécifiques selon la page
    if (is_page_template('ebox-tv-landing.php') ||
        is_page_template('commande-tv-*.php')) {
        wp_enqueue_script('eboxtv-scripts',
            get_template_directory_uri() . '/assets/eboxtv/eboxtv.bundle.js',
            ['common-scripts'], $version, true);
    } else {
        wp_enqueue_script('app-scripts',
            get_template_directory_uri() . '/assets/app/app.bundle.js',
            ['common-scripts'], $version, true);
    }

    // Variables globales pour JavaScript
    wp_localize_script('common-scripts', 'EboxGlobals', [
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ebox_nonce'),
        'currentLang' => ICL_LANGUAGE_CODE,
        'apiBaseUrl' => get_option('ebox_api_base_url', 'https://api.ebox.ca')
    ]);
}
add_action('wp_enqueue_scripts', 'ebox_enqueue_scripts');
```

### Composants PHP Réutilisables

**Composant de Résumé de Commande** :
```php
// templates/parts/commande-summary/order-summary.php
function render_order_summary($show_timeline = true) {
    ?>
    <div class="order__summary order-summary"
         commande-side-panel
         trigger-selector=".order-summary__tab-button"
         closer-selector=".order-summary__close-btn"
         media-query="(max-width: 1024px)"
         opened-class-name="order-summary--opened">

        <div class="order-summary__wrapper">
            <button class="TabButton order-summary__tab-button">
                <div class="TabButton__content">
                    <h6 class="TabButton__title">Total</h6>
                    <p class="TabButton__price"
                       commande-cost-counter
                       cost-type="total"
                       raise-decimals="true"
                       no-commas="true"
                       lang="<?php echo ICL_LANGUAGE_CODE; ?>">
                    </p>
                </div>
                <div class="TabButton__foot">
                    <span class="TabButton__foot-title">
                        <?php _e('Mon panier', 'onlineOrder'); ?>
                    </span>
                    <svg class="TabButton__foot-icon">
                        <use xlink:href="#icon-arrow"></use>
                    </svg>
                </div>
            </button>

            <div class="order-summary__sticky-track"
                 sticky-sidebar="top"
                 sticky-options="{ minWidth: 1025 }">

                <div class="order-summary__sticky-wrapper">
                    <div class="order-summary__head">
                        <h3 class="order-summary__title">
                            <?php _e('Ta sélection', 'eboxtv'); ?>
                        </h3>
                        <button class="order-summary__close-btn">
                            <svg class="order-summary__close-icon">
                                <use xlink:href="#icon-close"></use>
                            </svg>
                        </button>
                    </div>

                    <div class="order-summary__body">
                        <!-- Contenu dynamique généré par JavaScript -->
                        <div data-show-on-init>
                            <?php
                            commande_summary_section_component('chargeMensuel', false, true, true);
                            ?>
                        </div>
                    </div>

                    <div class="order-summary__footer" data-show-on-init>
                        <div class="order-summary__footer-wrapper">
                            <div class="order-summary__footer-section">
                                <span commande-total-order-counter
                                      item-name="<?php _e('service', 'eboxtv'); ?>"
                                      show-count="true"
                                      class="order-summary__title-summary">
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}
```

---

## 🚀 OPTIMISATION ET PERFORMANCE

### Stratégies de Performance Implémentées

**1. Code Splitting et Lazy Loading**
```javascript
// Webpack configuration pour le code splitting
module.exports = {
    optimization: {
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    enforce: true
                }
            }
        }
    }
};
```

**2. Optimisation des Images**
```javascript
// gulpfile.js - Optimisation avancée des images
gulp.task('imagemin', function() {
    return gulp.src('src/theme-images/**/*')
        .pipe(imagemin([
            // PNG - Compression avec pngquant
            imageminPngquant({
                speed: 8,
                quality: [0.65, 0.8]
            }),
            // JPEG - Compression avec mozjpeg
            imageminMozjpeg({
                quality: 75,
                progressive: true
            }),
            // SVG - Optimisation avec SVGO
            imagemin.svgo({
                plugins: [
                    {removeViewBox: false},
                    {cleanupIDs: false}
                ]
            }),
            // GIF - Compression avec giflossy
            imageminGiflossy({
                optimizationLevel: 3,
                optimize: 3,
                lossy: 2
            })
        ]))
        .pipe(gulp.dest('wordpress/wp-content/themes/ebox2018/img'));
});
```

**3. Mise en Cache Intelligente**
```php
// Gestion du cache des assets
function ebox_add_cache_busting() {
    $version = filemtime(get_template_directory() . '/assets/app/app.bundle.css');
    return $version;
}

// Headers de cache pour les assets statiques
function ebox_set_cache_headers() {
    if (strpos($_SERVER['REQUEST_URI'], '/assets/') !== false) {
        header('Cache-Control: public, max-age=31536000'); // 1 an
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    }
}
add_action('init', 'ebox_set_cache_headers');
```

**4. Optimisation JavaScript**
```javascript
// Utilisation de requestIdleCallback pour les tâches non critiques
class PerformanceOptimizer {
    static scheduleNonCriticalTask(callback) {
        if ('requestIdleCallback' in window) {
            requestIdleCallback(callback, { timeout: 2000 });
        } else {
            setTimeout(callback, 1);
        }
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
}
```

---

## 🔒 SÉCURITÉ ET VALIDATION

### Validation Côté Client

**Système de Validation Robuste** :
```javascript
class FormValidator {
    constructor(form) {
        this.form = form;
        this.rules = new Map();
        this.errors = new Map();
    }

    addRule(fieldName, validator, errorMessage) {
        if (!this.rules.has(fieldName)) {
            this.rules.set(fieldName, []);
        }
        this.rules.get(fieldName).push({ validator, errorMessage });
    }

    validate() {
        this.errors.clear();
        let isValid = true;

        for (let [fieldName, rules] of this.rules) {
            const field = this.form[fieldName];
            if (!field) continue;

            for (let rule of rules) {
                if (!rule.validator(field.value)) {
                    this.errors.set(fieldName, rule.errorMessage);
                    isValid = false;
                    break;
                }
            }
        }

        this._updateUI();
        return isValid;
    }

    _updateUI() {
        // Mise à jour de l'interface utilisateur
        for (let [fieldName, error] of this.errors) {
            const field = this.form[fieldName];
            const errorElement = field.parentNode.querySelector('.form-error');

            field.classList.add('error');
            if (errorElement) {
                errorElement.textContent = error;
                errorElement.style.display = 'block';
            }
        }

        // Nettoyage des champs valides
        for (let [fieldName] of this.rules) {
            if (!this.errors.has(fieldName)) {
                const field = this.form[fieldName];
                const errorElement = field.parentNode.querySelector('.form-error');

                field.classList.remove('error');
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            }
        }
    }
}

// Utilisation dans PersonalInfoDirective
const validator = new FormValidator(this.form);

validator.addRule('userEmail',
    value => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    'Adresse email invalide'
);

validator.addRule('userPhone',
    value => /^(\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/.test(value),
    'Numéro de téléphone invalide'
);

validator.addRule('userPostalCode',
    value => /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/.test(value),
    'Code postal canadien invalide'
);
```

### Sécurité Côté Serveur

**Protection CSRF et Validation** :
```php
// Validation et sécurisation des données
class EboxSecurityValidator {

    public static function validateNonce($nonce, $action = 'ebox_nonce') {
        return wp_verify_nonce($nonce, $action);
    }

    public static function sanitizePersonalInfo($data) {
        $sanitized = [];

        $sanitized['firstName'] = sanitize_text_field($data['userFirstName']);
        $sanitized['lastName'] = sanitize_text_field($data['userLastName']);
        $sanitized['email'] = sanitize_email($data['userEmail']);
        $sanitized['phone'] = preg_replace('/[^0-9+\-\s\(\)]/', '', $data['userPhone']);
        $sanitized['address'] = sanitize_text_field($data['userAddress']);
        $sanitized['postalCode'] = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $data['userPostalCode']));

        return $sanitized;
    }

    public static function validatePersonalInfo($data) {
        $errors = [];

        if (empty($data['firstName']) || strlen($data['firstName']) < 2) {
            $errors['firstName'] = 'Prénom requis (minimum 2 caractères)';
        }

        if (empty($data['lastName']) || strlen($data['lastName']) < 2) {
            $errors['lastName'] = 'Nom requis (minimum 2 caractères)';
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Adresse email invalide';
        }

        if (!preg_match('/^(\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/', $data['phone'])) {
            $errors['phone'] = 'Numéro de téléphone invalide';
        }

        if (!preg_match('/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/', $data['postalCode'])) {
            $errors['postalCode'] = 'Code postal canadien invalide';
        }

        return $errors;
    }

    public static function logSecurityEvent($event, $data = []) {
        error_log(sprintf(
            '[EBOX Security] %s - IP: %s - Data: %s',
            $event,
            $_SERVER['REMOTE_ADDR'],
            json_encode($data)
        ));
    }
}
```

---

## 📊 MONITORING ET ANALYTICS

### Système de Tracking Personnalisé

```javascript
class EboxAnalytics {
    constructor() {
        this.events = [];
        this.sessionId = this._generateSessionId();
        this.userId = this._getUserId();
    }

    trackEvent(category, action, label = '', value = 0) {
        const event = {
            category,
            action,
            label,
            value,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            userId: this.userId,
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        this.events.push(event);
        this._sendEvent(event);
    }

    trackOrderStep(step, data = {}) {
        this.trackEvent('Order', `Step_${step}`, JSON.stringify(data));
    }

    trackFormValidation(formName, errors = []) {
        this.trackEvent('Form', 'Validation', formName, errors.length);
    }

    trackPerformance(metric, value) {
        this.trackEvent('Performance', metric, '', value);
    }

    _sendEvent(event) {
        // Envoi vers Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', event.action, {
                event_category: event.category,
                event_label: event.label,
                value: event.value
            });
        }

        // Envoi vers système interne
        fetch('/wp-admin/admin-ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'ebox_track_event',
                nonce: EboxGlobals.nonce,
                event: JSON.stringify(event)
            })
        }).catch(error => {
            console.warn('Analytics tracking failed:', error);
        });
    }

    _generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    _getUserId() {
        // Récupération depuis cookie ou localStorage
        return localStorage.getItem('ebox_user_id') || 'anonymous';
    }
}

// Initialisation globale
window.EboxAnalytics = new EboxAnalytics();

// Tracking automatique des étapes de commande
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = document.body.className;

    if (currentPage.includes('commande-internet')) {
        EboxAnalytics.trackOrderStep('Internet_Selection');
    } else if (currentPage.includes('commande-tv')) {
        EboxAnalytics.trackOrderStep('TV_Selection');
    } else if (currentPage.includes('info-personnelle')) {
        EboxAnalytics.trackOrderStep('Personal_Info');
    } else if (currentPage.includes('confirmation')) {
        EboxAnalytics.trackOrderStep('Confirmation');
    }
});
```

---

## 🔧 MAINTENANCE ET DEBUGGING

### Outils de Debug Intégrés

```javascript
class EboxDebugger {
    constructor() {
        this.enabled = window.location.search.includes('debug=true');
        this.logs = [];
    }

    log(message, data = null, level = 'info') {
        if (!this.enabled) return;

        const logEntry = {
            timestamp: new Date().toISOString(),
            message,
            data,
            level,
            stack: new Error().stack
        };

        this.logs.push(logEntry);

        const style = this._getLogStyle(level);
        console.log(`%c[EBOX ${level.toUpperCase()}] ${message}`, style, data);
    }

    logDirective(directiveName, action, data = null) {
        this.log(`Directive ${directiveName}: ${action}`, data, 'directive');
    }

    logService(serviceName, method, data = null) {
        this.log(`Service ${serviceName}.${method}()`, data, 'service');
    }

    logPerformance(operation, duration) {
        this.log(`Performance: ${operation} took ${duration}ms`, null, 'performance');
    }

    exportLogs() {
        const blob = new Blob([JSON.stringify(this.logs, null, 2)],
                             { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ebox-debug-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    _getLogStyle(level) {
        const styles = {
            info: 'color: #2196F3; font-weight: bold;',
            warn: 'color: #FF9800; font-weight: bold;',
            error: 'color: #F44336; font-weight: bold;',
            directive: 'color: #4CAF50; font-weight: bold;',
            service: 'color: #9C27B0; font-weight: bold;',
            performance: 'color: #FF5722; font-weight: bold;'
        };
        return styles[level] || styles.info;
    }
}

// Instance globale
window.EboxDebugger = new EboxDebugger();

// Intégration dans les directives
export class Directive {
    constructor(host, attributes = []) {
        this.host = host;
        this.attrs = this._parseAttributes(attributes);

        EboxDebugger.logDirective(this.constructor.name, 'initialized', {
            selector: this.constructor.selector,
            host: this.host,
            attributes: this.attrs
        });

        this._init();
    }
}
```

Cette documentation complète couvre tous les aspects techniques du site EBOX, de l'architecture générale aux détails d'implémentation, en passant par les systèmes de sécurité, performance et maintenance. Elle constitue une référence exhaustive pour comprendre et maintenir le projet.
```
```