import { Directive } from '@core/directive';
import { TVOptionsService } from '../services/TVOptions.service';
import { OrderOnlineService } from '@eboxtv/js/services/orderOnline.service';
export class OptionSelectDirective extends Directive {
	static selector = 'input[option-select]';
	private orderOnline = OrderOnlineService.getInstance();
	public host: HTMLInputElement;

	//#region Private properties
	private $tvOptions: TVOptionsService = TVOptionsService.getInstance();
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLInputElement) {
		super(host, [
			{name: 'option-name', required: true},
			{name: 'option-id', type: 'int'}
		]);

		this.bindEvents();
		this.toggleSelection()
		this.$tvOptions.optionChange$.subscribe(this.onOptionChange.bind(this));
	}
	//#endregion

	//#region Private methods
	private bindEvents() {
		this.host.addEventListener('change', this.onHostChange.bind(this));
	}

	private onHostChange(evt: Event) {
		evt.preventDefault();
		this.toggleSelection()
		this.$tvOptions.selectOption(this.attrs.optionName, this.attrs.optionId);
		this.orderOnline.initCart();
	}

	private onOptionChange(selectedOptions: any) {
		const selectedOption = selectedOptions[this.attrs.optionName];

		if (selectedOption && selectedOption.ID == this.attrs.optionId) {
			this.host.checked = true;
		}
	}

	private toggleSelection(){
        jQuery('input[name="cloud_space"]').parent().parent().parent().removeClass('TVForfaitItem__active');
        jQuery('input[name="cloud_space"]:checked').parent().parent().parent().addClass('TVForfaitItem__active');
	}
	//#endregion
}