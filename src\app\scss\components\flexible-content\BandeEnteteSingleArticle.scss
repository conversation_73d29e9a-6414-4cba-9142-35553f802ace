.BandeEnteteSingleArticles {
    padding: 0 0 100px;
    .row {
        display: flex;
        align-items: center;
        @media (max-width: 991px) {
            display: block;
            align-items: none;
        }
    }
    &__left {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 45px 0 15px;
        &__date-categorie {
            margin: 0 0 20px 0;
            font-weight: 600;
            color: $c-black;
            span.divider {
                color: $c-light-grey;
                font-size: 20px;
                margin: 0 5px;
            }
        }
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 22px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
    }

    &__right {
        width: 50%;
        height: 320px;
        position: relative;
        float: left;
        padding: 0 15px;
        overflow: hidden;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.7s $cubic;
        will-change: opacity, transform;
        &--shown {
            opacity: 1;
            transform: translateX(0);
        }
        img {
            max-width: calc(100% - 30px);
            height: auto;
            display:block;
            position:absolute;
            left:0;
            right:0;
            top: 0;
            bottom:0;
            margin:auto;
            @media (max-width: 991px) {
                width: calc(100% - 39px);
            }
            @media (max-width: 767px) {
                width: 100%;
                max-width: 100%;
                position: relative;
            }
        }
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 22px;
            margin: 0 0 30px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
            height: auto;
        }
    }
    @media (max-width: 991px) {
        padding: 0 0 70px;
    }
}
