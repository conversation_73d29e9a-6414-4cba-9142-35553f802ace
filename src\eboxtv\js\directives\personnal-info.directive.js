/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { Tools } from '@common/ts/services/Tools.service';
import { QualifyService } from '@common/ts/services/Qualify.service';
import { CoreTools } from '@core/helpers';
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { get } from 'jquery';

export class PersonalInfoDirective extends Directive {

	// INSTANCE DU SERVICE
	nbre_code_promo = 0;
	_$OrderService = OrderOnlineService.getInstance()

	qualificator = QualifyService.getInstance();
	Qualification = this.qualificator.getQualification();

	static selector = '[personal-info]';

	// VARIABLES POUR LE FONCTIONNEMENT DU VALIDATEUR
	$form;
	$valid = true;
	$btnSubmit;
	$inputs = this.toArray(document.forms['personalInfo'].elements);
	langue = "";

	arrayCodePromo = [];
	//arrayCodePromoFIB500 = [];
	//arrayCodePromoFIB1000 = [];
	arrayCodePromoPERKO5 = [];
	arrayCodePromoVENNGO5 = [];
	arrayCodePromoBF24 = [];
	arrayCodePromoBW24 = [];
	creditRequest;
	availability;
	DSLInstallFee = 49.95;
	shippingFee = 15;
	isLastAddressCanadian = false;
	isCreditRequestDone = false;
	isCreditRequestApproved = false;
	equipementsInternet = {};
	equipementsPhone = {};
	inputDebounce;

	validatedDeliveryAddress = {};
	validatedLastAddress = {};
	qualificationAddress = {};
	temporaryAddressSuggestions = [];
	lastErrorParentId = null;
	internetPlan = {};

	tvStepTrigger = document.getElementById("tv-trigger");
    isTvAvailable = typeof (this.tvStepTrigger) != 'undefined' && this.tvStepTrigger != null;

	newRequest;
	isBuying;



	// transforme les tableaux d'elements html en array
	toArray(obj) {
		var array = [];
		for (var i = obj.length >>> 0; i--;) {
			array[i] = obj[i];
		}
		return array;
	}

	// LANCE LA VALIDATION DE TOUS LES CHAMPS DU FORMULAIRE
	validateForm() {
		this.$valid = true;
		this.lastErrorParentId = null;
		let formIndex = [];
		this.$inputs.forEach(input => {
			!formIndex.includes(input.name) ? formIndex.push(input.name) : null;
		});
		formIndex.forEach(index => {
			this.sendToValidator(document.forms['personalInfo'][index])
		});

		this.validateAvailabilities();

		if (this.lastErrorParentId) {

			this.srSpeak(jQuery(this.lastErrorParentId).find('.form-error').text(),'polite');
			jQuery('html, body').animate({
				scrollTop: jQuery(this.lastErrorParentId).offset().top - 100
			}, 1000);
		}
	}

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	getArrayCodePromo() {
		var validInternetPlanCodePromoArray = [];

		return validInternetPlanCodePromoArray;
	}

	/*getArrayCodePromoFIB500() {
		var validInternetPlanCodeFIB500 = [
			{key:'FIB00016', amount:'0' , duration: '1'},
			{key:'FIB00242', amount:'0' , duration: '1'}
		];
		return validInternetPlanCodeFIB500;
	}

	getArrayCodePromoFIB1000() {
		var validInternetPlanCodeFIB1000 = [
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'}
		];
		return validInternetPlanCodeFIB1000;
	}*/

	getArrayCodePromoBENNYQC() {
		// FIBRE 500 ET 1000
		var validInternetPlanCodeBENNYQC = [
			{key:'FIB00016', amount:'10' , duration: '1', prmCode:'PRM00418'},
			{key:'FIB00242', amount:'10' , duration: '1', prmCode:'PRM00418'},
			{key:'FIB00019', amount:'10' , duration: '1', prmCode:'PRM00409'},
			{key:'FIB00280', amount:'10' , duration: '1', prmCode:'PRM00409'},
			{key:'FIB00282', amount:'5' , duration: '1', prmCode:'PRM00413'},
			{key:'EBX00176', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00461', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00462', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00002', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00460', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00466', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00463', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'FIB00266', amount:'5' , duration: '1', prmCode:'PRM00417'},
			{key:'FIB00017', amount:'5' , duration: '1', prmCode:'PRM00417'}

		];
		return validInternetPlanCodeBENNYQC;
	}

	getArrayCodePromoBENNYON() {
		
		var validInternetPlanCodeBENNYON = [
			{key:'FIB00506', amount:'10' , duration: '1', prmCode:'PRM00410'},
			{key:'FIB00707', amount:'10' , duration: '1', prmCode:'PRM00410'},
			{key:'FIB00517', amount:'15' , duration: '1', prmCode:'PRM00411'},
			{key:'FIB00718', amount:'15' , duration: '1', prmCode:'PRM00411'},
			{key:'FIB00504', amount:'5' , duration: '1', prmCode:'PRM00414'},
			{key:'EBX00176', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00421', amount:'5' , duration: '1', prmCode:'PRM00416'},
			{key:'EBX00422', amount:'5' , duration: '1', prmCode:'PRM00416'},
			{key:'EBX00418', amount:'5' , duration: '1', prmCode:'PRM00416'},
			{key:'EBX00002', amount:'5' , duration: '1', prmCode:'PRM00415'},
			{key:'EBX00465', amount:'5' , duration: '1', prmCode:'PRM00416'},
			{key:'EBX00464', amount:'5' , duration: '1', prmCode:'PRM00416'}

		];
		return validInternetPlanCodeBENNYON;
	}

	getArrayCodePromoBF24() {
		var validInternetPlanCodeBF24 = [
			{key:'FIB00011', amount:'0' , duration: '1'},
			{key:'FIB00016', amount:'0' , duration: '1'},
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00240', amount:'0' , duration: '1'},
			{key:'FIB00242', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'},
			{key:'FIB00281', amount:'0' , duration: '1'},
			{key:'FIB00282', amount:'0' , duration: '1'},
			{key:'FIB00283', amount:'0' , duration: '1'},
			{key:'FIB00703', amount:'0' , duration: '1'},
			{key:'FIB00707', amount:'0' , duration: '1'},
			{key:'FIB00718', amount:'0' , duration: '1'},
			{key:'FIB00503', amount:'0' , duration: '1'},
			{key:'FIB00504', amount:'0' , duration: '1'},
			{key:'FIB00506', amount:'0' , duration: '1'},
			{key:'FIB00517', amount:'0' , duration: '1'}
		];
		return validInternetPlanCodeBF24;
	}

	getArrayCodePromoCOLOC25() {
		var validInternetPlanCodeCOLOC25 = [
			{key:'FIB00266', amount:'0' , duration: '1'},
			{key:'FIB00017', amount:'0' , duration: '1'},
			{key:'FIB00242', amount:'0' , duration: '1'},
			{key:'FIB00016', amount:'0' , duration: '1'},
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'},
		];
		return validInternetPlanCodeCOLOC25;
	}

	// CODE PROMO CAMPUS25 (semblable à COLOC25)
	getArrayCodePromoCAMPUS25() {
		var validInternetPlanCodeCAMPUS25 = [
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'},
			{key:'FIB00016', amount:'0' , duration: '1'},
			{key:'FIB00242', amount:'0' , duration: '1'},
		];
		return validInternetPlanCodeCAMPUS25;
	}

	// CODE PROMO COMMICON
	getArrayCodePromoCCMTL25() {
		var validInternetPlanCodeCCMTL25 = [
			{key:'FIB00017', amount:'0' , duration: '1'},
			{key:'FIB00242', amount:'0' , duration: '1'},
			{key:'FIB00266', amount:'0' , duration: '1'},
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'},
			{key:'FIB00016', amount:'0' , duration: '1'},
		];
		return validInternetPlanCodeCCMTL25;
	}

	getArrayCodePromoBW24() {
		var validInternetPlanCodeBW24 = [
			{key:'FIB00718', amount:'0' , duration: '1'},
			{key:'FIB00517', amount:'0' , duration: '1'},
			{key:'FIB00019', amount:'0' , duration: '1'},
			{key:'FIB00280', amount:'0' , duration: '1'},
		];
		return validInternetPlanCodeBW24;
	}

	getArrayCodePromoPERKO5() {
		var validInternetPlanCodePERKO5 = [
			{key:'FIB00016', amount:'5' , duration: '1'},
			{key:'FIB00242', amount:'5' , duration: '1'},
			{key:'FIB00707', amount:'5' , duration: '1'},
			{key:'FIB00506', amount:'5' , duration: '1'},
			{key:'FIB00718', amount:'5' , duration: '1'},
			{key:'FIB00517', amount:'5' , duration: '1'},
			{key:'FIB00019', amount:'5' , duration: '1'},
			{key:'FIB00280', amount:'5' , duration: '1'},

		];
		return validInternetPlanCodePERKO5;
	}

	getArrayCodePromoVENNGO5() {
		var validInternetPlanCodeVENNGO5 = [
			{key:'FIB00707', amount:'5' , duration: '1'},
			{key:'FIB00242', amount:'5' , duration: '1'},
			{key:'FIB00506', amount:'5' , duration: '1'},
			{key:'FIB00016', amount:'5' , duration: '1'},
			{key:'FIB00718', amount:'5' , duration: '1'},
			{key:'FIB00517', amount:'5' , duration: '1'},
			{key:'FIB00019', amount:'5' , duration: '1'},
			{key:'FIB00280', amount:'5' , duration: '1'}
		];
		return validInternetPlanCodeVENNGO5;
	}

	_onInit() {

		this.arrayCodePromo = this.getArrayCodePromo();
		//this.arrayCodePromoFIB500 = this.getArrayCodePromoFIB500();
		//this.arrayCodePromoFIB1000 = this.getArrayCodePromoFIB1000();
		this.arrayCodePromoBF24 = this.getArrayCodePromoBF24();
		this.arrayCodePromoPERKO5 = this.getArrayCodePromoPERKO5();
		this.arrayCodePromoVENNGO5 = this.getArrayCodePromoVENNGO5();
		this.arrayCodePromoBW24 = this.getArrayCodePromoBW24();
		this.arrayCodePromoBENNYQC = this.getArrayCodePromoBENNYQC();
		this.arrayCodePromoBENNYON = this.getArrayCodePromoBENNYON();
		this.arrayCodePromoCOLOC25 = this.getArrayCodePromoCOLOC25();
		this.arrayCodePromoCCMTL25 = this.getArrayCodePromoCCMTL25();
		this.arrayCodePromoCAMPUS25 = this.getArrayCodePromoCAMPUS25();
		//console.log(this.arrayCodePromo);

		const expirationDate = new Date();
		expirationDate.setDate(expirationDate.getDate() - 10);
		CookiesService.setCookies({
			name: 'noCAAInternet',
			value: false,
			expires: expirationDate
		});

		// if benny
		var fournisseur_id = CookiesService.getCookies('fournisseur_id');
		//console.log('fournisseur_id');
		//console.log(fournisseur_id);

		if(fournisseur_id == 2){
			//console.log('benny');
			jQuery('.discountCode').hide();
		}

		// get cookie
		this.langue = CookiesService.getCookies('wp-wpml_current_language');
		var promo_code_referer = CookiesService.getCookies('promo_code_referer');
		if (promo_code_referer == 'CAADOLLARS') {
			if(this.langue == 'fr'){
				jQuery('#promoCode').val('RABAISCAA');
			}else{
				jQuery('#promoCode').val('CAAREBATE');
			}
			this.promoCodeValidator(jQuery('#promoCode')[0]);
		}

		// fin du test
		this.internetPlan = '';
		var internetLocal = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : '';
		this.internetPlan = internetLocal;

		var selection_internet = this._$OrderService.retInternetPlan();
        if( !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            
            if(!LocalStorage.get('qualificationAddresse')){
                province_complet = CookiesService.getCookies('eboxprovince');
            }else{
                var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
                var province_complet = "";

                if(province == "on"){
                    province_complet = 'ontario';
                }else{
                    province_complet = 'quebec';
                }
            }

			this.langue = CookiesService.getCookies('wp-wpml_current_language');
			var fournisseur = '';
			if(this.parseCookies().eboxQualification != undefined){
				var currentQualification = JSON.parse(decodeURI(this.parseCookies().eboxQualification));
				fournisseur = currentQualification.details.internetType;
			}

			if(province_complet != 'ontario' && province_complet != 'quebec'){
				province_complet = 'quebec';
			}
			
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/forfaits-internet/" + fournisseur);
            }else{
                window.location.replace("/en/" + province_complet + "/residential/internet-packages/" + fournisseur);
            }
            
        }

		this.isCodePromoReady();
		this.verifyAutomaticPromo();

		// Retrait promo crave
		LocalStorage.delete('promoCrave');
		this.cravePromo();

		jQuery('#ramasseMoi').hide();
		jQuery('.activation-installation-plus').hide();
		jQuery('.question-livraison').hide();
		if(this.internetPlan.cableTech == 'FTTH'){
			jQuery('#listePrecisionCable').remove();
		}else{
			jQuery('#listePrecisionFitre').remove();
			jQuery('#ramassageFibre').remove();
			jQuery('#activationFibre').remove();
			jQuery('#activationFibre2').remove();
		}

		var deliveryInput = document.querySelector('input[name=delivery]:checked');
		if(deliveryInput != null){
			this.deliveryToggle(deliveryInput);
		}

		jQuery('#checkconsentShip, #checkconsentRamasse').on('change', function(){
			if(this.checked) {
				jQuery('.activation-installation-plus').show();
				jQuery('.question-date-activation').hide();
				jQuery('.creditSection').hide();
				jQuery('.question-livraison').show();
			}else{
				jQuery('.activation-installation-plus').hide();
				jQuery('.form-date-activation').hide();
				jQuery('.explication-disponibilite').hide();
				jQuery('.question-date-activation').hide();
				jQuery('.creditSection').hide();
				jQuery('.question-livraison').hide();
				jQuery('#checkconsentDisponibilite').prop('checked', false);
				jQuery('#checkconsentActivationDate').prop('checked', false);
			}
		});

		jQuery('#checkconsentDisponibilite').on('change', function(){
			if(this.checked) {
				jQuery('.question-date-activation').show();
				jQuery('.creditSection').show();
			}else{
				jQuery('.question-date-activation').hide();
				jQuery('.creditSection').hide();
			}
		});

		jQuery('#checkconsentActivationDate').on('change', function(){
			if(this.checked) {
				jQuery('.form-date-activation').show();
				jQuery('.explication-disponibilite').show();
			}else{
				jQuery('.form-date-activation').hide();
				jQuery('.explication-disponibilite').hide();
				jQuery('.question-date-activation').hide();
				jQuery('.creditSection').hide();
				jQuery('#checkconsentDisponibilite').prop('checked', false);
			}
		});

		jQuery('body').on('click', '#checkCodeCaaQc', (event) => {
			var codeCaa = jQuery('#codeCaaQc').val();

			if(codeCaa.length < 16 || codeCaa.length > 16){
				jQuery('#codeCaaQcDiv').find('.form-error').show();
				jQuery('#codeCaaQcDiv').find('.form-succes').hide();
				LocalStorage.delete('partner_discount');
				LocalStorage.delete('partner_membership_number');
				return;
			}
			
			const data = {
				code_caa: codeCaa,
				action: 'check_membership'
			};

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/caa/controler_ajax.php',
				data,
				'json'
			).done((response) => {
				response = JSON.parse(response);
				if(response.RESPONSE.status == 'OK' && response.INFO.membershipActive == true){

					LocalStorage.set('partner_discount', '1');
					LocalStorage.set('partner_membership_number', codeCaa);

					jQuery('#codeCaaQcDiv').find('.form-succes').show();
					jQuery('#codeCaaQcDiv').find('.form-error').hide();
				}else{

					LocalStorage.delete('partner_discount');
					LocalStorage.delete('partner_membership_number');
					jQuery('#codeCaaQcDiv').find('.form-error').show();
					jQuery('#codeCaaQcDiv').find('.form-succes').hide();
				}
				
				this._$OrderService.charges.next(this._$OrderService.chargesValues);
			}).error((response) => {
				console.log(response);
			});
		});

		jQuery('body').on('change', '#caaForfaitNonEligible', (event) => {
			// selected value
			var selectedValue = event.target.value;
			// get option attributes
			var selectedOption = jQuery('#caaForfaitNonEligible option[value="' + selectedValue + '"]');

			jQuery('.caaForfaitNonEligible').find('.titleSection').hide();
			jQuery('.caaForfaitEligible').show();

			
			this.toggleInternetForCAA(selectedOption[0]);
			jQuery('.caa_quebec').show();
		});

		jQuery('body').on('change', '#checkboxCaaNonEligible_continue', (event) => {
			if(event.target.checked){
				var oldInternetPlan = LocalStorage.get('oldInternetPlan');
				this._$OrderService.saveInternetService(oldInternetPlan);
				LocalStorage.delete('partner_discount');
				LocalStorage.delete('partner_membership_number');
				jQuery('#codeCaaQcDiv').find('.form-error').hide();
				jQuery('#codeCaaQcDiv').find('.form-succes').hide();
				jQuery('#codeCaaQc').val('');
				jQuery('.title_select_container').hide()
				jQuery('.caa_quebec').show();
				CookiesService.setCookies({
					name: 'noCAAInternet',
					value: true
				});
				jQuery('.caaForfaitNonEligible').find('.titleSection').show();
				jQuery('.caaForfaitEligible').hide();
			}else{
				jQuery('.title_select_container').show();
				jQuery('#caaForfaitNonEligible').val('defaultValue');
				jQuery('.caa_quebec').hide();

				const expirationDate = new Date();
				expirationDate.setDate(expirationDate.getDate() - 10);
				CookiesService.setCookies({
					name: 'noCAAInternet',
					value: false,
					expires: expirationDate
				});
			}
		});

		//AFFICHER LE BON FORMULAIRE DE DISPONIBILITES SELON LA SELECTION DE FORFAIT ET LA QUALIFICATION
		if(this.internetPlan.cableTech == 'FTTH'){

			document.querySelector('[name="availability_template_id"]').value = 1;
			document.querySelector('.cable').remove();

			document.querySelector('.adsl2').classList.remove('hidden');
			document.querySelector('.adsl1').remove();

		}else if(this.internetPlan.cableTech == 'DSL' || this.internetPlan.technology == 'DSL'){
			// A AJOUTER UNE CONDITION SELON LA QUALIFICATION ADSL POUR AFFICHER LE BON FORMULAIRE
			document.querySelector('[name="availability_template_id"]').value = 1;
			document.querySelector('.cable').remove();

			/*if(parseInt(this.internetPlan.downloadSpeed) >= 25){
				document.querySelector('.adsl2').classList.remove('hidden');
				document.querySelector('.adsl1').remove();
			}
			else{
				document.querySelector('.adsl1').classList.remove('hidden');
				document.querySelector('.adsl2').remove();
			}*/

			document.querySelector('.adsl2').classList.remove('hidden');
			document.querySelector('.adsl1').remove();


		}else{
			document.querySelector('.cable').classList.remove('hidden');
			document.querySelector('.adsl1').remove();
			document.querySelector('.adsl2').remove();
		}
		if(internetLocal.technology == 'c-cable'){
			this.DSLInstallFee = 29.95;
		}
		else if(this.internetPlan.cableTech == 'DSL'){
			this.DSLInstallFee = 49.95;
			if(parseInt(this.internetPlan.cableTech.downloadSpeed) < 25){
				this.DSLInstallFee = 0;
			}
		}else if(this.internetPlan.cableTech == 'FTTH'){
			this.DSLInstallFee = 49.95;
		}

		if(!( this.internetPlan.cableTech == 'FTTH' && ( internetLocal.technology == 'c-cable' || internetLocal.technology == 'v-cable' ) ) ){
			
			//document.getElementById("masterEtageDiv").remove();
			
		}

		var selection =  this._$OrderService.retPhoneOrder();
        if( !(selection && !jQuery.isEmptyObject(selection)) ){

			var selection_phone = {
                type: 'none',
                planCode: 'none',
                lineCode: 'none',
                planPrice: '0.00',
            }

            this._$OrderService.savePhoneService(selection_phone);
        }


		jQuery('.full-screen-spinner-background').hide();
		this.hideSuggestions();
		let stepValidation = this._$OrderService.getStepValidation();
		/*if (!stepValidation.internet || !stepValidation.phone || (this.isTvAvailable && !stepValidation.tv)) {
			if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
		} else { */
			this.form = document.forms['personalInfo']
			this._$OrderService.setForm(this.form);
			this._$OrderService.initFormInformationPersonnel();
			this.setQualificationAddress();
			this.placeValues(this._$OrderService._retInfoClient());
			this.updateFullName();
			this.placeValues(this._$OrderService._retPersonalInfoToggle());
			this.availability = this._$OrderService._retAvailibilities();
			this.storageCheckedValues(this.availability.availability_days);
			//this.form.dateFrom.value = this.availability.dateFrom;
			this.storageCheckedValues({ 'consent': this.availability.consent });
			this.placeValues(this._$OrderService._retShippingInfo());

			// Nouveau datepicker
			jQuery('#dateActivator').val(this.availability.dateFrom);

			this.equipementsInternet = this._$OrderService._retInternetEquipement();
			this.equipementsPhone = this._$OrderService._retPhoneEquipement();

			this.creditRequest = this._$OrderService._retCreditInfo();
			this.placeValues(this.creditRequest);

			// Afficher le formulaire Equifax si le client veut/doit louer les équipements:
			jQuery('.creditSection').toggleClass('hidden', !this._$OrderService.mustRentEquipement());
			jQuery('.order-summary__navbutton').toggleClass('disabled', this._$OrderService.mustRentEquipement());
			jQuery('.eboxtv-navbutton').toggleClass('disabled', this._$OrderService.mustRentEquipement());
			if(this._$OrderService.mustRentEquipement()){
				jQuery('.btnifBuy').hide();
			}
			else{
				const resizeObserver = new ResizeObserver(entries => {
					if(!this._$OrderService.mustRentEquipement()){
						if(jQuery('window').width > 1240){
							jQuery('.btnifBuy').show();
						}
					}
				});
				
				  resizeObserver.observe(document.body);
			}
			

			// QUAND ON CHANGE UN CHAMP DANS LE FORMULAIRE ON LANCE LA VALIDATION DU INPUT MODIFIÉ
			this.form.addEventListener('change', function (evt) {
				this.optionsChange(evt.target);
				this.sendToValidator(evt.target);
			}.bind(this));


			// DUET DATEPICKER EVENT
			const datepicker = document.querySelector("duet-date-picker")

			datepicker.addEventListener("duetFocus", function(evt){
				if(!jQuery('.duet-date__dialog').hasClass('is-active')){
					document.querySelector("duet-date-picker").show()
					let question = CoreTools.translate('À partir de quelle date es-tu disponible pour l\'activation de tes services?', 'Specify from which date you are available for the activation of your services');
					this.srSpeak(question, 'polite');
				}
			}.bind(this));

			// Listen for when date is selected
			datepicker.addEventListener("duetChange", function(evt) {
				this.sendToValidator(evt.target);
			}.bind(this))

			jQuery('body').on('blur', '#dateActivator', function (evt) {
				this.sendToValidator(evt.target);
			}.bind(this));
			// DUET DATEPICKER EVENT END


			// Bouton Valider
			jQuery('#submitBtn').on('click', (evt) => {
				this.sendForm(evt);
			});

			// Bouton Continuer
			jQuery('.order-summary__navbutton').add('.eboxtv-navbutton').on('click', (evt) => {
				if (this._$OrderService.mustRentEquipement()) {
					if (!this.isCreditRequestApproved) {
						evt.preventDefault();
					}
				} else {
					this.sendForm(evt);
				}
			});

			
			// Suggestion d'adresses
			jQuery('#lastAddress, #addDelivery')
				.on('input focus', this.onAddressInput.bind(this))
				.on('keydown', (event) => { if (event.key === 'Escape') { this.hideSuggestions() } });

			jQuery('#userPhone').on('input', this.phoneValidator.bind(this));
			jQuery('#userFirstName' ).on('input', this.validateNames.bind(this));
			jQuery('#userLastName' ).on('input', this.validateNames.bind(this));
			jQuery('#userMail' ).on('input', this.emailValidator.bind(this));



			jQuery('#addDeliverySuggestions, #lastAddressSuggestions').on('click', 'li', this.onSuggestionClick.bind(this));

			jQuery('.modifyOrderLink').on('click', () => { jQuery('#internet-trigger').trigger('click') });


			/*document.querySelector('#datetimepicker').addEventListener('blur', function (evt) {
				this.sendToValidator(evt.target);
			}.bind(this));*/


			// Vérifier le input haveInternet
			if (document.querySelectorAll('input[name="haveInternet"]:checked').length == 1) {
				this.optionsChange(document.querySelector('input[name="haveInternet"]:checked'));
			}

			jQuery('.birthDate-select').removeClass('error');
		//}
	}

	setQualificationAddress() {
		this.qualificationAddress = this._$OrderService._retQualificationAddresse();

		let dn = this.qualificationAddress.dn != undefined && this.qualificationAddress.dn.toString().trim() ? this.qualificationAddress.dn.toString().trim() + ' ' : '';
		let app = this.qualificationAddress.app ? this.qualificationAddress.app.toLowerCase() + ' ' : '';
		let stc = this.qualificationAddress.stc ? this.qualificationAddress.stc.toLowerCase() : '';
		let sdc = this.qualificationAddress.sdc ? ' ' + this.qualificationAddress.sdc : '';
		let sn = this.qualificationAddress.sn ? this.qualificationAddress.sn.charAt(0) + this.qualificationAddress.sn.slice(1).toLowerCase() : '';
		let mn = this.qualificationAddress.mn ? this.qualificationAddress.mn.charAt(0) + this.qualificationAddress.mn.slice(1).toLowerCase() + ', ' : '';
		let pc = this.qualificationAddress.pc ? this.qualificationAddress.pc : '';
		let zip = this.qualificationAddress.zip ? this.qualificationAddress.zip.slice(0, 3) + ' ' + this.qualificationAddress.zip.slice(3) : '';

		let street = (this.qualificationAddress.stc === 'ST' ? sn + ' ' + stc : stc + ' ' + sn) + sdc;

		jQuery('#addressStreet').text(dn + app + street);
		jQuery('#addressCityProvince').text(mn + pc);
		jQuery('#addressPostalCode').text(zip);
	}

	parseCookies() {
		var cookiesArray = document.cookie.split('; ');
		var output = {};
		for (var i = 0; i < cookiesArray.length; i++) {
			var currentCookie = cookiesArray[i];
			var _a = currentCookie.split('='),
				key = _a[0],
				value = _a[1];
			value = decodeURI(value);
			output[key] = value;
		}
		return output;
	}

	/**
	 * DETECTE LE TYPE DE INPUT MODIFIÉ ET LANCE LA VALIDATION DU CHAMP
	 * @param {INPUT} input QUI A ETE MODIFIÉ
	 */
	sendToValidator(input) {
		if (input) {
			if (input.length > 1) {
				input.tagName !== 'SELECT' ? this.validateRadio(input[0]) : this.validateSelect(input);
			}
			else {
				switch (input.type) {
					case 'text':
						this.validateText(input);
						break;
					case 'checkbox':
						this.validateCheckbox(input);
						break;
					case 'radio':
						//NE DEVRAIT JAMAIS SE RENDRE ICI
						this.validateRadio(input);
						break;
					default:
						//NE DEVRAIT JAMAIS SE RENDRE ICI
						this.validateSelect(input);
						break;
				}
			}
		}
	}

	/**
	 * VALIDE LES INPUT DE TYPE TEXTE ET LES ENVOIE A DES VALIDATEURS DIFFERENTS SI CERTAINS SPECIFIQUES SONT DETECTÉS
	 * @param {input} input
	 */
	validateText(input) {
		this._$OrderService._infoFormDataBuilder(input);
		if (input.required) {
			if (input.name === 'userPhone') {
				this.phoneValidator();
			} else if (input.name === 'userMail') {
				this.emailValidator(input)
			}else if(input.classList.contains('duet-date__input')){
				const inputWrapper = input.parentElement;
				const divWrapper = inputWrapper.parentElement;
				const duetDatePicker = divWrapper.parentElement;
				const formGroup = duetDatePicker.parentElement;
				// Date picker
				if(input.value.trim() === ''){
					this.setError(formGroup);
				}else{
					this.removeError(formGroup);
				}
			
			}else {
				// SI CE N EST PAS UN INPUT SPECIFIQUE ON AFFICHE OU RETIRE LE MESSAGE D ERREUR SI LE CHAMP EST VIDE
				const parent = input.parentElement;
				input.value.trim() === '' ? this.setError(parent) : this.removeError(parent);
				if (input.name === 'userFirstName' || input.name === 'userLastName') {
					this.validateNames(input);
					this.updateFullName();
				}
			}
		}
		// VALIDATION DES FRIEND CODE ICI, ENCORE UNE FOIS NE DEVRAIT PAS ETRE ICI (KEY UP EVENT OU QUELQUE CHOSE DU GENRE)
		else if (input.name === 'friendCode') {
			if (input.value.trim() === '') {
				parent = input.parentElement;
				this.removeError(parent);
			}
			else {
				this.friendCodeValidator(input)
			}
		}
		else if (input.name === 'promoCode') {
			if (input.value.trim() === '') {
				parent = input.parentElement;
				this.removeError(parent);
			}
			else {
				this.promoCodeValidator(input)
			}
		}
	}

	updateFullName() {
		let fullName = document.getElementById('userFirstName').value + ' ' + document.getElementById('userLastName').value;
		document.getElementById('showUserFullName').innerText = fullName;
	}

	/**
	 * RECOIS UN INPUT DE TYPE CHECKBOX POUR LE VALIDER
	 * @param {INPUT} input INPUT A VALIDER
	 */
	validateCheckbox(input) {
		this._$OrderService._infoFormDataBuilder(input); // ENREGISTRE LES DONNEES DANS LE LOCALSTORAGE
		if (input.required) {
			const parent = input.parentElement;
			input.checked ? this.removeError(parent) : this.setError(parent);
		}
		this.validateAvailabilities();
	}

	/**
	 * RECOIS UN INPUT DE TYPE SELECT RADIO
	 * @param {input} input INPUT A VALIDER
	 */
	validateRadio(input) {
		this._$OrderService._infoFormDataBuilder(input); // ENREGISTRE LES DONNEES DANS LE LOCALSTORAGE
		let inputs = document.forms['personalInfo'][input.name];
		let radios = this.toArray(document.forms['personalInfo'][input.name])
		radios.forEach(input => {
			input.checked ? input.parentElement.classList.add('radio--red') : input.parentElement.classList.remove('radio--red')
		});

		if (inputs[0].required) {
			let parent = inputs[0].parentElement.parentElement;
			inputs.value.trim() !== '' ? this.removeError(parent) : this.setError(parent);
		}
	}

	/**
	 * VALIDER LES CHAMPS SELECT
	 * @param {input} input CHAMP INPUT RECU
	 */
	validateSelect(input) {
		this._$OrderService._infoFormDataBuilder(input);
		if (input.required) {
			//VALIDER LA DATE DE FETE
			if (input.name.includes('Birth')) {
				this.birthDayValidator(input);
			}
			else {
				const parent = input.parentElement;
				if(input.required && input.value === 'defaultValue'){
					 this.setError(parent);
				}else{
					this.removeError(parent);
				}
			}
		}
	}

	/**
	 * VALIDATEUR CUSTOM POUR LE NUMERO DE TELEPHONE
	 * REMPLACE LA VALEUR DU CHAMPS POUR UN FORMAT SPECIFIQUE
	 * @param {input} input INPUT A VALIDER
	 */
	phoneValidator() {

		clearTimeout(this._inputDebounce);
		let input = document.getElementById('userPhone');


		var phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
		input.value = input.value.replace(/[^0-9 ()-]/, '');
		const parent = input.parentElement;
		if (input.value.trim() !== '' && phoneRegex.test(input.value)) {
			input.value = input.value.replace(phoneRegex, "($1) $2-$3");
			this.removeError(parent);
		} else {
			this.setError(parent);
		}

	}

	/**
	 * VALIDATEUR CUSTOM POUR LES COURRIEL
	 * @param {input} input INPUT A VALIDER
	 */
	emailValidator() {
		let input = document.getElementById('userMail');
		const parent = input.parentElement;
		const re = /\S+@\S+\.\S+/;
		input.value = input.value.replace(/[^A-Za-z0-9.@+_-]/, '');
		if (input.value.trim() !== '' && re.test(input.value)) {
			this.removeError(parent);
		}
		else {
			this.setError(parent);
		}
	}

	/**
	 * VALIDATEUR CUSTOM POUR LA DATE DE FETE
	 * @param {input} input INPUT A VALIDER
	 */
	birthDayValidator(input) {
		const parent = input.parentElement;
		const day = document.forms['personalInfo']['BirthDay'];
		const month = document.forms['personalInfo']['BirthMonth'];
		const year = document.forms['personalInfo']['BirthYear'];

		if (day.value == 'defaultValue' || month.value == 'defaultValue' || year.value == 'defaultValue') {
			parent.classList.add('error');
			this.setError(parent);
		} else {
			this.removeError(parent);
			let is18orMore = this.isDate18orMoreYearsOld(parseInt(year.value), parseInt(month.value), parseInt(day.value));
			if (is18orMore) {
				parent.classList.remove('error');
				jQuery('#isTooYoungText').hide();
			} else {
				this.$valid = false;
				this.lastErrorParentId = document.getElementById('isTooYoungText');
				jQuery('#isTooYoungText').show();
			}
		}
	}

	isDate18orMoreYearsOld(year, month, day) {
		return new Date(year + 18, month - 1, day) <= new Date();
	}

	// CODE QUI CACHE OU AFFICHE LE DIV DU CODE PROMO
	isCodePromoReady(){
		var internetLocal = LocalStorage.get('internetPlan');
		var validForfait = false;

		this.arrayCodePromo.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		/*this.arrayCodePromoFIB500.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		})*/

		this.arrayCodePromoBF24.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		this.arrayCodePromoBW24.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		this.arrayCodePromoBENNYQC.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		this.arrayCodePromoBENNYON.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		this.arrayCodePromoPERKO5.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});
		this.arrayCodePromoVENNGO5.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});
		this.arrayCodePromoCOLOC25.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});
		this.arrayCodePromoCAMPUS25.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});
		// CODE PROMO COMMICON
		this.arrayCodePromoCCMTL25.forEach(function(item){
			if(item.key == internetLocal.forfait){
				validForfait = true;
			}
		});

		if(!validForfait){
			var element = document.getElementsByClassName("promoCode")[0];
			if(document.getElementsByClassName("promoCode").length > 0){
				//element.remove();
			}
		}else{
			var element = document.getElementById("promoCode");
			var codePromo = LocalStorage.get('codePromo');
			if(codePromo){
				element.value  = codePromo.codePromo;
			}
		}

		// SUPPRESSION DU CODE PROMO APRÈS LE 1 JANVIER 2023
		/*var ajout5hours = 5 * 60 * 60 * 1000;
		var datum = new Date(Date.UTC('2023','00','01','00','00','00')).getTime() + ajout5hours;
		var datTEST = new Date(Date.UTC('2022','10','30','9','20','00')).getTime() + ajout5hours;
		var dateNow = Date.now();

		if(dateNow > datum){
			var element = document.getElementsByClassName("promoCode")[0];
			if(document.getElementsByClassName("promoCode").length > 0){
				element.remove();
			}
			LocalStorage.delete('codePromo');
		}else{
			//console.log('pass');
		}*/


	}

	verifyAutomaticPromo(){
		// Si le forfait internet est fibre et que la date est entre le 27 janvier 2025 à 00:00 et le 2 février 2025 à 11h59
		var now = new Date();
		var start = new Date(now.getFullYear(), 0, 27, 0, 0, 0);
		var end = new Date(now.getFullYear(), 1, 2, 11, 59, 0);
		var isBetween = now >= start && now <= end;
		//isBetween = true;
		if(isBetween){
			var internetLocal = LocalStorage.get('internetPlan');
			if(internetLocal.cableTech == 'FTTH'){
				LocalStorage.set('promoFibre', JSON.stringify({codePromo:'PRM00407', amount:"0", duration:1}));
			}else{
				LocalStorage.delete('promoFibre');
			}
		}else{
			LocalStorage.delete('promoFibre');
		}
	}

	cravePromo(){
		
		const bundle = LocalStorage.get('selectedBundles');
		const channels = LocalStorage.get('selectedChannels');
		var returnValue = {};
		var isForfaitDeBase = false;
		var codePromo = '';
		var promoPrice = 0;

		var isEligible = false;
		channels['a-la-carte'].forEach(premium => {
			if(premium == '10762'){
				isEligible = true;
			}
		});

		var is5Channel = false;
		var channelLength = 0;
		const chaines = LocalStorage.get('selectedChannels');
		for (const [key, value] of Object.entries(chaines)) {
			if(key == 'a-la-carte') {
				channelLength = value.length;
				if(channelLength && channelLength >= 5){
					is5Channel = true;
				}
			}
		}

		if(!is5Channel){
			codePromo = 'PRM00222';
			promoPrice = 22;
		}else{
			codePromo = 'PRM00223';
			promoPrice = 12;
		}
		var now = new Date();
		var start = new Date(2025, 6, 7, 0, 0, 0);
		var end = new Date(2025, 8, 28, 23, 59, 0);
		var isBetween = now >= start && now <= end;
		if (!isBetween) {
			isEligible = false;
		}
		console.log(isEligible);
		if(isEligible){
			LocalStorage.set('promoCrave', JSON.stringify({rebateCode:codePromo, amount:promoPrice, duration: "3"}));
			returnValue = {code:codePromo, amount:promoPrice};
		}else{
			LocalStorage.delete('promoCrave');
		}

		this._$OrderService.chargesValues.promoCrave = returnValue;
		this._$OrderService.charges.next(this._$OrderService.chargesValues);
		

	}

	/**
	 * VALIDE SI LE PROMO CODE EST VALIDE OU NON
	 * @param {Input} input INPUT A VALIDER
	 */
	promoCodeValidator(input) {

		var validPromoCodeArray = [];

		// if date now is between July 3 and July 30 (include) in javascript
		/*var now = new Date();
		var start = new Date(now.getFullYear(), 6, 3);
		var end = new Date(now.getFullYear(), 6, 30);
		var isBetween = now >= start && now <= end;
		if(isBetween){
			validPromoCodeArray.push({promoCode:'EBOXETE0723',promoRebateCode:'PRM00195'});
		}

		// if date now is between July 14 and August 14 (include) in javascript
		start = new Date(now.getFullYear(), 6, 14);
		end = new Date(now.getFullYear(), 7, 14);
		isBetween = now >= start && now <= end;
		if(isBetween){
			validPromoCodeArray.push({promoCode:'EBOXMCC23',promoRebateCode:'PRM00196'});
		}

		// if date now is between August 14 and September 30 (include) in javascript
		start = new Date(now.getFullYear(), 7, 14);
		end = new Date(now.getFullYear(), 8, 30);
		isBetween = now >= start && now <= end;
		if(isBetween){
			validPromoCodeArray.push({promoCode:'EBOX50BS23',promoRebateCode:'PRM00197'});
		}*/

		// if date now is between August 14 and September 30 (include) in javascript
		/*start = new Date(now.getFullYear(), 5, 21);
		end = new Date(now.getFullYear(), 5, 21);
		isBetween = now >= start && now <= end;
		if(isBetween){
			//console.log('isBetween June 10 and June 30');
			//validPromoCodeArray.push({promoCode:'EBOXTEST',promoRebateCode:'PRM00197'});
		}*/

		//validPromoCodeArray.push({promoCode:'EBOXETE0723',promoRebateCode:'PRM00195'});
		//validPromoCodeArray.push({promoCode:'EBOXMCC23',promoRebateCode:'PRM00196'});
		//validPromoCodeArray.push({promoCode:'EBOX50BS23',promoRebateCode:'PRM00197'});


		// CODE PROMO COMMICON - 4 juillet 2025 au 13 juillet 2025
		var now = new Date();
		var start = new Date(now.getFullYear(), 6, 4);
		var end = new Date(now.getFullYear(), 6, 13);
		var isBetween = now >= start && now <= end;
		//var isBetween = true;
		if(isBetween){
			validPromoCodeArray.push({promoCode:'CCMTL25',promoRebateCode:'PRM00400'});
		}


		// Vérification des trois plages de dates pour le code promo COLOC25
		var now = new Date();

		// Première plage: 20 mai 00:00 au 25 mai 23:59
		var start1 = new Date(2025, 4, 20, 0, 0, 0);
		var end1 = new Date(2025, 4, 25, 23, 59, 59);

		// Deuxième plage: 3 juin 00:00 au 22 juin 23:59
		var start2 = new Date(2025, 5, 3, 0, 0, 0);
		var end2 = new Date(2025, 5, 22, 23, 59, 59);

		var isInRange1 = now >= start1 && now <= end1;
		var isInRange2 = now >= start2 && now <= end2;

		if(isInRange1 || isInRange2){
			validPromoCodeArray.push({promoCode:'COLOC25',promoRebateCode:'PRM00419'});
		}

        // CAMPUS25 — Actif du 2 septembre 00:00 au 14 septembre 23:59 (2025)
        var startCampus = new Date(2025, 8, 2, 0, 0, 0);
        var endCampus = new Date(2025, 8, 14, 23, 59, 59);
        var isCampus = now >= startCampus && now <= endCampus;

		// Laisse isCampus à true pour tester le code promo (PAT à enlever pour Mise en production)
        //var isCampus = true;
        if(isCampus){
            validPromoCodeArray.push({promoCode:'CAMPUS25',promoRebateCode:'PRM00521'});
        }

		// if date now is between November 29 and Decembre 2 (include) in javascript
		var now = new Date();
		var start = new Date(now.getFullYear(), 10, 29);
		var end = new Date(now.getFullYear(), 11, 3);
		var isBetween = now >= start && now <= end;
		if(isBetween){
			//validPromoCodeArray.push({promoCode:'EBOXBF24',promoRebateCode:'PRM00404'});
		}

		// if date now is between Decembre 24 and Decembre 30 (include) in javascript
		var now = new Date();
		var start = new Date(now.getFullYear(), 11, 23);
		var end = new Date(now.getFullYear(), 11, 30);
		var isBetween = now >= start && now <= end;
		if(isBetween){
			//validPromoCodeArray.push({promoCode:'EBOXBW2024',promoRebateCode:'PRM00406'});
		}
		//validPromoCodeArray.push({promoCode:'EBOXBW2024',promoRebateCode:'PRM00406'});
		//validPromoCodeArray.push({promoCode:'FIB500',promoRebateCode:'PRM00404'});
		//validPromoCodeArray.push({promoCode:'FIB1000',promoRebateCode:'PRM00405'});
		validPromoCodeArray.push({promoCode:'PERKO5',promoRebateCode:'PRM00403'});
		validPromoCodeArray.push({promoCode:'VENNGO5',promoRebateCode:'PRM00520'});

		var agentName = CookiesService.getCookies('agent_name');
		if(agentName){
			// QUEBEC - 500
			// BENNYQC devient CHEPROMOQC
			validPromoCodeArray.push({promoCode:'CHEPROMOQC',promoRebateCode:'000000'});
			// QUEBEC - 1000
			// BENNYON devient CHEPROMOON
			validPromoCodeArray.push({promoCode:'CHEPROMOON',promoRebateCode:'000000'});
		}

		// Code promo SNH25 - Ajout du code promo 
		var now = new Date();
		var start = new Date(2025, 2, 13);
		var end = new Date(2025, 2, 31);
		var isBetween = now >= start && now <= end;
		if(isBetween){
			validPromoCodeArray.push({promoCode:'SNH25',promoRebateCode:'PRM00412'});
		}


		// GET DATE TIME NOW in javascript in console log
		var dateNow = new Date();
		var dateNowUTC = Date.UTC(dateNow.getFullYear(),dateNow.getMonth(),dateNow.getDate(),dateNow.getHours(),dateNow.getMinutes(),dateNow.getSeconds());
		//console.log(dateNowUTC);

		var internetLocal = LocalStorage.get('internetPlan');
		if(internetLocal.forfait){
			
			var promoCodeAmount = 0;
			var promoRebateCode = '';
			var promoCodeDuration = 1;
			var validPromoCode = false;
			var validForfait = false;
			var returnValue = {};
			var promoCodeMessage = "";

			validPromoCodeArray.forEach(function(item){
				if(item.promoCode == input.value.toUpperCase()){
					validPromoCode = true;
					promoRebateCode = item.promoRebateCode;
				}
			});

			this.arrayCodePromo.forEach(function(item){
				if(item.key == internetLocal.forfait){
					validForfait = true;
					promoCodeAmount = item.amount;
					promoCodeDuration = item.duration;
				}
			});

			/*if(input.value.toUpperCase() == 'FIB500'){
				this.arrayCodePromoFIB500.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}

			if(input.value.toUpperCase() == 'FIB1000'){
				this.arrayCodePromoFIB1000.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}*/

			if(input.value.toUpperCase() == 'COLOC25'){
				this.arrayCodePromoCOLOC25.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
						if(item.prmCode){
							promoRebateCode = item.prmCode;
						}
					}
				});
			}

			if(input.value.toUpperCase() == 'CAMPUS25'){
				this.arrayCodePromoCAMPUS25.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
						if(item.prmCode){
							promoRebateCode = item.prmCode;
						}
					}
				});
			}

			// CODE PROMO COMMICON
			if(input.value.toUpperCase() == 'CCMTL25'){
				this.arrayCodePromoCCMTL25.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
						if(item.prmCode){
							promoRebateCode = item.prmCode;
						}
					}
				});
			}

			// BENNYQC devient CHEPROMOQC
			if(input.value.toUpperCase() == 'CHEPROMOQC'){
				this.arrayCodePromoBENNYQC.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
						if(item.prmCode){
							promoRebateCode = item.prmCode;
						}
					}
				});
			}

			// BENNYON devient CHEPROMOON
			if(input.value.toUpperCase() == 'CHEPROMOON'){
				this.arrayCodePromoBENNYON.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
						if(item.prmCode){
							promoRebateCode = item.prmCode;
						}
					}
				});
			}

			if(input.value.toUpperCase() == 'EBOXBF24'){
				this.arrayCodePromoBF24.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}

			// Code promo SNH25 - Ajout du code promo - Si le forfait est fibre, le code promo est valide.
			if(input.value.toUpperCase() == 'SNH25'){
				if(internetLocal.cableTech == 'FTTH'){
					validForfait = true;
					promoCodeAmount = '0';
					promoCodeDuration = '1';
				}
			}

			if(input.value.toUpperCase() == 'EBOXBW2024'){
				this.arrayCodePromoBW24.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}

			if(input.value.toUpperCase() == 'PERKO5'){
				this.arrayCodePromoPERKO5.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}

			if(input.value.toUpperCase() == 'VENNGO5'){
				this.arrayCodePromoVENNGO5.forEach(function(item){
					if(item.key == internetLocal.forfait){
						validForfait = true;
						promoCodeAmount = item.amount;
						promoCodeDuration = item.duration;
					}
				});
			}

			//console.log(validForfait);
			//console.log(validPromoCode);
			// S'assurer qu'il n'y a pas plusieurs code promo
			if(LocalStorage.get('codePromo') || LocalStorage.get('partner_discount')){
				this.nbre_code_promo++;
			}

			if(LocalStorage.get('codePromo')){
				LocalStorage.delete('codePromo');
			}

			// S'assurer que CAA est caché
			jQuery('.caa_quebec').hide();
			jQuery('.caaForfaitEligible').hide();
			jQuery('.caaForfaitNonEligible').hide();
			

			if(validForfait && validPromoCode){

				if(LocalStorage.get('partner_discount')){
					LocalStorage.delete('partner_discount');
				}
				if(LocalStorage.get('partner_membership_number')){
					LocalStorage.delete('partner_membership_number');
				}

				LocalStorage.set('codePromo', JSON.stringify({codePromo:input.value, amount:promoCodeAmount, rebateCode:promoRebateCode, duration:promoCodeDuration}));
				returnValue = {codePromo:input.value, amount:promoCodeAmount};
				promoCodeMessage = CoreTools.translate('Ton code promo est valide.', 'Your promo code is valid.');
				if(this.nbre_code_promo >= 1){
					this.nbre_code_promo++;
					jQuery('#messagebox-promocode-warning').show();
				}
			}else{

				var dateNow = new Date();
				var date_caa = new Date(2024, 7, 8, 1, 0, 0); // 8 août 2024 01h00
				var isDev = window.location.href.includes('beta3');
				if((input.value.toUpperCase() == 'RABAISCAA' || input.value.toUpperCase() == 'CAAREBATE') && (date_caa < dateNow || isDev)){
					if(this.nbre_code_promo >= 1){
						this.nbre_code_promo++;
						jQuery('#messagebox-promocode-warning').show();
					}
					// AJAX
					//console.log('AJAX');
					this.caa_validate_forfait(input.value).then(resp => {
						//console.log(resp);
						if(resp.status == 'success'){
							// Si le forfait est valide
							jQuery('.caaForfaitEligible').show();
							jQuery('.caa_quebec').show();
							jQuery('.caaForfaitNonEligible').hide();
						}else{
							// Si le forfait n'est pas valide
							jQuery('.caaForfaitEligible').hide();
							jQuery('.caa_quebec').hide();
							jQuery('.caaForfaitNonEligible').show();

							// Prepare select

							var oldInternetPlan = LocalStorage.get('internetPlan');
							//console.log(oldInternetPlan);
							LocalStorage.set('oldInternetPlan', JSON.stringify(oldInternetPlan));

							resp.forfait.forEach(function(item){
								var option = document.createElement('option');
								option.value = item.ID;
								option.text = item.attributes['data-title'];
								option.setAttribute('data-sku', item.attributes['data-sku']);
								option.setAttribute('data-price', item.attributes['data-price']);
								option.setAttribute('data-cabletech', item.attributes['data-cabletech']);
								option.setAttribute('data-fournisseur', item.attributes['data-fournisseur']);
								option.setAttribute('data-title', item.attributes['data-title']);
								option.setAttribute('data-prm-modem', item.attributes['data-prm-modem']);
								option.setAttribute('data-prm-routeur', item.attributes['data-prm-routeur']);
								option.setAttribute('data-vitesse', item.attributes['data-vitesse']);

								jQuery('#caaForfaitNonEligible').append(option);
							});
							
						}
					});
				}else{
					if(input.value.toUpperCase() == 'PERKO5'){
						promoCodeMessage = CoreTools.translate("Ton code promo est invalide. Tu dois sélectionner la fibre 500 pour l’utiliser.", 'Your promo code is invalid. You must select fiber 500 to use it.');
					}else{
						promoCodeMessage = CoreTools.translate("Ton code promo est invalide.", 'Your promo code is invalid.');
					}
				}
				
			}

			var messageBox = document.getElementById('messagebox-promocode');
			messageBox.textContent = promoCodeMessage;

			this._$OrderService.chargesValues.promoCode = returnValue;
			this._$OrderService.charges.next(this._$OrderService.chargesValues);

		}
		
	}

	toggleInternetForCAA(target){
		this.selection = this._$OrderService.retInternetPlan();
        if(target.getAttribute('data-sku') !== 'none'){
            this.selection.forfait = target.getAttribute('data-sku');
            this.selection.modem = 'rent';
            this.selection.router = 'rent';
            this.selection.downloadSpeed = target.getAttribute('data-vitesse');
            this.selection.planPrice = target.getAttribute('data-price');
            this.selection.technology = target.getAttribute('data-fournisseur');
            this.selection.title = target.getAttribute('data-title');
            this.selection.cableTech = target.getAttribute('data-cabletech');
            this.selection.prmModem = target.getAttribute('data-prm-modem');
            this.selection.prmRouter = target.getAttribute('data-prm-routeur');
        }else{
            this.selection = {};
        }
         
        this._$OrderService.saveInternetService(this.selection);
        this._$OrderService.saveEquipementInternet([]);
    }

	caa_validate_forfait(code_caa) {
		return new Promise((resolve, reject) => {
			var internetLocal = LocalStorage.get('internetPlan');
			const data = {
				code_caa: code_caa,
				code_forfait: internetLocal.forfait,
				tech: internetLocal.technology,
				cableTech: internetLocal.cableTech,
				action: 'caa_validate_forfait',
				langue: CookiesService.getCookies('wp-wpml_current_language')
			};

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/caa/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}

	/**
	 * VALIDE SI LE CODE D AMI EST VALIDE OU NON
	 * @param {Input} input INPUT A VALIDER
	 */
	friendCodeValidator(input) {
		this.validateCodeRequest(input)
			.then(resp => {
				const parent = input.parentElement;
				resp == 1 || resp == '1' ? this.removeError(parent) : this.setError(parent);
				resp == 1 || resp == '1' ? parent.classList.add('succes') : parent.classList.remove('succes');

			}, err => {
				console.error(err);
			});
	}

	/**
	 * REQUETE AJAX POUR VALIDER LE CODE DE REFERENCEMENT
	 * @param {INPUT} input
	 * @returns
	 */
	validateCodeRequest(input) {
		return new Promise((resolve, reject) => {
			const data = {
				code: input.value
			}

			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validate_clientCode' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}

	validateAvailabilities() {
		this.availability = this._$OrderService._retAvailibilities();
		jQuery('#availabilities').toggleClass('error', this.availability.isEmpty);
		if (this.availability.isEmpty) {
			this.$valid = false;
		}
	}

	validateNames(input){

		if(!input.name){
			input = input.target;
		}
			input.value = input.value.replace(/[^A-Za-zàèìòùÀÈÌÒÙáéíóúýÁÉÍÓÚÝâêîôûÂÊÎÔÛãñõÃÑÕäëïöüÿÄËÏÖÜŸçÇßØøÅåÆæœ-\s]/gi, "");

	}

	/**
	 * AJOUTE L ERREUR AU CHAMP INPUT
	 * @param {parent} parent PARENT DU INPUT MODIFIER
	 */
	setError(parent) {
		parent.classList.add('error');
		this.$valid = false;
		this.lastErrorParentId = parent;
	}

	/**
	 * RETIRE L ERREUR AU CHAMP INPUT
	 * @param {parent} parent PARENT DU INPUT MODIFIER
	 */
	removeError(parent) {
		parent.classList.remove('error');
	}

	/**
	 * ENVOIE LE FORMULAIRE SI AUCUNE ERREUR N'A ETE DETECTEE
	 */
	sendForm(event) {
		jQuery('.full-screen-spinner-background').show();

		event.preventDefault();
		this.validateForm();
		jQuery('#resultDescription div').addClass('hidden');
		this._$OrderService._personalInfoFormStorage(document.forms['personalInfo']);
		if (this.$valid) {
			let waitLabel = CoreTools.translate('Donne-nous un moment pour vérifier ta commande. On te revient avec les prochaines étapes le plus vite possible!', 'Give us a moment while we verify your order and get you through the next steps.');
			this.srSpeak(waitLabel, 'polite');
			this._$OrderService.sendForm().then(resp => {
				jQuery('.full-screen-spinner-background').hide();
				if (resp && resp.RESPONSE.status === 'OK') {
					// Si credit_response est null, c'est qu'on n'a pas fait de demande equifax (achat seulement);
					let responseDivID = resp.INFO.credit_response ? resp.INFO.credit_response.action : '';
					let depositAmount = resp.INFO.credit_response ? resp.INFO.credit_response.total_deposit_amount.toFixed(2) || 0 : 0;

					this.isCreditRequestDone = true;
					this.isCreditRequestApproved = responseDivID !== 'MUST_BUY_EQUIPMENTS';

					jQuery('.order-summary__navbutton').toggleClass('disabled', !this.isCreditRequestApproved);
					jQuery('.eboxtv-navbutton').toggleClass('disabled', !this.isCreditRequestApproved);


					
					this._$OrderService.setStepValidation('personalInfo', this.isCreditRequestApproved);

					this._$OrderService.setCreditRequest(resp.INFO);
					jQuery('#depositAmount').text(depositAmount);
					jQuery('#' + responseDivID).removeClass('hidden');
					this.srSpeak(jQuery('#' + responseDivID + ' h4').text(), 'polite');
					if(responseDivID !== ''){
						jQuery('#' + responseDivID + ' .order-tabs__footer').removeClass('hidden');
						
					}
					jQuery('#submitBtn').addClass('hidden');
					if (event.currentTarget.href) {
						window.location.href = event.currentTarget.href;
					}
				} else {
					jQuery('#technicalError').removeClass('hidden');
					if(resp.RESPONSE.statuscode){
						let span = document.createElement('SPAN');
						span.textContent = ' ( ' + resp.RESPONSE.statuscode + ' )';
						document.querySelectorAll('#technicalError p')[0].appendChild(span);
					}
				}
			});
		} else {
			jQuery('.full-screen-spinner-background').hide();
			jQuery('#invalidForm').removeClass('hidden');


			let waitLabel = CoreTools.translate('Tu dois remplir tous les champs du formulaire.', 'You must fill in all the fields of the form');
			this.srSpeak(waitLabel, 'polite');

		}
	}

	onSuggestionClick(event) {
		const { target } = event;
		this.hideSuggestions();
		let addressId = target.getAttribute('from');
		let newAddress = this.temporaryAddressSuggestions.find(ad => ad.id == target.id);

		jQuery('#' + addressId).val(target.innerHTML);

		if (addressId === 'lastAddress') {
			this.validatedLastAddress = newAddress;
			this._$OrderService._set_addresses(newAddress);
		} else {
			this.validatedDeliveryAddress = newAddress;
			this._$OrderService._set_addressesShipping(newAddress);
		}
	}

	onAddressInput(event) {
		const { target } = event;

		clearTimeout(this._inputDebounce);

		if (target.value.trim().length) {
			this._inputDebounce = setTimeout(this.searchAddress.bind(this, target), 500);
		} else {
			this.hideSuggestions();
			target.name === 'lastAddress' ? this._$OrderService._set_addresses({}) : this._$OrderService._set_addressesShipping({});
		}
	}

	onAddressBlur(event) {
		const { target } = event;
		this.hideSuggestions();
	}

	searchAddress(target) {
		let suggestionsDivID = '#' + target.name + 'Suggestions';
		jQuery(suggestionsDivID).empty().html('<div class="lds-ellipsis"><div></div><div></div><div></div><div></div></div>').show();
		this.callAddressAPI(target.value.trim())
			.then(resp => {

				// Parse 
				resp = Object.values(resp);
				resp.forEach(item => {
					item.id = Tools.generateRdmString();
				});

				this.temporaryAddressSuggestions = resp;
				let newHtml = '';
				let resultsLabel = CoreTools.translate('Résultats: ', 'Results: ');
				let adresseResultLabelSR = "";
				if (resp.length) {
					let lastResult = ''
					newHtml = '<ul>';
					resp.forEach(adr => {
						let address = this.concatAddressFields(adr);
						adresseResultLabelSR += address + ', ';
						if (address !== lastResult) {
							newHtml += '<li id="' + adr.id + '" from="' + target.name + '" class="suggestion">' + address + '</li>';
						}
						lastResult = address;
					});
					newHtml += '</ul>';
				} else {
					newHtml = '<p class="text">' + CoreTools.translate('Aucun résultat pour: ', 'No result for: ') + target.value.trim() + '</p>'
				}
				if(adresseResultLabelSR !== ""){
					adresseResultLabelSR = adresseResultLabelSR.slice(0, -2);
					adresseResultLabelSR = resultsLabel + adresseResultLabelSR;
					this.srSpeak(adresseResultLabelSR, 'polite');
				}else{
					this.srSpeak(CoreTools.translate('Aucun résultat pour: ', 'No result for: ') + target.value.trim(), 'polite');
				}


				jQuery(suggestionsDivID).empty().html(newHtml);


			}, err => {
				console.error(err);
			});
	}

	concatAddressFields(address) {
		let dn = address.dn.toString().trim() ? address.dn.toString().trim() + ', ' : '';
		let stc = address.stc ? address.stc + ' ' : '';
		let sn = address.sn ? address.sn + ' ' : '';
		let mn = address.mn ? address.mn + ', ' : '';
		let pc = address.pc ? address.pc + ', ' : '';
		let zip = address.zip ? address.zip : '';

		return dn + stc + sn + mn + pc + zip;
	}

	hideSuggestions() {
		jQuery('#addDeliverySuggestions, #lastAddressSuggestions').hide();
	}

	callAddressAPI(search) {
		return new Promise((resolve, reject) => {
			const data = {
				filter: this.isLastAddressCanadian ? "canada" : "",
				search,
				checkprov: "QC",
				lng: Tools.lang,
				action: 'searchAddressAC'
			};
			jQuery.post(
				'/wp-content/themes/ebox2018/modules/components/validateur' + '/controler_ajax.php',
				data, resolve, 'json'
			).error(reject);
		});
	}

	/**
	 * LOOP DANS LES VALEURS RETOURNÉES PAR LE LOCAL STORAGE ET PLACE LES VALUE DANS LE BON CHAMP
	 * @param {Object} obj
	 */
	placeValues(obj) {
		for (const [key, value] of Object.entries(obj)) {
			if (key === 'addDelivery' || key === 'lastAddress') {
				const adr = jQuery.isEmptyObject(value) ? '' : this.concatAddressFields(value);
				this.form[key].value = adr;
			} else {
				if(key === 'partner_discount' || key === 'partner_membership_number'){
					//this.form[key].value = value;
					continue;
				}else{
					this.form[key].value = value;
				}
				this.optionsChange(this.form[key]);
			}
		}
	}

	/**
	 * LOOP DANS DES VALEURS CHECKBOX POUR PLACER LES VALEURS : (PEUT SUREMENT ETRE FUSIONNÉ AVEC LA FUNCTION PRECEDENTE)
	 * @param {OBJECT} obj
	 */
	storageCheckedValues(obj) {
		for (const [key, value] of Object.entries(obj)) {
			if (this.form[key]) {
				this.form[key].checked = value;
			}
		}
	}

	/**
	 * RECOIS UN INPUT POUR MODIFIER LES CHAMPS REQUIS ET CEUX A CACHER
	 * @param {INPUT} input INPUT A VALIDER
	 */
	optionsChange(input) {
		if (input.length > 1) {
			if (input.tagName == 'SELECT') {
				if (input.name.includes('Birth')) {
					this.birthDayValidator(input);
				}
			}
			else {
				let radios = this.toArray(document.forms['personalInfo'][input[0].name])
				radios.forEach(radio => {
					radio.checked ? input = radio : '';
				});
			}
		}
		switch (input.name) {
			//TOGGLE APPARTEMENT SECTION
			case 'isAppartement':
				this.appartementSelectionToggle(input);
				break;
			//TOGGLE OLD INTERNET PROVIDER OPTIONS
			case 'haveInternet':
				this.haveInternetToggle(input);
				break;
			//TOGGLE LA SELECTION GOUPILLE ET DE LA TECHNOLOGIE AU CHANGEMENT DE OLD PROVIDER
			case 'internetProvider':
				this.changeInternetProvider(input);
				break;
			//TOGGLE LA DELIVERY SI AU CHANGEMENT DU BOUTON RADIO
			case 'delivery':
				this.deliveryToggle(input);
				break;
			//TOGGLE LA DELIVERYFORM SI AU CHANGEMENT DU BOUTON RADIO
			case 'sameDeliveryAddress':
				this.deliveryAddressChange(input);
				break;
			//TOGGLE LA DELIVERYAPP SI AU CHANGEMENT DU BOUTON RADIO
			case 'deliveryAppartement':
				this.deliveryAppToggle(input);
				break;
			//TOGGLE DU CONSENTEMENT DE CREDIT
			case 'creditConsent':
			case 'oldLivingAddress':
				this.manageCreditConsent();
				break;
			case 'oldTech':
				this._$OrderService.setInstallFee(input.value === 'cable_or_fiber' ? this.DSLInstallFee : this.DSLInstallFee);
				break;
		}
	}

	appartementSelectionToggle(input) {
		const parentIsAppartement = document.querySelector('#appNumber').parentElement;
		if (input.value === 'true') {
			document.forms['personalInfo']['appNumber'].required = true;
			parentIsAppartement.classList.remove('hidden')
		}
		else {
			document.forms['personalInfo']['appNumber'].required = false;
			parentIsAppartement.classList.add('hidden');
			this.resetInput('#appNumber');
		}
	}

	resetInput(selector) {
		let input = document.querySelector(selector)
		if (input) {
			input.value = '';
			this._$OrderService._infoFormDataBuilder(input);
		}
	}

	haveInternetToggle(input) {
		const oldProviderInfo = document.querySelector('#oldProviderInfo');
		if (input.value === 'true') {
			document.forms['personalInfo']['internetProvider'].required = true;
			oldProviderInfo.classList.remove('hidden');
			this._$OrderService.setInstallFee(this.DSLInstallFee);
		} else {
			let provider = document.forms['personalInfo']['internetProvider']
			provider.required = false;
			provider.value = 'defaultValue';
			this._$OrderService._infoFormDataBuilder(provider);
			this.changeInternetProvider(provider);

			let safetyPin = document.forms['personalInfo']['oldTech'][0];
			safetyPin.required = false;
			safetyPin.checked = true;
			this.validateRadio(safetyPin);

			this._$OrderService.setInstallFee(this.DSLInstallFee);
			oldProviderInfo.classList.add('hidden');
		}
	}

	changeInternetProvider(input) {
		const goupille = document.querySelector('#goupille');
		const techno = document.querySelector('#technologie');

		if (input.value === 'videotron' && this.Qualification.details.internetType === 'v-cable') {
			document.forms['personalInfo']['safetyPin'][0].required = true;
			goupille.classList.remove('hidden');
		} else {
			document.forms['personalInfo']['safetyPin'][0].required = false;
			document.forms['personalInfo']['safetyPin'].value = false;
			goupille.classList.add('hidden');
			this._$OrderService._infoFormDataBuilder(document.forms['personalInfo']['safetyPin'][0]);
		}

		if (input.value === 'bell') {
			jQuery('input:radio[name=oldTech][value=dsl_fttn]').trigger('click');
		}
		else if (input.value === 'videotron' || input.value === 'cablevision' || input.value === 'cogeco' || input.value === 'rogers') {
			jQuery('input:radio[name=oldTech][value=cable_or_fiber]').trigger('click');
		}
		else{
			jQuery("input:radio[name=oldTech][value=cable_or_fiber]").prop('checked', false);
			jQuery("input:radio[name=oldTech][value=dsl_fttn]").prop('checked', false);

			this.sendToValidator(this.form.elements['oldTech']);
		}

		if (input.value === 'defaultValue') {
			document.forms['personalInfo']['oldTech'][0].required = false;
			techno.classList.add('hidden');
		} else {
			document.forms['personalInfo']['oldTech'][0].required = true;
			techno.classList.remove('hidden');
		}
	}

	deliveryToggle(input) {
		const deliveryFormContainer = document.querySelector('#deliveryFormContainer')
		jQuery('.activation-installation-plus').hide();
		jQuery('.question-date-activation').hide();
		jQuery('.question-livraison').hide();
		jQuery('.form-date-activation').hide();
		jQuery('.explication-disponibilite').hide();
		jQuery('.creditSection').hide();
		jQuery('#checkconsentRamasse').prop('checked', false); 
		jQuery('#checkconsentShip').prop('checked', false); 
		jQuery('#checkconsentActivation').prop('checked', false);
		jQuery('#checkconsentActivationDate').prop('checked', false);
		jQuery('#checkconsentDisponibilite').prop('checked', false);
		if (input.value === 'shipping') {
			jQuery('#ramasseMoi').hide();
			document.forms['personalInfo']['sameDeliveryAddress'][0].required = true;
			deliveryFormContainer.classList.remove('hidden');
			this._$OrderService.setShippingFee(this.shippingFee);		}
		else {

			jQuery('#ramasseMoi').show();
			document.forms['personalInfo']['sameDeliveryAddress'][0].required = false;
			deliveryFormContainer.classList.add('hidden');
			this._$OrderService.setShippingFee(0);
		}
	}

	deliveryAddressChange(input) {
		let isSameAddress = input.value === 'true'
		jQuery('#deliveryForm').toggleClass('hidden', isSameAddress);
		jQuery('#addDelivery, #radio16').prop('required', !isSameAddress);
	}

	deliveryAppToggle(input) {
		const deliveryApp = document.querySelector('#deliveryApp');
		if (input.value === 'true') {
			document.forms['personalInfo']['appNumber-delivery'].required = true;
			deliveryApp.classList.remove('hidden');
		}
		else {
			document.forms['personalInfo']['appNumber-delivery'].required = false;
			deliveryApp.classList.add('hidden');
			this.resetInput('#appNumber-delivery');
		}
	}

	manageCreditConsent() {
		//let hasConsent = jQuery('input[name="creditConsent"]:checked').val() === 'true';
		let hasConsent = jQuery('input[name="creditConsent"]:checked').length === 1;
		let oldAddressType = jQuery('input[name="oldLivingAddress"]:checked').val();
		let wasOutsideCanada = oldAddressType === 'outsideCanada';
		this.isLastAddressCanadian = oldAddressType === 'inCanada';
		jQuery('#resultDescription div').addClass('hidden');

		jQuery('[name="oldLivingAddress"]').prop('required', hasConsent && this._$OrderService.mustRentEquipement());

		jQuery('.order-summary__navbutton').toggleClass('disabled', !hasConsent || (this._$OrderService.mustRentEquipement() && !this.isCreditRequestApproved));
		jQuery('.eboxtv-navbutton').toggleClass('disabled', !hasConsent || (this._$OrderService.mustRentEquipement() && !this.isCreditRequestApproved));

		
		jQuery('#buyEquipement').toggleClass('hidden', hasConsent || jQuery('input[name="creditConsent"]:checked').val() === undefined);
		jQuery('#equifaxForm').toggleClass('hidden', !hasConsent);
		//jQuery('#doesNotPreQualify').toggleClass('hidden', !(hasConsent && wasOutsideCanada));
		jQuery('#submitBtn').toggleClass('hidden', !hasConsent  || this.isCreditRequestDone);

		// Old living address
		this._$OrderService._set_addresses({});
		jQuery('#lastAddress').prop('required', this._$OrderService.mustRentEquipement() && hasConsent && (oldAddressType === 'ONQC' || oldAddressType === 'inCanada')).val('');

		if(oldAddressType != undefined){
			jQuery('#lastAddressForm').toggleClass('hidden', oldAddressType === 'same' || wasOutsideCanada);
		}
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}

}