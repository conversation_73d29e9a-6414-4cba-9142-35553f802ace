.SinglePrice {
    width: 100%;
    //min-height: 325px;
    height: 325px;
    background: url('~@app/img/red-pattern.png') left top repeat;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    @media (max-width: 1199px) {
        margin-top: 30px;
    }
    &--haspromo {
        .Listing__promo {
            opacity: 1;
        }
        .Listing__prix--promo {
            display: inline-block!important;
        }
    }
    .Listing__promo {
        right: auto;
        left: -4px;
        transform: rotate(-82deg);

        @media (max-width: 1199px) {
            background: url('~@app/img/Module/Flag/promo-fr.svg')
        }
    }
    .Listing__recap {
        border-top: 0;
        padding-top: 0;
        
        &-wrapper {
            text-align: center;
        }
    }
    .Listing__fournisseur {
        left: auto;
        right: 25px;
    }
    .Listing__apartir {
        font:400 16px/22px $f-primary;
        color: $c-white;
        margin-left: 6px;
        margin-bottom: 0;
        text-align: left;
    }
    .Listing__prix {
        font:900 100px/100px $f-primary;
        color: $c-white;
        position: relative;
        margin-left: 0;
        sup {
            font-size:35px;
            vertical-align: super;
            top: 3px;
        }
        span {
            position: absolute;
            right: 3px;
            top: 50px;
            font: 400 12px $f-secondary;
        }
        &--promo {
            font:900 22px $f-primary;
            margin-right: 10px;
            color: $c-white;
            position: relative;
            display: none;
            sup {
                font-size: 12px;
            }
            &:after {
                content: "";
                width: 100%;
                height: 1px;
                background: $c-light-grey;
                opacity: 0.7;
                position: absolute;
                left: 0;
                top: 14px;
                transform: rotate(-25deg);
            }
        }
    }

    .Listing__forfait-title {
        font: 700 25px/30px $f-primary;
        color: #FFF;
        margin-bottom: 20px;
    }
}

.single-internet {
    .SinglePrice {
        justify-content: center;

        .Listing__recap {
            display: flex;
            align-items: center;
            height: 100%;
            flex: 1 0 auto;
        }

        .ButtonEffect {
            margin-top: 12px;

            @media (max-width:991px) {
                margin-top: 10px;
            }
        }
    }
}


.single-internet {
    .display-price {
        font: 900 100px/100px "BrandonGrotesque", sans-serif!important;
        color: #FFF!important;
        position: relative!important;
        margin-left: 0!important;
        top: auto!important;
        right: auto!important;
    }
    sup {
        top: 62px!important;
    }
    .SinglePrice .Listing__prix span {
        right: -55px;
        top: inherit;
    }
}

.single-internet:lang(en) {
    .SinglePrice .Listing__prix span {
        right: -42px;
        top: inherit;
    }
}

.single-telephonies,
.single-internet {
    .SinglePrice {
        min-height: 325px;
        height: auto;
    }
}

.single-internet {
    .SinglePrice {
        padding: 20px;
    }
}