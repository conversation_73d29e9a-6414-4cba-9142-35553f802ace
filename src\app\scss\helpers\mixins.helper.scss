@mixin checklistItem {
    font: 16px/22px $f-primary;
    list-style: none;
    background: url('../../img/ic_check.png') no-repeat left 2px;
    background-size: 16px 16px;
    margin: 0 0 10px 0;
    padding: 0 0 0 26px;
    //display: inline-block;
    width: 100%;
    &:last-of-type {
        margin: 0;
    }
}

@mixin applyAtRoot($selector) {
	$path: &;

	@at-root {
		#{ $selector } #{ $path } {
			@content;
		}
	}
}

.fakeList {
    &:before {
        content: '';
        width: 18px;
        height: 18px;
        background: url('../../img/ic_check.png') no-repeat left 0px;
        background-size: 16px 16px;
        padding: 0 0 0 26px;
    }
}

.noCheck {
   li {
    background: none!important;
    padding-left: 0!important;
   } 
}