.BandeRougeCarousel {
    position: relative;
    //margin: 50px 0;
    background: #BE2323;
    padding: 80px 0;
    text-align: center;
    @media (max-width: 991px) {
        padding: 60px 0;
    }
    &__slider {
        margin-top: 70px;
        margin-left: 0;
        padding:0;
        @media (max-width: 991px) {
            margin-top: 40px;
        }
        li {
            //max-width: 322px;
            @media (max-width: 767px) {
                max-width: none;
            }
        }
    } 

    &__black{
        // background: url('~@app/img/black-pattern.png') left top repeat;
        background: #1F1F1F;
        padding: 0;

        .BandeRougeCarousel__title{
        text-transform: uppercase;
        color: $c-white;
        font: 900 30px $f-primary;
        margin: 80px 0 -30px 0;

        @media (max-width: 450px) {
            font: 900 14px $f-primary;
        }

        .BandeRougeCarousel__subTitle {
            color: white;
            font: 24px $f-secondary;
            margin-top: 50px;
        }
     }

    }

    &__subTitle {
        color: white;
        font: 24px $f-secondary;
        margin-top: 50px;
    }

    &__title {
        text-transform: uppercase;
        color: $c-white;
        font: 900 45px/50px $f-primary;
        margin: 0;
        @media (max-width: 991px) {
            font: 900 26px/32px $f-primary;
        }
        @media (max-width: 991px) {
            font: 900 22px/24px $f-primary;
        }
    }
    &__icon {
        fill: $c-white;
        width: 50px;
        height: 50px;
        margin-bottom: 30px;
    }
    &__titre {
        color: $c-white;
        font: 20px/30px $f-secondary;
        margin-bottom: 5px;
    }
    &__subtitle {
        color: $c-white;
        font: 700 18px/22px $f-primary;
        text-transform: uppercase;
    }
    &__text {
        color: $c-white;
        font: 16px/22p $f-primary;
        margin-top: 15px;
    }

    .owl-nav {
        &.disabled {
            //display: block!important
        }
        .owl-prev {
            background: url('../../../img/Icon/ic_arrow-left-white.svg') no-repeat center top;
            background-size: 100%;
            width: 32px;
            height: 32px;
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            text-indent: -3000px;
            opacity: 0.50;
            transition: $t-primary;
            &:hover {
                opacity: 1;
            }
            @media (max-width: 991px) {
                left: -25px;
            }
        }
        .owl-next {
            background: url('../../../img/Icon/ic_arrow-left-white.svg') no-repeat center top;
            background-size: 100%;
            width: 32px;
            height: 32px;
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%) rotate(180deg);
            text-indent: -3000px;
            opacity: 0.50;
            transition: $t-primary;
            &:hover {
                opacity: 1;
            }
            @media (max-width: 991px) {
                right: -25px;
            }
        }
    }
    &--grey {
        background: url('../../../img/dark-grey-pattern.png') left top repeat;
    }

    &__wrapper {
        position: relative;
        padding: 0 60px;

        @media (max-width: 400px) {
            padding: 0;
        }
    }

    &--controls {
        .BandeRougeCarousel__controls {
            top: auto;
            bottom: 30px;
        }
    }

    &__controls_notVisible{
        .BandeRougeCarousel__controls{
            display: none;
        }

        @media (max-width: 768px) {
            .BandeRougeCarousel__controls{
                display: block;
                position: absolute;
                top: 50%;
                left: 0;
                width: 100%;
                z-index: 10;
                height: 60px;

                @media (max-width: 430px) {

                    top: 75%;

                    &--prev {
                        left: auto;
                        right: 100%;
                        margin-right: -20px;
                    }
        
                    &--next {
                        right: auto;
                        left: 100%;
                        margin-left: -20px;
                        transform: rotate(180deg);
                    }
                }
            }
        }

    }

    &__controls {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        z-index: 10;
        height: 60px;
    }

    &__control {
        position: absolute;
        padding: 0;
        appearance: none;
        background: none;
        border: none;
        display: inline-block;
        cursor: pointer;
        font-size: 0;

        &--prev {
            left: 0;
        }

        &--next {
            right: 0;
            transform: rotate(180deg);
        }

        @media (max-width: 400px) {
            &--prev {
                left: auto;
                right: 100%;
                margin-right: -20px;
            }

            &--next {
                right: auto;
                left: 100%;
                margin-left: -20px;
                transform: rotate(180deg);
            }
        }
    }

    &__control-icon {
        width: 60px;
        height: 60px;
        fill: #FFF;

        @media (max-width: 400px) {
            width: 40px;
            height: 40px;
        }
    }
}