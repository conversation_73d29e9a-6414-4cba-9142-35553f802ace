.cform {
	text-align: center;

	&__content {
		margin-bottom: 30px;
		@include clearfix;

		@media (max-width: 630px) {
			margin-bottom: 40px;
		}
	}

	&__input-container {
		&--left {
			text-align: right;
			padding-right: 60px;
		}

		&--right {
			text-align: left;
		}
	}

	&__field-wrapper {
		display: inline-block;
		text-align: center;
	}

	&__input-container-title {
		font: 400 22px/20px $f-primary;
		color: $c-mid-grey;
		margin-bottom: 30px;
	}

	&__input {
		appearance: none;
		border: 1px solid $c-primary;
		text-align: center;
		color: $c-primary;
		font: 700 14px/12px $f-primary;
		width: 280px;
		line-height: 25px;
		&::placeholder {
			color: lighten($c-primary, 35%);
			letter-spacing: 1px;
			font-weight: 400;
		}

		&.invalid-input {
			border-width: 3px;
			border-color: #BE2323;
		}
	}

	&__label {
		display: inline-block;
		font: 900 14px/24px $f-primary;
		color: $c-primary;
		text-transform: uppercase;
		margin-right: 40px;
		vertical-align: middle;

		&:last-child {
			margin-right: 0;
		}

		@media (max-width: 850px) {
			margin-right: 12px;
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			font-size: 17px;
			margin-right: 7px;
		}
	}

	&__radio-wrapper {
		&.invalid-input {
			border: 2px solid #BE2323;
			padding: 5px;
		}

		@media (max-width: 460px) {
			display: flex;
			justify-content: space-between;
			width: 100%;
			max-width: 320px;
			margin: 0 auto;

			.cform__label {
				margin-right: 0;
			}
		}
	}

	&__radio {
		position: relative;
		top: 2px;
		appearance: none;
		border: 2px solid currentColor;
		border-radius: 100%;
		width: 15px;
		height: 15px;
		margin-left: 15px;
		outline: none;
		transition: all 0.2s $cubic;

		&:checked {
			border-width: 6px;
		}

		@media (max-width: 800px) {
			margin-left: 5px;
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			margin-left: 0;
		}
	}

	&__btn-container {
		position: relative;
		display: inline-block;
	}

	&__btn-wrapper {
		margin-bottom: 15px;
		&:hover {
			.cform {
				&__btn-splash {
					animation: splash 0.6s steps(3) infinite;
				}
			}
		}
	}

	&.form-sent--success, &.form-sent--error {
		.cform__content, .cform__btn-container, .cform__btn-mouse {
			display: none;
		}
	}

	&.form-sent--success {
		.cform {
			&__message--success {
				display: block;
			}
		}
	}

	&.form-sent--error {
		.cform {
			&__message--error {
				display: block;
			}
		}
	}

	&__message {
		font: 900 25px/30px $f-primary;
		color: $c-primary-dark;
		display: none;
		margin: 60px 0 90px;
	}

	&__submit-btn {
		position: relative;
		display: inline-block;
		&:hover {
			background: $c-primary;
			color: #FFF;
		}
	}

	&__btn-mouse {
		position: absolute;
		display: inline-block;
		top: 29px;
		left: 96%;
		width: 58px;
		height: 60px;
		overflow: visible !important;
		pointer-events: none;

		@include fromRootSelector(':lang(en_CA)') {
			top: 30%;
			left: 98%;
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			width: 55px;
			height: 55px;
		}
	}

	&__note {
		font: 600 12px/11px $f-primary;
		color: $c-mid-grey;
		text-transform: uppercase;
	}



	@media (max-width: 630px) {
		&__input-container, &__field-wrapper {
			display: block;
			width: 100%;
		}

		&__input-container {
			margin-bottom: 33px;
			padding: 0;

			&:last-child {
				margin-bottom: 0;
			}
		}

		&__input-container-title {
			margin-bottom: 16px;
			font-size: 25px;
		}

		&__input {
			width: 100%;
			height: 35px;
			line-height: 35px;
			font-size: 22px;

			&::placeholder {
				font-size: 14px;
			}
		}
	}
}