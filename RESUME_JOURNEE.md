## Résumé de la journée

- **Survol du site Ebox avec Gérémy.**
- **Identification des pages stratégiques où il y a souvent des bugs**
  - Page de qualification
  - Page d’information personnelle
  - Page de confirmation de commande
- **Introduction au troubleshooting (survol du DevTool).**
- **Fonctionnement des codes promo.**
- **Ajout à Bitbucket, récupération du repo et survol des fichiers importants avec Gérémy.**
- **Observation des tâches effectuées en après-midi.**

### Fichiers importants
- `sticky-cart.php` → Template
- `numerique-popup.directive.js` → Directive
- `numerique-cart.directive.js` → Directive
- `forfaits-listing.component.ts`
- `commande-paiement.directive.js`



### Qualification
- **But**: système de qualification (structure, debug).
- Qualification nécessaire pour accéder aux commandes Ebox (internet, TV, téléphone).
- En Ontario, pas de service TV.

**En cas de problème de qualification**:
1. Ouvrir la console (DevTool).
2. Ongle<PERSON> Réseau → filtrer par Fetch/XHR.
3. Faire une qualification et vérifier `controller_ajax.php` (affiché dans Réseau).
4. Vérifier le cookie `eboxQualification` (qualification stockée dans les cookies).

**Fichiers pertinents**:
- `/validateur/controler_ajax.php` (envoi vers Ebox)
- `src/_common/ts/services/Qualify.service.ts`
- `themes/ebox2018/templates/partials/commons/templates/Qualification.php`
- `/wp-content/themes/ebox2018/modules/components/validateur` (composant + routes de qualif)

### Information personnelle
**Règles de test**:
- Prénom: `EBOX-TEST`
- Nom: `TEST-SCORE-NEUF`
- Courriel et cellulaire: ne pas utiliser ses infos personnelles.

**Enquête de crédit**:
- Cliquer sur Valider → surveiller `controller_ajax.php` dans DevTool (Réseau).
- Le code client est l’élément le plus important pour les tests.
- En cas d’erreur, un ADM apparaîtra.
- Relever le `ADM-*` dans `controller_ajax.php`.
- Remonter à JP → décision si on contacte Ebox.

**Fichiers nécessaires**:
- `/src/eboxtv/js/directives/personnal-info.directive.js`
- `/wordpress/wp-content/themes/ebox2018/commande-info-personnelle.php`
- `/wordpress/wp-content/themes/ebox2018/modules/components/validate_order/controler_ajax.php`

### Confirmation de commande
**Cartes de test Moneris**:
- Visa → `4242 4242 4242 4242`
- MasterCard → `5454 5454 5454 5454`

- Passerelle: Moneris → rechercher `moneris_single.js` dans l’IDE.

**Fichier nécessaire**:
- `/wordpress/wp-content/themes/ebox2018/modules/components/validate_order_moneris/controler_ajax.php`

### Gestion des données de commande
- **localStorage** → infos non persistantes (temporaire).
- **Cookies** → infos persistantes avec expiration (qualif, langue, province).
- **Backend (wp-admin)** → reste des infos gérables côté administration.

### Troubleshooting
**DevTool (JS)**:
- `console.log()` avant et après un bloc de code (équivalent d’un print).
- Permet de savoir si le code passe et où se situe le bug.

**PHP**:
- Utiliser `var_dump`.
- Activer le debug dans `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_DISPLAY', false); // Affiche dans la page
define('WP_DEBUG_LOG', true);      // Log
```

- Log disponible dans: `/wp-content/debug.log`
- Doc complète: `docs/debug.md`

### Accès & Passwords
- Gestion via 1Password.
- Si absent de 1Password → vérifier Cloudways.
- Sinon → demander à JP.

### Codes promo
- Documentation → `/docs/code-promo.md`
- Validation avec JP (prompt code promo).

### API & Routes
- Routes API (existantes + création de nouvelles).
- Système de redirection (langue/région).
- Système de commande.

### Objectifs
- Être capable de débugger.
- Création et modification de nouvelles fonctionnalités.

### Exemple concret – Forfaits Costco
**Contexte**: afficher des forfaits Costco avec validation par numéro de membre.

**Étapes**:
1. Query pour n’afficher que les forfaits taxo Costco.
2. Filtrage via une route spécifique → validation du numéro Costco (même principe que CAA Québec).
3. Création d’un cookie Costco.

### Général
**VPN**:
- Connexion obligatoire à NordVPN (adresse dédiée) pour accéder à beta2, beta3.

**Builder**:
- Node `9.6.0`.
- On build seulement si modification JS/TS.

**Commandes**:

```bash
npm run build-staging   # staging
npm run build-prod-code # production
```


