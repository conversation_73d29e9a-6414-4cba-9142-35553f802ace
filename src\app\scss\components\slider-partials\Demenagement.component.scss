$mainTransitionDuration: 0.9s;
$mainTransitionDelay: 0.3s;

.Demenagement {
	display: flex;
	background: url('../../../img/grey-pattern.png') left top repeat;
	padding-top: 80px;
	color: #FFF;

	&--red {
		background-image: url('../../../img/bg-slider-demenagement--red.png');
	}

	@media (max-width: 1600px) {
		align-items: center;
		padding-top: 0;
	}
	@media (max-width: 1199px) {
		background-size: 100% 100%;
	}
	@media (max-width: 960px) {
		background: url('../../../img/grey-pattern.png') left top repeat;

		&--red {
			background-image: url('../../../img/bg-slider-demenagement--red@0,5x.png');
		}
	}
	@media (max-width: 800px) {
		flex-direction: column;
	}
	@media (max-width: 370px) {
		background-size: cover;
	}

	@include mediaScreenRatio(2) {
		background: url('../../../img/grey-pattern.png') left top repeat;

		&--red {
			background-image: url('../../../img/<EMAIL>');
		}

		@media (max-width: 768px) {
			background: url('../../../img/grey-pattern.png') left top repeat;

			&--red {
				background-image: url('../../../img/bg-slider-demenagement--red.png');
			}
		}

		@media (max-width: 380px) {
			background: url('../../../img/grey-pattern.png') left top repeat;

			&--red {
				background-image: url('../../../img/bg-slider-demenagement--red@0,5x.png');
			}
		}
	}

	&__col {
		flex: 0 0 50%;
		opacity: 0;
		transition: all $mainTransitionDuration cubic-bezier(0.215, 0.610, 0.355, 1);

		&--left {
			text-align: right;
			padding-top: 48px;

			.Demenagement__col-wrapper {
				padding-bottom: 55px;

				@media (max-width: 1600px) {
					padding-bottom: 20px;
				}
			}
		}

		&--right {
			transform: translateX(-100%);
			padding-left: 111px;
		}

		@media (max-width: 1475px) {
			&--left {
				flex-basis: 60%;
			}

			&--right {
				flex-basis: 40%;
				padding-left: 60px;
			}
		}
		@media (max-width: 1075px) {
			&--left {
				flex-basis: 55%;
			}

			&--right {
				flex-basis: 45%;
			}
		}
		@media (max-width: 900px) {
			&--left {
				padding-top: 20px;
			}

			&--right {
				padding-left: 30px;
			}
		}

		@media (max-width: 800px) {
			text-align: center;
		}
	}

	&__col-wrapper {
		position: relative;
		display: inline-block;
	}

	&__title {
		font: 900 50px/66px $f-primary;
		margin-bottom: 40px;

		.is-doodled {
			&--underlined {
				&:after {
					height: 15%;
					top: 85%;
				}
			}
		}

		@media (max-width: 1600px) {
			font: 900 48px/64px $f-primary;
		}
		@media (max-width: 1250px) {
			font: 900 42px/58px $f-primary;
			margin-bottom: 20px;
		}
		@media (max-width: 1075px) {
			font: 900 3.4vw/1.4 $f-primary;
			margin-bottom: 20px;
		}
		@media (max-width: 800px) {
			font: 900 20px/28px $f-primary;
		}
	}

	&__underlined-container {
		position: relative;
		display: inline-block;

		&:before, &:after {
			content: '';
			position: absolute;
			display: none;
			width: 60px;
			height: 60px;
			background: {
				image: url('../../../img/Icon/drawing-arrow05-white.svg');
				size: contain;
				repeat: no-repeat;
			};
			animation: pulse 0.8s steps(2) infinite;

			@media (max-width: 1024px) {
				width: 40px;
				height: 40px;
			}
			@media (max-width: 730px) {
				content: none;
			}
		}
		
		&:before {
			top: 80%;
			right: 97%;
		}

		&:after {
			left: 100%;
			top: 0;
			transform: rotateY(180deg);
			animation-name: pulseMirror;
			animation-delay: 0.1s;
		}
	}

	&__truck-svg {
		display: inline-block;
		width: 610px;
		height: 457px;
		transform-origin: center bottom;
		overflow: visible !important;
		// animation: roadBumps 0.4s infinite;

		@media (max-width: 1600px) {
			width: 425px;
			height: calcFromRatio(425px, (457 / 610));
		}
		@media (max-width: 1475px) {
			width: 375px;
			height: calcFromRatio(375px, (457 / 610));
		}
		@media (max-width: 1250px) {
			width: 325px;
			height: calcFromRatio(325px, (457 / 610));
		}
		@media (max-width: 1199px) {
			width: 275px;
			height: calcFromRatio(275px, (457 / 610));
		}
		@media (max-width: 900px) {
			width: 28vw;
			height: calcFromRatio(28vw, (457 / 610));
		}
		@media (max-width: 800px) {
			width: 136px;
			height: calcFromRatio(136px, (457 / 610));
		}
	}

	&__box-svg {
		position: absolute;
		bottom: 0;
		left: 88px;
		width: 188px;
		height: 129px;
		opacity: 0;
		transform-origin: center bottom;
		transition: all 0.2s $cubicElastic;

		@media (max-width: 1600px) {
			width: 120px;
			height: calcFromRatio(120px, (129 / 188));
		}
		@media (max-width: 1199px) {
			width: 90px;
			height: calcFromRatio(90px, (129 / 188));
			bottom: 15px;
			left: 20%;
		}
		@media (max-width: 800px) {
			display: none;
		}
	}

	&__speed-trails {
		transform: translateX(50px);
		path {
			animation: speedTrails 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) infinite;

			@for $i from 1 through 3 {
				&:nth-child(#{ $i }) {
					animation-delay: #{ ($i - 1) * 0.4s };
				}
			}
		}
	}


	@at-root {
		.owl-item.active, .Demenagement--active {
			.Demenagement {
				&__col {
					opacity: 1;
					transition-delay: $mainTransitionDelay;

					&--left {
						transition-delay: #{$mainTransitionDuration * 0.75};
					}

					&--right {
						transform: translateX(0%);
					}
				}

				&__box-svg {
					animation: fall 0.3s ease-in forwards;
					animation-delay: #{ $mainTransitionDuration + 0.2s };
				}

				&__underlined-container {
					&:before, &:after {
						display: block;
						transition-delay: #{ $mainTransitionDuration + 0.3s };
					}
				}
			}
		}
	}
}

@keyframes roadBumps {
	0%, 100% {
		transform: scaleY(1.0125);
	}

	50% {
		transform: scaleY(1);
	}
}

@keyframes fall {
	0% {
		opacity: 0;
		transform: scaleY(1) scaleX(1) translateY(-150%);
		transform-origin: center bottom;
	}

	60% {
		opacity: 1;
		transform: scaleY(1.1) scaleX(0.9) translateY(0%);
	}

	65% {
		opacity: 1;
		transform: scaleY(1) scaleX(1) translateY(0%);
	}

	75% {
		transform: scaleY(0.9) scaleX(1.05) translateY(0%);
	}

	85%, 100% {
		opacity: 1;
		transform: scaleY(1) scaleX(1) translateY(0%);
	}
}

@keyframes speedTrails {
	0% {
		opacity: 0;
		transform: translateX(0);
	}

	15% {
		opacity: 0.5;
	}

	100% {
		opacity: 0;
		transform: translateX(-15%);
	}
}