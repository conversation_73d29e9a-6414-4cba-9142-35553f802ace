.RedonneBanner {
	padding: 65px 0;
	background-image: url('../../img/ebox-bg--red.jpg');
	color: #FFF;

	&__title {
		font-size: 41px;
		line-height: 1.3;

		@media (min-width: 1170px) {
			@include applyAtRoot('html:lang(en)') {
				font-size: 48px;
			}
		}
		@media (min-width: 650px) and (max-width: 1169px) {
			@include applyAtRoot('html:lang(en)') {
				font-size: 6.1vw;
			}
		}
		@media (min-width: 640px) and (max-width: 1159px) {
			@include applyAtRoot('html:lang(fr)') {
				font-size: 4.9vw;
			}
		}

		&-part {
			position: relative;
			display: table;

			.truck {
				position: absolute;
				display: inline-block;
				width: 75px;
				height: 40px;
				margin: -20px 0 0 24px;
				left: 100%;
				top: 50%;
			}

			&--demenage {
				&:after {
					content: '';
					position: absolute;
					display: block;
					bottom: 100%;
					left: -4px;
					width: 102%;
					height: 25px;
					background: {
						image: url('../../img/Demenage_lignes.svg');
						size: 100% 100%;
						position: center;
						repeat: no-repeat;
					}

					@include applyAtRoot('html:lang(en)') {
						background-image: url('../../img/Demenage_lignes--small.svg');
					}
				}
			}

			.dog {
				bottom: -35px;
				left: -75px;
				width: 82px;
				height: 53px;
			}

			.cat {
				margin-bottom: -4px;
				bottom: 100%;
				right: 0;
				width: 80px;
				height: 72px;
			}

			@include applyAtRoot('html:lang(en)') {
	
				.cat {
					margin-bottom: -4px;
					bottom: 100%;
					right: 0;
					width: 80px;
					height: 72px;
				}
			}

			.pointed {
				position: relative;
				&:after {
					content: '';
					position: absolute;
					display: block;
					top: 100%;
					left: -12px;
					margin-top: -8px;
					width: 100%;
					height: 27px;
					background: {
						image: url('../../img/Demenage_flecheBlanche.svg');
						size: 100% 100%;
						position: center;
						repeat: no-repeat;
					}
				}
			}

			.exclamations {
				top: 4px;
				left: 100%;
				margin-left: 4px;
				width: 57px;
				height: 57px;

				@include applyAtRoot('html:lang(en)') {
					@media (min-width: 485px) and (max-width: 1169px) {
						top: 2.1vw;
					}
				}
				@include applyAtRoot('html:lang(en)') {
					@media (min-width: 485px) and (max-width: 1169px) {
						top: 1.3vw;
					}
				}
			}

			.underlined {
				&:after {
					background-image: url('../../img/Demenage_sousligne.svg');
					background-size: 100% 100%;
					top: auto;
					bottom: 0;
					width: 100%;
				}
			}

			@media (min-width: 360px) {
				@include applyAtRoot('html:lang(en)') {
					br {
						display: none;
					}
				}
			}
			@media (max-width: 359px) {
				@include applyAtRoot('html:lang(en)') {
					.cat {
						top: 5px;
						transform: rotate(16deg);
					}
				}
			}
		}

		.dark {
			color: $c-primary-dark;
			display: inline-block;

			@media (max-width: 1159px) {
				display: block;

				&:before {
					content: '';
					position: absolute;
					display: block;
					right: 100%;
					margin-top: 6px;
					width: 13px;
					height: 38px;
					background: {
						image: url('../../img/Demenage_pointe_gauche.svg');
						size: 100% 100%;
						position: center;
						repeat: no-repeat;
					};
				}
			}

			@media (max-width: 639px) {
				&:before {
					margin-top: 2.6vw;
					margin-bottom: 0;
				}
			}
			@media (max-width: 544px) {
				&:before {
					margin-top: 6px;
				}
			}
		}
	}

	@media (min-width: 641px) and (max-width: 1199px) {
		.dog {
			display: none;
		}
	}
	@media (max-width: 767px) {
		&__title {
			.pointed {
				&:after {
					content: none;
				}
			}
		}
	}
	@media (max-width: 640px) {
		@include applyAtRoot('html:lang(fr)') {
			padding-bottom: 0;
		
			&__title {
				position: relative;
				padding-bottom: 89px;
				font-size: 9.2vw;

				&-part {
					position: static;

					&--demenage {
						position: relative;
					}
				}

				.truck {
					display: none;
				}

				.dog {
					width: 51px;
					height: 57px;
					bottom: 15px;
					left: 45px;
					transform: rotate(20deg);
				}

				.cat {
					width: 64px;
					height: 44px;
					margin-bottom: -4px;
					bottom: 20px;
					right: 32px;
					left: auto;
				}

				.exclamations {
					width: 9vw;
					height: 9vw;
					top: 3vw;
				}
			}
		}

		@include applyAtRoot('html:lang(en)') {
			padding-bottom: 0;
		
			&__title {
				position: relative;
				padding-bottom: 76px;
				font-size: 10vw;

				&-part {
					position: static;

					&--demenage {
						position: relative;
					}
				}

				.truck {
					display: none;
				}

				.dog {
					width: 51px;
					height: 57px;
					bottom: 15px;
					left: 20px;
					transform: rotate(9deg);
				}

				.cat {
					width: 64px;
					height: 44px;
					top: 0;
					right: 16px;
					left: auto;
				}

				.exclamations {
					width: 9vw;
					height: 9vw;
					top: 3vw;
				}

				.pointed {
					&:after {
						content: none;
					}
				}
			}
		}
	}
	@media (max-width: 400px) {
		@include applyAtRoot('html:lang(en)') {
			padding-bottom: 0;
		
			&__title {
				.exclamations {
					width: 45px;
					height: 40px;
					top: 2vw;
				}
			}
		}
	}
}