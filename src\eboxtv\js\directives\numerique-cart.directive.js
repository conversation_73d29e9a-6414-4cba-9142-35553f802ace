/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { PriceCalcService, TVOrderService } from '../services';
import { CoreTools } from '@core/helpers';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { TVOptionsService } from '@eboxtv/ts/services/TVOptions.service';

export class NumeriqueCart extends Directive {

	_$OrderService = OrderOnlineService.getInstance()
	$orderOnlineService = OrderOnlineService.getInstance();

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-cartNumerique]';

	MonthlyPrices = [];

	$priceCalc = PriceCalcService.getInstance();
	$tvOrder = TVOrderService.getInstance();



	monthlyBill = 0;
	oneTimeBill = 0;

	sousTotalNoTVMonth = 0
	sousTotalNoTVUnique = 0

	state = {
		type : this.attrs.type,
		items: [],
		color: this.attrs.color,
	};


	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
		this.$orderOnlineService.initCart();
		this.displayIcon();
		// this.subscribeToChanges();
		this.subscribeToChargesChange();
	}
	
	subscribeToChargesChange(){
		this.$orderOnlineService.charges.subscribe(
				this.monthlyPricesChanges.bind(this)
		);
	}

	displayIcon(){
		var selectionPhone =  this._$OrderService.retPhoneOrder();
		if( selectionPhone && !jQuery.isEmptyObject(selectionPhone) && selectionPhone.type != 'none' ){
			jQuery('#sticky-cart-update-btn-telephonie').show();
			//jQuery('#sticky-cart-update-btn-telephonie').closest('a').attr("aria-hidden","false");
			jQuery('#sticky-cart-add-btn-telephonie').closest('a').remove();
			//jQuery('#sticky-cart-add-btn-telephonie').closest('a').attr("aria-hidden","true");
			jQuery('#sticky-cart-cancel-btn-telephonie').show();
			//jQuery('#sticky-cart-cancel-btn-telephonie').attr("aria-hidden","false");
		}else{
			jQuery('#sticky-cart-update-btn-telephonie').closest('a').remove();
			//jQuery('#sticky-cart-update-btn-telephonie').closest('a').attr("aria-hidden","true");
			jQuery('#sticky-cart-add-btn-telephonie').show();
			//jQuery('#sticky-cart-add-btn-telephonie').closest('a').attr("aria-hidden","false");
			jQuery('#sticky-cart-cancel-btn-telephonie').remove();
			//jQuery('#sticky-cart-cancel-btn-telephonie').attr("aria-hidden","true");
		}

		var selection_forfait =  this._$OrderService._retForfaitOption();
		if( selection_forfait && !jQuery.isEmptyObject(selection_forfait) ){
			jQuery('#sticky-cart-update-btn-television').show();
			//jQuery('#sticky-cart-update-btn-television').closest('a').attr("aria-hidden","false");
			jQuery('#sticky-cart-add-btn-television').closest('a').remove();
			//jQuery('#sticky-cart-add-btn-television').closest('a').attr("aria-hidden","true");
			jQuery('#sticky-cart-cancel-btn-tv').show();
			//jQuery('#sticky-cart-cancel-btn-tv').attr("aria-hidden","false");
		}else{
			jQuery('#sticky-cart-update-btn-television').closest('a').remove();
			//jQuery('#sticky-cart-update-btn-television').closest('a').attr("aria-hidden","true");
			jQuery('#sticky-cart-add-btn-television').show();
			//jQuery('#sticky-cart-add-btn-television').closest('a').attr("aria-hidden","false");
			jQuery('#sticky-cart-cancel-btn-tv').remove();
			//jQuery('#sticky-cart-cancel-btn-tv').attr("aria-hidden","true");
		}

		var selectionInternet = this._$OrderService.retInternetPlan();
		if( selectionInternet && !jQuery.isEmptyObject(selectionInternet) ){
			jQuery('#sticky-cart-update-btn-internet').closest('a').show();
			//jQuery('#sticky-cart-update-btn-internet').closest('a').attr("aria-hidden","false");
			jQuery('#sticky-cart-add-btn-internet').closest('a').remove();
			//jQuery('#sticky-cart-add-btn-internet').closest('a').attr("aria-hidden","true");
		}else{
			jQuery('#sticky-cart-update-btn-internet').closest('a').remove();
			//jQuery('#sticky-cart-update-btn-internet').closest('a').attr("aria-hidden","true");
			jQuery('#sticky-cart-add-btn-internet').show();
			//jQuery('#sticky-cart-add-btn-internet').closest('a').attr("aria-hidden","false");
		}


	}
	

    monthlyPricesChanges(charges ){
		let promoMensuel = 0;


		this.monthlyBill = 0;
		const items = [];
/**
* AJOUTS DES ELEMENTS INTERNET DANS LA FACTURE MENSUEL
*/
		if(charges.internetPlan.planPrice){
			let internetPrice = parseFloat(charges.internetPlan.planPrice);
			if(charges.internetEquipement.length > 0){
				charges.internetEquipement.forEach(equitement => {
					if(equitement.buyRent == 'rent'){
						internetPrice += parseFloat(equitement.rentPrice);
					}
				});
			}

			if(charges.promoInternet.length > 0){
				charges.promoInternet.forEach(promo => {
					if(promo.recurrent === 'oui'){
						internetPrice -= parseFloat(promo.prix);
					}
				});
			}

			if(charges.promoInternetEquipement.length > 0){
				charges.promoInternetEquipement.forEach(promo => {
					if(promo.recurrent === 'oui'){
						internetPrice -= parseFloat(promo.prix);
					}
				});
			}

			items.push({	
				type: '',
				title: CoreTools.translate('Internet', 'Internet'),
				count: CoreTools.translate('modifier', 'change'),
				cost: internetPrice,
				icon: '',
			});
			this.calcSousTotalMonthly(internetPrice, 'add');




		}

/**
 * AJOUTS DES ELEMENTS TELEPHONIE DANS LA FACTURE MENSUEL
 */
		if(charges.phonePlan.planPrice){
			let phonePrice = parseFloat(charges.phonePlan.planPrice)
			if(charges.phoneEquipement.length > 0){
				charges.phoneEquipement.forEach(equitement => {
					if(equitement.buyRent == 'rent'){
						//phonePrice += parseFloat(equitement.price);
					}
				});
			}

			if(phonePrice > 0){
				items.push({
					type: '',
					title: CoreTools.translate('Téléphonie', 'Home Phone'),
					count: CoreTools.translate('modifier', 'change'),
					cost: phonePrice,
					icon: '',
				});
				this.calcSousTotalMonthly(phonePrice, 'add');
				
				//promoMensuel += 5.00;
				//this.calcSousTotalMonthly(5.00, 'substract');
			}


		}

		let stepsArray = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvOptions.steps.details : [];
		const hasTV = CookiesService.getCookies('hasTV');
		//let { total } = this.$priceCalc.getTotal();
		//const totals  = this.$priceCalc.getTotal();

		let { total } = this.$priceCalc.totals;
		const totals  = this.$priceCalc.totals;

		//ici
		const optionsTVBundle = this._$OrderService._retForfaitOption();
		let prix = total;
		if(hasTV == 'true'){
			if(prix > 0){
				if(optionsTVBundle.a_la_carte){
					stepsArray.forEach(step => {
						if(step.size == parseInt(optionsTVBundle.nbChaines)){
							var chaines = LocalStorage.get('selectedChannels')
							if(chaines == null || chaines == undefined){
								chaines = [];
							}
							for (const [key, value] of Object.entries(chaines)) {
								if(key == 'a-la-carte') {
									const channelLength = value.length;
									if(channelLength <  parseInt(optionsTVBundle.nbChaines)){
										prix -= totals.channels['a-la-carte']  + totals.channels['others'] // si on veut seulement ajoiuter les bundle price, on ajoute : + totals.channels['others']
									}
									else{
										prix -= step.cost
									}
								}
							  }
						}
					});
					
				}

				if(parseInt(charges.decodeur.qty) > 0  && charges.decodeur.buyRent == 'rent'){
					prix += parseFloat(charges.decodeur.qty - 1) * parseFloat(charges.decodeur.price);
				}
				else{
					prix += parseFloat(0)
				}

				if(charges.eboxTVOptions.cloudSpace){
					prix += parseFloat(charges.eboxTVOptions.cloudSpace.price);
				}
				
				/*if(charges.promoDecodeur.length > 0 && parseInt(charges.decodeur.qty) > 0){
					charges.promoDecodeur.forEach(promo => {
						prix -= parseFloat(promo.prix)
					});
				}*/

				// PROMO NUMERIQUE ONLY
				//PRM00200
				var selection_internet = this._$OrderService.retInternetPlan();
        		if( selection_internet && !jQuery.isEmptyObject(selection_internet) ){
					var internet_dispo = ['NUM00002', 'NUM00003', 'NUM00004', 'NUM00005', 'NUM00016', 'NUM00017',
										  'NUM00018', 'NUM00019', 'NUM00020', 'NUM00021', 'NUM00022', 'NUM00012',
										  'NUM00013', 'NUM00014', 'NUM00015', 'NUM00035', 'NUM00036'];
					if( internet_dispo.includes(selection_internet.forfait) ){
						var forfait_choisi = this._$OrderService._retForfaitOption();
						if( (forfait_choisi && !jQuery.isEmptyObject(forfait_choisi)) ){
							if(forfait_choisi.prmSku != ""){
								prix -= parseFloat(forfait_choisi.prmPrix);
							}
						}
					}
				}

				items.push({
					type: '',
					title: CoreTools.translate('Télévision', 'Television'),
					count: CoreTools.translate('modifier', 'change'),
					cost: prix,
					icon: '',
				});
				this.calcSousTotalMonthly(prix, 'add');
			}

		}



		/*if(charges.promoDuo.length > 0){
			charges.promoDuo.forEach(promo => {
				if(promo.recurrent == 'oui'){
					promoMensuel += parseFloat(promo.prix)
					this.calcSousTotalMonthly(promo.prix, 'substract');
				}
			});
		}

		if(charges.promoInfo.length > 0){
			charges.promoInfo.forEach(promo => {
				if(promo.recurrent == 'oui'){
					promoMensuel += parseFloat(promo.prix);
					this.calcSousTotalMonthly(promo.prix, 'substract');
				}
			});
		}*/

		if(promoMensuel > 0){
			items.push({
				type: '',
				title: CoreTools.translate('Rabais mensuel', 'Monthly rebate'),
				count: '',
				cost: promoMensuel,
				icon: '',
				promo : true,
			});
		}
/**
 * AJOUTS DU SOUS-TOTAL DANS LA FACTURE MENSUEL
 */
		items.push({
			type: '',
			title: CoreTools.translate('Sous-total', 'Subtotal'),
			count: CoreTools.translate('modifier', 'change'),
			cost: this.monthlyBill,
			icon: '',
			subtotal : true,
			sectionMonthly : CoreTools.translate('/mois', '/month')
		});

		total = parseFloat(total.toFixed(2));

		this.$orderOnlineService.cartMonthlyPrice.next(this.monthlyBill - total);

		// this.setState({ items });
		this.displayPrices(items)
	}

    calcSousTotalMonthly(add, plusOrMinus){
		if(typeof(add) === 'string'){
			add = parseFloat(add);
		}
		if(plusOrMinus == 'add'){
			this.monthlyBill += add;
		}
		else{
			this.monthlyBill -= add;
		}
	}


    onPriceChange(totals) {
		this.update(totals);
	}

    update(totals = this.$priceCalc.totals) {
		const items = this.buildItemsArray(totals);
		// this.setState({ items });
	}

    buildItemsArray(totals) {
		const selectedBundle = this.$tvOrder.getSelectedBundle();
		const items = [];

		if (!selectedBundle || (selectedBundle && !selectedBundle.isFeatured)) {
			items.push({
				type: 'base',
				title: CoreTools.translate('La base', 'Basic channels'),
				count: CoreTools.translate('obligatoire', 'mandatory'),
				cost: totals.channels.base,
				icon: 'icon-ic_laptop'
			});

		}

		if (selectedBundle) {
			items.push({
				type: 'pre-selected',
				title: CoreTools.translate(selectedBundle.name, selectedBundle.nameEN),
				count: selectedBundle.isFeatured ? selectedBundle.channels.length + this.$channels.getLength('base') : selectedBundle.channels.length,
				cost: selectedBundle.cost,
				icon: 'icon-ic_laptop'
			});
		}
		return items;
	}


	displayPrices(items){
		items.forEach(item => {
			if(item.title == 'Internet'){
				document.querySelector('.internetPrice .detail-price').innerHTML = CoreTools.translate(item.cost.toFixed(2)+'$', '$'+item.cost.toFixed(2));
				let price = item.cost.toFixed(2)
				price = price.split('.')
				document.querySelector('.internetPrice .sr-only').innerHTML = price[0] + " dollars " + price[1] + " cents ";
			}
			else if(item.title == "Téléphonie" || item.title == "Home Phone"){
				document.querySelector('.telephonyPrice .detail-price').innerHTML = CoreTools.translate(item.cost.toFixed(2)+'$', '$'+item.cost.toFixed(2));
				let price = item.cost.toFixed(2)
				price = price.split('.')
				document.querySelector('.telephonyPrice .sr-only').innerHTML = price[0] + " dollars " + price[1] + " cents ";
			}
			else if(item.title == "Télévision" || item.title == "Television"){
				document.querySelector('.televisionPrice .detail-price').innerHTML = CoreTools.translate(item.cost.toFixed(2)+'$', '$'+item.cost.toFixed(2));
				let price = item.cost.toFixed(2)
				price = price.split('.')
				document.querySelector('.televisionPrice .sr-only').innerHTML = price[0] + " dollars " + price[1] + " cents ";
			}

			else if(item.title == "Sous-total" || item.title == "Subtotal"){
				let sup = document.createElement("SUP")
				let price = item.cost.toFixed(2)
				price = price.split('.')
				let dec = price[1];
				let int = price[0]
				if(CoreTools.lang == 'fr'){
					sup.textContent = dec + '$'
				}
				else{
					sup.textContent = price[1];
					int = '$' + int;
				}
				document.querySelector('.sticky-cart-price').innerHTML = int
				document.querySelector('.sticky-cart-price').appendChild(sup)
				document.querySelector('.sticky-cart-price-sr-only').innerHTML = "Total: " + price[0] + " dollars " + price[1] + " cents ";
			}
		});

	}
}




