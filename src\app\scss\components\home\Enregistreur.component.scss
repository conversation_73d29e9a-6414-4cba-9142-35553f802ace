.HomeEnregistreur {
	float: left;
	width: 100%;

	&__border {
		border-top: 2px solid #D8D8D8;
		padding: 120px 0;
		@media (max-width: 1199px) {
			padding: 90px 0 60px;
		}
	}
	&__section {
		padding: 0 15px;
	}
	&__title {
		font: 900 45px/50px "BrandonGrotesque", sans-serif!important;
		text-align: center;
		color: #ffffff;
		float: left;
		width: 100%;
		@media (min-width: 768px) and (max-width: 1199px) {
			font: 900 26px/32px "BrandonGrotesque", sans-serif!important;
		}
		@media (max-width: 767px) {
			font: 900 22px/24px "BrandonGrotesque", sans-serif!important;
			text-align: left;
		}
	}
	&__desc {
		font: 400 16px/22px "BrandonGrotesque", sans-serif!important;
		color: #C7C7C7;
		text-align: center;
		float: left;
		width: 100%;
		margin-top: 20px;
		@media (max-width: 767px) {
			text-align: left;
		}
	}
	&__imgsection {
		text-align: center;
		color: #C7C7C7;
		padding: 80px 0;
		float: left;
		width: 100%;
		@media (max-width:1199px){
			padding: 60px 0 0 0;
		}
		@media (max-width:767px){
			padding: 40px 0 0 0;
		}
	}
	&__imgsection .title {
		font: 700 20px/24px "BrandonGrotesque", sans-serif!important;
		color: #C7C7C7;
		padding-bottom: 40px;
	}
	&__imgsection > .border {
		border-right: 1px solid #979797;

		@media (max-width:767px) {
			border-right:none;
				border-bottom: 1px solid #979797;
				padding: 0 0 60px 0;
				margin-bottom: 60px;

		}

	}
	&__left {
		max-width: 445px;
		ul {
			margin: 40px 0 48px 18px;
			li {
				list-style-type: disc;
				text-align: left;
				line-height: 21px;
				margin: 0 0 5px 0;
			}
		}
	}
	&__block-title {
		width: 100%;
		display: inline-block;
		position: relative;
		.BackCircle {
			width: 81px;
			height: 81px;
			background-size: 81px 81px;
			float: left;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			&--red {
				background: url('../../../img/back-red.png') center center no-repeat;
			}
			&--grey {
				background: url('../../../img/back-grey.png') center center no-repeat;
			}
			&__price  {
				position: relative;
				color: $c-white;
				margin-top: 5px;
				padding-top: 5px;
				border: none!important;
				font: 900 32px/36px $f-primary!important;
				width: 62px;
				margin: 0!important;
				sup {
					position: relative;
					top: -13px;
					left: 3px;
					font: 900 17px $f-primary!important;
				}
				.dollar {
					font: 900 32px/25px $f-primary!important;
				}
			}
			&__mois {
				position: absolute;
				font: 400 8px/12px $f-secondary;
				left: 26px;
    			bottom: 6px;
				width: 40px;
				&:lang(en) {
					left: 35px;
				}
			}
		}
		h3 {
			width: calc(100% - 81px);
			top: 50%;
			position: absolute;
			transform: translateY(-50%);
			left: 100px;
			span {
				font-family: $ebtv-f-secondary;
				font-size: 25px;
				line-height: 30px;
				color: $c-white;
				display: block;
				text-align: left;
				&.gloria {
					font-weight: 400;
				}
				&.classic {
					font-family: $ebtv-f-primary;
					font-size: 25px;
					line-height: 30px;
					color: $c-white;
					text-transform: uppercase;
					@media (max-width: 767px) { 
						font-size: 18px;
						line-height: 25px;
					}
				}
			}
		}
	}
	&__devices-image {
		max-width: 445px;
		
		@media (max-width: 1199px) {
			width: auto;
		}

		@media (max-width: 991px) {
			max-width: 295px;
		}
		@media (max-width: 767px) {
			max-width: 445px;
		}
	}
	&__right {
		max-width: 445px;
		padding: 0 0 0 80px;
		ul {
			margin: 40px 0 125px 18px;
			li {
				list-style-type: disc;
				text-align: left;
				line-height: 21px;
				margin: 0 0 5px 0;
			}
			@media (max-width: 991px) { 
				margin: 40px 0 167px 18px;
			}
			@media (max-width: 767px) { 
				margin: 40px 0 60px 18px;
			}
		}
		@media (max-width: 991px) { 
			padding: 0;
			float: right;
		}
	}

	@media (max-width: 767px) {
		&__right, &__left {
			width: 100%;
			float: none;
			text-align: center;
			max-width: none;

			img {
				width: 370px;
				max-width: 100%;
			}
		}
		&__right {
			img {
				width: auto;
			}
		}
	}
}