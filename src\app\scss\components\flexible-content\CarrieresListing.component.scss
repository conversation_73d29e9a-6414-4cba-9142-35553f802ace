.CarrieresListing {
    .input-block{
        input::placeholder{
            color: $c-black;
        }
    }
    padding-top: 100px;
    padding-bottom: 100px;
    background: #1F1F1F;
    .CardListing__title {
        text-transform: uppercase;
        color: $c-white;
        font: 900 45px/50px $f-primary;
        margin: 0 auto 70px;
        // padding: 0 20px;
        @media (max-width: 767px)  {
            font: 900 25px/30px $f-primary;
            margin: 0 auto 40px;
        }
    }
    @media (max-width: 991px )  {
        padding-top: 70px;
        padding-bottom: 70px;
    }
    @media (max-width: 767px )  {
        padding-top: 40px;
        padding-bottom: 40px;
    }
    
}

.CarrieresListing .CardItem__title{
    height: 60px;
    padding: 0 20px;
    line-height: 22px;
    font-weight: 700;
    font-family: BrandonGrotesque;
    color: #333333;
    @media (max-width: 767px)  {
        height: 35px;
        line-height: 13px;
    }
}


body.single-carriere{
    .TwoCol-LeftTextRightImg{
        padding: 64px 0;
    }
    background: url('../../../img/ebox-texture-background--red.jpg') center center repeat;
    color: white;
    .Retour__link{
        color: $c-white;
        svg{
            fill: $c-white;
        }
        @media (max-width: 767px)  {
            padding-top: 10px;
        }
    }
    .Retour__link:hover{
        color: $c-black;
        svg{
            fill: $c-black;
        }
    }

    hr{
        // margin-top: 64px;
        // margin-bottom: 0;
        // padding-bottom: 64px;
        padding: 0;
        margin : 64px 0;
        width: 100%;
        border: 1px none;
        height: 1px;
        background-color: #979797;
    }


    .col-md-12{
        float: none;
        p{
            margin-bottom: 30px;
        }
        .PageTitle__title{
            margin-bottom: 0;
        }
            width: 100%;
    }
    #applyBtn{
        color: $c-primary;

    }
    #applyBtn:hover,
    #applyBtn:focus{
        color: $c-white;
    }
    #applyBtn::before{
        background: white;
    }

    .whiteBG{
        color: $c-black;
        background: $c-white;
    }

    .Wysiwyg>ul{
        font-size: 16px;
        columns: 2;
        @media (max-width: 767px)  {
            columns: 1;
        }
        li{
            display: inline-block;
            width: 100%;

        }
        ul{
            margin-top: 10px;
            li{
              background: none;
                position: relative;
            }
            li::before{
                content: '';
                width:10px ;
                height: 2px;
                background: $c-primary;
                position: absolute;
                top: calc(50% - 1px);
                left: 5px;
            }
        }
    }

    .row{
        @media (max-width: 767px)  {
            margin: 0;
        }
    }

    .BandeFormulaire input[type="text"],
    .BandeFormulaire input[type="email"]{
        background-color: $c-white;
        font: 600 16px "BrandonGrotesque", sans-serif !important;
        color: $c-black;
        height: 35px;
        border: 1px solid $c-grey-faded;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        margin-bottom: 10px;
        border-radius: 6px;
    }
    .BandeFormulaire input[type="file"]{
        display: hidden;
        height: 0;
        width: 0;
        padding: 0;
        margin: 0;
    }
    .BandeFormulaire{
        input::placeholder{
            color: $c-black;
        }
        h4{
            font-size: 25px;
            margin-bottom: 10px;
        }
    }

    .is-doodled{
        color: $c-white;
    }

    .BandeFormulaire__iconset{
        width: 80%;
        height: 66px;
        background-color: white;
        mask: url(../../../img/icon-set-contact.svg) no-repeat center / contain;
    }

    .fileInputContainer{
        display: flex;
        width: 100%;
        padding: 0;
        align-items: center;
        margin-bottom: 10px;
        flex-wrap: wrap;
        @media (max-width: 767px)  {
            flex-wrap: wrap;
        }
        span{
            padding-left: 15px;
            font-weight: 700;
            font-size: 16px;
            width: 50%;
            @media (max-width: 768px)  {
                width: 100%;
                padding-left: 0;
            }
        }
        label{
            display: flex;
            align-items: center;
            background-color: $c-white;
            border-radius: 6px;
            background-color: $c-white;
            font: 600 16px "BrandonGrotesque", sans-serif !important;
            color: $c-black;
            height: 35px;
            border: 1px solid $c-grey-faded;
            flex-grow: 1;
            padding: 0 15px;
            cursor: pointer;
            @media (max-width: 767px)  {
                width: 100%;
            }
        }

        #cvError,
        #attachmentsError{
            width: 100%;
            margin-top: 10px;
            background-color: transparent;
            border: none;
            color: $c-white;
        }
    }
    .BandeFormulaire input[type="submit"]{
        border: 2px solid $c-white !important;
        min-width: auto ;
        line-height: 45px ;
        margin-top: 20px !important;
    }

    .BandeFormulaire__right p {
        color: $c-white;
    }

    .BandeFormulaire input[type="submit"]:hover{
        border: 2px solid transparent !important;


    }

    .Wysiwyg h3,
    .TwoCol h3{
        margin-bottom: 16px;
    }

    .responsability{
        padding-top: 64px;
        padding-bottom: 0;
    }

    .benifits{
        padding-top: 0;
        padding-bottom: 64px;
    }

    .btnWrapper{
        position: relative;
        max-width: fit-content;
        svg{
            position: absolute;
            @media(max-width: 991px){
                display: none;
            }
        }
        #ic_mouse{
            width: 60px;
            right: -50px;
            top: -25px;
        }

        #ic_cursor{
            top: -77px;
            right: 207px;
            width: 28px;
            transform: scaleX(-1);
        }
    }


}
