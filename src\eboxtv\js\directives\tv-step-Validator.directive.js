import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { CoreTools } from '@core/helpers';
export class tvStepValidator extends Directive {
    _$OrderService = OrderOnlineService.getInstance();
	static selector = '[tvStepValidator]';


    // Il y a 2 étapes pour la TV, mais c'est le même icône sur la timeline. Faire attention.
 

    constructor(host) {
        super(host, []);
        this._onInit();
    }

    _onInit() {
        let stepValidator = this._$OrderService.getStepValidation();
        if(!stepValidator.tv){
            if( CoreTools.lang === 'en') window.location.href = '/unlimited-internet-order/';
			if( CoreTools.lang === 'fr') window.location.href = '/commande-internet-illimite/';
        }
    }

}