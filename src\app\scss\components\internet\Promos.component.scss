.Promos {
    margin-bottom: 30px;
    &__section-wraper {
        @media (max-width: 767px) {
            border-bottom: 1px solid #979797;
            margin-bottom: 20px;
        }
    }
    &__section-title {
        font: 900 45px/50px $f-primary;
        text-transform: uppercase;
        position:relative;
        display: inline-block;
        @media (max-width:991px) {
            font: 900 26px/32px $f-primary;
        }
    }
    &__infos {
        position: absolute;
        left: 156px;
        top: 0px;
        cursor: pointer;
        svg {
            width: 16px;
            height: 16px;
            fill: $c-primary;
            @media (max-width:767px) {
                left: 100px;
                position: relative;
            }
        }
        @media (max-width:991px) {
            left: 96px;
        }
        @media (max-width:767px) {
            left: 0px;
            width: 100%;
        }

    }
    &__section-exp {
        font: 400 12px/16px $f-primary;
        color: $c-medium-grey;
        margin: 0;
        @media (max-width:991px) {
            display: none;
        }
    }
    &__slider-wrapper {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;

        @media (max-width: 991px) {
            align-items: flex-start;
        }
        @media (max-width: 680px) {
            flex-wrap: wrap-reverse;
        }
    }
    &__slider {
        margin-left: 0;
        font-size: 0;

        &--adsl {
            display: inline-block !important;
            flex: 1 0 50%;
            overflow: hidden;
            padding: 0 15px 0 0;

            @media (max-width: 991px) {
                align-items: flex-start;
            }
            @media (max-width: 680px) {
                flex-basis: 100%;
                margin-top: 20px;
            }
        }

        .owl-item.center {
            text-align: center;
        }
    }
    &__slider-item {
        display: inline-flex;
        align-items: center;
        padding-top: 8px;
        @media (max-width:991px) {
            padding-top: 0;
        }
        @media (max-width: 767px) {
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    }
    &__slider-icon {
        svg {
            width: 50px;
            height: 50px;
            fill: $c-primary;
            @media (max-width:991px) {
                width: 35px;
                height: 35px;
            }

        }
    }
    &__slider-text {
        padding-left: 5px;
        @media (max-width: 767px) {
           text-align: center;
        }
    }
    &__slider-title {
        font: 300 16px/16px $f-secondary;
        color: $c-primary;

        &--sub {
            font: 700 16px/30px $f-primary;
            text-transform: uppercase;
            color: $c-grey;
            @media (max-width:991px) {
                font: 700 11px/12px $f-primary;
                margin-top: 5px;
            }
            @media (min-width:992px ) and (max-width: 1199px) {
                font: 700 13px/20px $f-primary;
            }
        }

        @media (max-width: 1199px) {
            font: 300 14px/16px $f-secondary;
        }
        @media (max-width:991px) {
            font: 300 12px/12px $f-secondary;
        }
    }
    &__close {
        opacity: 0;
        display: none;
    }
    @media (max-width: 767px) {
        &__close {
            opacity: 1;
            display: inline-block;
            position: absolute;
            right: 15px;
            top: 10px;
            width: 30px;
            height: 30px;
            border: 0px;
            background: none;
            svg {
                left: 0;
                width: 25px;
                height: 25px;
            }
        }
    }

    &__qualified-speeds {
        @media (min-width: 681px) {
            margin-left: auto;
        }
    }
}

.Promos__infos {
    [role="tooltip"],
    .hidetooltip.hidetooltip.hidetooltip + [role="tooltip"] {
    visibility: hidden;
    position: absolute;
    top: 2rem;
    left: 2rem;
    background: white;
    color: black;
    }

    [aria-describedby]:hover,
    [aria-describedby]:focus {
    position: relative;
    }

    [aria-describedby]:hover + [role="tooltip"],
    [aria-describedby]:focus + [role="tooltip"] {
    visibility: visible;
    }
}


.Promos__tooltip {
    width: 300px;
    min-height: 100px;
    box-sizing: border-box;
    border-radius: 10px;
    color: $c-grey;
    padding: 15px;
    background: white;
    box-shadow: 0px 5px 15px 0px rgba(0,0,0,0.3);
    position: absolute;
    z-index:9;
    left: 50%;
    strong {
        font-weight: 700;
    }

    &-arrow {
      width: 50px;
      height: 25px;
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      overflow: hidden;
      @media (max-width: 767px) {
          display: none;
      }
    }
}





  // LISTING
  .ListingPromo {
    margin-bottom: 100px;
    @media (max-width: 1199px) {
        margin-bottom: 70px;
    }
    @media (max-width: 991px) {
        margin-bottom: 40px;
    }

      &__image {
          height: 330px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url('~@app/img/red-pattern.png') left top repeat;
          padding: 15px;
          svg,
          img {
              max-width: 100%;
              height: auto;
          }
          .Listing__fournisseur {
              left: 45px;
          }
          @media (max-width: 767px) {
              padding: 25px;
              height: auto;
              margin-top: 10px;
              margin-bottom: 40px;
          }
          &--grey {
            background: url('../../../img/grey-pattern.png') left top repeat;
          }
      }
      &__content {
        min-height: 330px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        flex: 1 1 auto;
        flex-shrink: 0;
        align-items: flex-start;
        em {
            font: 400 12px/16px $f-primary;
            margin-top: 15px;
            color: $c-medium-grey;
            display: inline-block;
        }
        .ButtonEffect {
            margin-top: 40px;
        }
        @media (max-width: 767px) {
            height: auto;
            min-height: 1px;
        }
      }
      &__title {
          font: 900 35px/40px $f-primary;
          margin-bottom: 15px;
          width: 100%;
          @media (max-width: 991px) {
              font: 900 26px/32px $f-primary;
          }
          @media (max-width: 767px) {
            font: 900 24px/26px $f-primary;
        }
      }
      &__description {
          width: 100%;
      }
      &__divider {
          border-bottom: 1px solid $c-light-grey;
          margin-top: 100px;
          margin-bottom: 100px;
          @media (max-width: 991px) {
              margin-top: 70px;
              margin-bottom: 70px;
          }
          @media (max-width: 767px) {
            margin-top: 40px;
            margin-bottom: 40px;
        }
      }
      &__wrapper-title {
          //margin-top: 100px;
          &__title {
              font: 900 45px/50px $f-primary;
              margin-bottom: 70px;
              text-transform: uppercase;
              @media (max-width: 991px) {
                font: 900 26px/32px $f-primary;
                }
            @media (max-width: 767px) {
                font: 900 24px/26px $f-primary;
                margin-bottom: 40px;
            }
          }
          @media (max-width: 991px) {
              margin-top: 70px;
          }
          @media (max-width: 767px) {
            margin-top: 40px;
        }
      }
      &__item--last {
          display: none;
      }

  }

  .page-template-listing-promotion {
      .PageTitle__subtitle {
          @media (max-width: 767px) {
              display: none;
          }
      }
  }

  .tooltip-link-sr {
    border: 0;
    background: 0;
    padding: 0;
  }