.NouvellesItem {
    background: $c-white;
    margin: 0 20px 60px;
    width: 345px;
    border-radius: 12px;
    box-shadow: 0 2px 6px 0 rgba(#000, 0.5);
    overflow: hidden;
    float: left;
    @media (max-width: 1200px) {
        width: 293px;
        margin: 0 15px 60px;
    }
    @media (max-width: 991px) {
        width: 47%;
        margin: 0 3% 60px;
    }
    @media (max-width: 767px) {
        width: 100%;
        margin: 0 0 60px;
    }
    &:first-of-type {
        margin-left: 0;
    }
    &:nth-child(3n) {
        margin-right: 0;
        @media (max-width: 991px) {
            margin-right: 15px;
        }
    }
    &:nth-child(3n+1) {
        margin-left: 0;
        @media (max-width: 991px) {
            margin-left: 15px;
        }
    }
    @media (max-width: 991px) {
        &:nth-child(odd) {
            margin-left: 0;
        }
        &:nth-child(even) {
            margin-right: 0;
            float: right;
        }
    }
    &__top {
        height: 180px;
        overflow: hidden;
        position: relative;
        img {
            width: 122%;
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        ul {
            position: absolute;
            bottom: 10px;
            li {
                background: $c-grey;
                padding: 5px;
                float: left;
                margin: 0 10px 0 0;
                color: $c-white;
                font-size: 12px;
                line-height: 16px;
            }
        }

    }
    &__btm {
        min-height: 300px;
        padding: 20px;
        @media (max-width: 991px) {
            height: 275px;
        }
        @media (max-width: 767px) {
            height: auto;
        }
        h2 {
            font: 700 22px/26px $f-primary;
            margin: 0 0 22px;
            text-transform: none;
            a {
                color: $c-primary;
                transition: all 0.2s $cubic;
                text-decoration: underline;
                &:hover {
                    text-decoration: underline;
                    color: $c-black;
                }
            }
        }
        p {
            color: $c-medium-grey;
            margin: 0 0 22px;
            &.suite {
                .separateur {
                    margin: 0 5px;
                    font-size: 20px;
                }
                a {
                    color: $c-primary;
                    font-weight: 700;
                    transition: $t-primary;
                    &:hover {
                        color: $c-grey;
                        text-decoration: underline;
                        svg {
                            fill: $c-grey;
                        }
                    }
                }
                svg {
                    width: 16px;
                    height: 16px;
                    fill: $c-primary;
                    display: inline-block;
                    position: relative;
                    margin: 0 0 0 5px;
                    top: 3px;
                    transition: $t-primary;
                }
            }
        }
    }
}

.pagination {
    width: 100%;
    text-align: center;
    margin: 45px 0 0;
    clear: both;
    display: inline-block;
    @media (max-width: 991px) {
        margin: 15px 0 0;
    }
    span, a {
        border-radius: 0;
        float: none;
    }
    li {
        font-family: $f-primary;
        margin: 0 5px;
        display: inline-block;
        list-style-type: none;
        width: 29px;
    }
    .pages {
        display: none;
    }
    .current {
        background-color: $c-primary;
        color: $c-white;
        padding: 7px 10px;
        border-radius: 6px;
        font-weight: 700;
        width: 29px;
        height: 29px;
        display: inline-block;
    }
    a, a:focus {
        background-color: #595959;
        color: $c-white;
        padding: 7px 10px;
        border-radius: 6px;
        transition: $t-primary;
        display: inline-block;
        width: 100%;
        height: 29px;
        &:hover, &:focus {
            background-color: $c-primary;
            color: $c-white!important;
            text-decoration: none;
        }
        svg {
            width: 15px;
            height: 15px;
        }
    }
    .next {
        padding: 5px 10px 9px;
        width: 29px;
        height: 29px;
        display: inline-block;
        position: relative;
        top: -2px;
    }
}
