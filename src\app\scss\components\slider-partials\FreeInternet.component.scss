.FreeInternet {
	display: flex;
	align-items: center;
	height: 709px;
	box-shadow: inset 0 5px 30px 0 rgba(#000, 0.45);

	@media (max-width: 1500px) {
		height: 609px;
	}
	@media (max-width: 1199px) {
		height: 355px;
	}
	@media (max-width: 767px) {
		height: 350px;
	}

	&--always-show-subtitle {
		.FreeInternet {
			&__sub-title {
				display: inline-block !important;

				@media (max-width: 767px) {
					font-size: 0;
					margin: -4px 0;

					span {
						font-size: 13px;
					}
				}
			}
		}
	}

	&--fttn50 {
		&.FreeInternet {
			&--reversed {
				.FreeInternet {
					&__col {
						flex: 0 0 auto;
					}
				}
			}
		}

		.FreeInternet {
			&__title {
				@media (min-width: 1760px) {
					line-height: 1.5;
					font-size: 50px;
				}

				@include targetIE() {
					line-height: 1.6;
				}
			}

			&__wrapper {
				@media (max-width: 1199px) {
					justify-content: space-between;

					.FreeInternet__col--content {
						padding: 0;
					}
				}
			}

			&__title-part {
				position: relative;
				&--price {
					margin-left: 8px;

					@include targetIE() {
						bottom: 14px;
					}

					@media (max-width: 1199px) {
						margin-left: 14px;
					}
				}
			}

			&__title-underline {
				@media (min-width: 1760px) {
					top: 78%;
				}

				@include targetIE() {
					top: auto;
					bottom: -30%;
				}
			}

			&__price {
				color: #FFF;
				margin-left: 0;
				text-align: center;

				sup {
					font: 900 24px/1.2 $f-primary;
					top: 0;
				}

				span {
					text-transform: none;
					right: 18px;
					top: 40px;
					font: 400 10px/1 $f-secondary;
				}

				@media (min-width: 1760px) {
					padding: 9px 13px;
					font-size: 60px;
					line-height: 1;
				}
				@media (max-width: 1759px) {
					span {
						right: 6px;
						top: 32px;
					}

					@include targetIE() {
						span {
							right: -4px;
						}

						sup {
							top: 7px;
						}
					}
				}
				@media (max-width: 1199px) {
					padding: 5px 0;
					font-size: inherit;
					line-height: 1;

					sup {
						font: 900 14px/1.2 $f-primary;
						top: 2px;
					}

					span {
						text-transform: none;
						right: 2px;
						top: 20px;
						font: 400 6px/1 $f-secondary;
					}
				}
			}

			&__promo-duration {
				&:after {
					left: auto;
					right: 95%;
					bottom: 55%;

					@media (max-width: 1199px) {
						content: none;
					}
				}
			}
		}
	}

	&--reversed {
		.FreeInternet__wrapper {
			flex-direction: row-reverse;
			width: auto;
			max-width: none;
			@media (max-width: 767px) {
				flex-direction: column;
			}
		}
		.FreeInternet__col {
			flex: 0 0 45%;
			@media (max-width: 1199px) {
				flex: 0 0 45%
			}
		}
	}

	&__wrapper {
		display: flex;
		width: 100%;
		max-width: 1200px;
		margin: 0 auto;

		@media (max-width: 991px) {
			max-width: 970px;
		}
		@media (max-width: 767px) {
			flex-direction: column;
		}
	}

	&__col {
		flex: 0 0 50%;
		display: flex;
		flex-direction: column;
		justify-content: center;

		&--content {
			padding: 35px 0 35px 35px;

			@media (max-width: 767px) {
				padding: 15px 0 0;
			}
		}

		&--image {
			align-items: center;
		}
	}

	&__main-img.FreeInternet__main-img.FreeInternet__main-img {
		width: 100%;
		height: auto;
		max-width: 602px;
		transform: translateY(-40px);
		opacity: 0.0001;
		will-change: transform, opacity;
		transition: all 0s linear 0.25s;

		@media (max-width: 1199px) {
			width: 317px;
		}
		@media (max-width: 767px) {
			width: 160px;
		}
	}

	&__content-wrapper {
		.FreeInternet {
			&__title, &__sub-title, &__btn, &__disclaimer {
				opacity: 0;
				transform: translateX(50%);
				transition: all 0s linear;
			}
		}

		> span {
			display: block;
		}
	}

	&__title-container {
		margin-bottom: 30px;

		@media (max-width: 767px) {
			margin-bottom: 20px;
		}
	}

	&__title {
		font: 900 50px/60px $f-primary;
		text-transform: uppercase;

		&:after {
			content: '';
			display: block;
			width: 38px;
			height: 4px;
			background: $c-light-grey;
			border-radius: 2px;
			margin: 21px 0 5px;
		}

		@media (max-width: 1500px) {
			font: 900 40px/50px $f-primary;
		}
		@media (max-width: 1199px) {
			font: 900 24px/30px $f-primary;
			
			&:after {
				margin-top: 10px;
			}
		}
		@media (max-width: 767px) {
			font: 900 20px/28px $f-primary;

			&:after {
				display: none;
			}
		}
	}

	&__title-part {
		display: inline-block;
		white-space: nowrap;

		&--flashing-bubble {			
			&.is-doodled.is-doodled--highlight-white {
				display: table;
				margin: 0 0 8px -20px;

				&:before {
					top: 50%;
					left: 48%;
					width: 101%;
					height: 101%;
				}
	
				@media (max-width: 767px) {
					position: absolute;
					left: 55%;
					top: -118%;

					&:before {
						top: 49%;
						left: 48%;
						width: 98%;
						height: 98%;
					}
				}
			}

			.FreeInternet {
				&__promo-duration {
					display: block;
					padding: 24px;
					white-space: normal;
					margin: 0;

					&:before {
						content: none;
					}

					&:after {
						bottom: auto;
						top: 61%;
						left: 94%;
						transform: rotateX(0deg) rotateZ(56deg);
						width: 40px;
						height: 40px;
					}

					@media (max-width: 1500px) {
						padding: 18px 20px;
					}
					@media (max-width: 1199px) {
						padding: 16px;

						&:after {
							bottom: auto;
							top: 60%;
							left: 92%;
							transform: rotateX(0deg) rotateZ(56deg);
							width: 28px;
							height: 28px;
						}
					}
					@media (max-width: 767px) {
						width: 145px;
						padding: 16px 12px 20px;

						&:after {
							top: 60%;
							left: 85%;
						}
					}
				}
			}
		}

		&--main-top {
			position: relative;

			@media (max-width: 767px) {
				margin-bottom: 5px;
			}
		}

		&--main-bottom {
			display: block;
			font: 900 30px/40px "BrandonGrotesque", sans-serif;
			@media (max-width: 767px) {
				font: 900 18px/24px "BrandonGrotesque", sans-serif;
			}
		}
	}

	&__flashing-bubble-title {
		display: block;
		text-align: center;
		text-transform: none;
		font: 400 18px/26px $f-secondary;

		@media (max-width: 1500px) {
			font: 14px/20px $f-secondary;
		}
		@media (max-width: 1199px) {
			font: 12px/18px $f-secondary;
		}
		@media (max-width: 767px) {
			font: 10px/14px $f-secondary;
		}
	}

	&__title-top {
		@media (max-width: 1750px) {
			white-space: normal;
		}
	}

	&__promo-duration {
		position: relative;
		display: inline-flex;
		align-items: center;
		padding: 7px 14px 10px 21px;
		margin-left: -18px;

		&:before, &:after {
			content: '';
			position: absolute;
			display: block;
			background: {
				size: 100% 100%;
				repeat: no-repeat;
				position: center;
			};
		}

		&:before {
			top: 50%;
			left: 50%;
			width: 100%;
			height: 100%;
			background-image: url('~@app/img/drawn-circle.svg');
			transform: translate(-50%, -50%) scale(0.7) rotate(-10deg);
			opacity: 0;
			transition: 0.5s $cubicElastic 0.75s;
		}
		
		&:after {
			bottom: 70%;
			left: 90%;
			background-image: url('~@app/img/round-arrow.svg');
			width: 48px;
			height: 48px;
			transform: rotate(-22deg);

			@media (max-width: 1199px) {
				width: 32px;
				height: 32px;
				bottom: 60%;
			}
			@media (max-width: 767px) {
				bottom: 50%;
			}
		}

		&-time {
			font: 900 56px/1.2 $f-primary;
			margin-right: 6px;

			@media (max-width: 1500px) {
				font: 900 48px/1.2 $f-primary;
			}
			@media (max-width: 1199px) {
				font: 900 30px/1.2 $f-primary;
			}
			@media (max-width: 767px) {
				font: 900 20px/1.2 $f-primary;
			}
		}
		&-unit {
			font: 400 40px/79px $f-secondary;
			text-transform: none;

			@media (max-width: 1500px) {
				font: 400 32px/60px $f-secondary;
			}
			@media (max-width: 1199px) {
				font: 400 12px/24px $f-secondary;
			}
		}

		@media (max-width: 1199px) {
			padding: 4px 12px 4px 18px;
		}
		@media (max-width: 767px) {
			padding: 4px 8px 4px 10px;
		}
	}

	&__title-underline {
		position: absolute;
		width: 100%;
		height: auto;
		top: 85%;
		left: 0;
		stroke: #C7C7C7;
		stroke-dashoffset: -468px;
		stroke-dasharray: 468px;
		transition: all 0s linear 0.25s;

		@media (max-width: 1199px) {
			top: 80%;
		}
	}

	&__sub-title {
		margin-top: 5px;
		font: 600 30px/43px $f-primary;
		color: $c-light-grey;
		//white-space: nowrap;

		.bold {
			font-weight: 700;
		}

		.white {
			color: $c-white;
		}

		.is-doodled.is-doodled--underlined {
			&:after {
				height: 14%;
			}
		}

		.small.small.small {
			display: inherit;
			color: $c-light-grey;
			line-height: inherit;
			font-size: 16px;
		}

		@media (max-width: 1199px) {
			font: 600 16px/23px $f-primary;
		}
		@media (max-width: 767px) {
			//display: none;
		}
	}

	&__btn {
		@media (min-width: 1025px) {
			&:after {
				background: #FFF;
			}

			&:hover {
				color: $c-primary;
				transition-delay: 0s !important;
			}
		}
	}

	&__disclaimer {
		display: block;
		padding: 0 16px 0 0;
		margin-top: 15px;
		color: $c-light-grey;
	}

	@at-root {
		.textured-bg--red {
			.FreeInternet {
				&__pre-title,
				&__title,
				&__sub-title,
				&__disclaimer {
					color: #FFF !important;
				}

				&__title-underline {
					stroke: #661618;
				}

				&__btn {
					color: $c-primary;

					&:before {
						background: #FFF;
					}
				}
			}
		}
	}
}


// Animation on slide active
.owl-item {
	&.active {
		.FreeInternet {
			&__promo-duration {
				transform: scale(1) rotate(0deg);
				opacity: 1;

				&:before {
					transform: translate(-50%, -50%) scale(1) rotate(0deg);
					opacity: 1;
				}
			}

			&__content-wrapper {
				.FreeInternet {
					&__title, &__sub-title, &__btn, &__disclaimer {
						opacity: 1;
						transform: translateX(0);

						@for $i from 1 through 5 {
							&:nth-child(#{ $i }) {
								$delay: #{ (($i - 1) * 0.1s) + 0.25 };

								transition: transform 0.4s $cubic $delay,
											opacity 0.4s $cubic $delay,
											color 0.25s $cubic 0s;
							}
						}
					}
				}
			}

			&__title-underline {
				stroke-dashoffset: 0;
				stroke-dasharray: 468px;
				transition: all 0.3s $cubic 1s;
			}

			&__main-img {
				transform: translateY(0);
				opacity: 1;
				transition: 0.7s $cubic 1s;
			}

			&--reversed {
				.FreeInternet__main-img.FreeInternet__main-img.FreeInternet__main-img {
					transform: translate(30px,0);
					@media (max-width: 1199px) {
						transform: translate(0);
					}
				}
			}
		}
	}
}

.slideBizz {
	.FreeInternet__title-part--main-top {
		svg {
			display: none;
		}
	}
	.FreeInternet__title-part--main-bottom {
		font: inherit!important;
	}
	.green {
		color: #00C265;
		span {
			position: relative;
			&:after {
				content: '';
				position: absolute;
				width: 100%;
				height: 5px;
				right: 0;
				bottom: 7px;
				background: url('~@app/img/stroke-underline-bizz.svg') center center no-repeat;
				@media (max-width: 1199px) {
					height: 3px;
					bottom: 3px;
				}
			}
		}
	}
}