.TwoCol {
    width: 100%;
    padding: 100px 0;
    display: inline-block;
    position: relative;
    .col-md-12  {
        @media (max-width: 991px) {
            padding: 0 21px
        }
        @media (max-width: 767px) {
            padding: 0 15px
        }
    }
    h3 {
        margin: 0 0 40px;
    }
    a {
        color: $c-primary;
        &:focus,
        &:hover {
            text-decoration: underline;
        }
    }
    .TwoCol__left {
        width: 50%;
        float: left;
        padding: 0 30px 0 15px;
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 21px 40px;
        }
        @media (max-width: 767px) {
            padding: 0 15px 40px;
        }

        p {
            display: flex;
            align-items: center;
            text-align: left;

            img {
                padding-right: 20px;
            }
        }
    }

    .TwoCol__right {
        width: 50%;
        float: left;
        padding: 0 15px 0 30px;
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 21px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }

        p {
            //display: flex;
            flex-direction: row-reverse;
            align-items: center;
            text-align: left;
            justify-content: start;

            img {
                margin-right: 20px;
            }
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    @media (max-width: 767px) {
        padding: 40px 0;
    }
}
