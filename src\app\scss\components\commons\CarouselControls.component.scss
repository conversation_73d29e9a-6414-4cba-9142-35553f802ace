.CarouselControls {
	position: relative;
	
	&--full-width {
		display: flex;
		width: 100%;
		justify-content: space-between;
	}

	&__control {
		appearance: none;
		background: none;
		padding: 0;
		border: none;
		margin-right: 24px;
		outline: none;
		pointer-events: all;

		&.disabled {
			opacity: 0.3;
		}
		
		&:last-child {
			margin-right: 0;
		}
		
		&--prev {
			transform: rotate(0deg);
		}

		&--next {
			transform: rotate(180deg);
		}

		&-icon {
			display: inline-block;
			width: 32px;
			height: 32px;
			fill: $c-light-grey;
		}
	}
}