.page-template-page-routeurs {
    background-image: url('../../../img/Body.jpg');
    position: relative;

    .TwoCol-LeftImgRightText__leftImg {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        img {
            @media (max-width: 991px) {
                position: relative;
            }
        }
    }

    .TwoCol-LeftImgRightText__rightTxt {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
    }

    .TwoCol-LeftTextRightImg__rightImg {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        img {
            @media (max-width: 991px) {
                position: relative;
                transform: translate(-50%) !important;
                left: 50%;
            }
        }
    }

    .TwoCol-LeftTextRightImg__leftTxt {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
    }
}

.RouteurFirstSlide {
    padding: 100px 0;
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    &__title {
        @extend h2;
        text-align: center;
        color: $c-white;
        float: left;
        width: 100%;
    }
    &__subtitle {
        font: 40px/50px $f-secondary!important;
        text-align: center;
        color: $c-white;
        float: left;
        width: 100%;
        text-transform: none;
        margin: 0 0 62px;
        @media (max-width: 991px) {
            font: 22px/30px $f-secondary!important;
            margin: 0 0 30px;
        }
    }
    .TwoCol-LeftImgRightText__leftImg {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        img {
            @media (max-width: 991px) {
                position: relative;
            }
        }
    }
    .TwoCol-LeftImgRightText__rightTxt {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        padding: 0 15px 0 0;
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 15px
        }
    }
}

.RouteurPrice {
    width: 100%;

    min-height: 240px;
    background: url('../../../img/grey-pattern.png') left top repeat;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    @media (max-width: 767px) {
        height: auto;
    }
    &__achat {
        width: 63%;
        min-height: 240px;
        align-items: center;
        float: left;
        background: url('../../../img/half_box.png') right top;
        display: flex;
        color: $c-white;
        text-align: center;
        @media (max-width: 767px) {
            width: 100%;
            background: url('../../../img/half_box--vertical-large.png') center bottom;
        }
        h4 {
            margin: 0 0 33px;
            @media (max-width: 767px) {
                width: 49%;
                margin: 0;
                display: inline-block;
                vertical-align: middle;
            }
        }
        .Listing__prix {
            font-size: 100px;
            line-height: 100px;
            letter-spacing: -2.17px;
            @media (max-width: 767px) {
                font-size: 55px;
            }
            sup {
                font-size: 35px;
                line-height: 35px;
                letter-spacing: 0;
                margin: 0 0 0 8px;
                @media (max-width: 767px) {
                    font-size: 25px;
                    line-height: 30px;
                }
            }
        }
        .Listing__recap {
            @media (max-width: 767px) {
                padding: 30px 25px 50px!important;
            }
        }
    }
    &__location {
        width: 37%;
        min-height: 240px;
        align-items: center;
        float: left;
        display: flex;
        color: $c-white;
        text-align: center;
        @media (max-width: 767px) {
            width: 100%;
        }
        h4 {
            margin: 0 0 33px;
            @media (max-width: 767px) {
                width: 49%;
                margin: 0;
                display: inline-block;
                vertical-align: middle;
            }
        }
        .Listing__prix {
            font-size: 80px;
            line-height: 100px;
            letter-spacing: -1.74px;
            @media (max-width: 767px) {
                font-size: 70px;
            }
            sup {
                font-size: 24px;
                line-height: 35px;
                letter-spacing: 0;
                margin: 0 0 0 8px;
            }
        }
    }
    .Listing__recap {
        flex: 0 0 100%;
        @media (max-width: 991px) {
            border: none
        }
        @media (max-width: 767px) {
            padding: 15px 25px 20px;
            display: block;
            text-align: left;
        }
    }
    .Listing__recap-content {
        @media (max-width: 767px) {
            left: 50%;
            width: auto;
            display: inline-block;
            position: relative;
            transform: translateX(-50%);
        }
    }
    .Listing__recap-wrapper {
        @media (max-width: 767px) {
            width: 49%;
            display: inline-block;
            vertical-align: middle;
        }
    }
    .Listing__prix-wrapper {
        text-align: center;
        display: block;
    }
    .Listing__prix {
        color: $c-white;
        span {
            right: 31px;
            top: 46px;
            font-size: 12px;
            @media (max-width: 1200px) {
                right: 15px;
            }
            @media (max-width: 991px) {
                right: 0;
            }
            @media (max-width: 767px) {
                right: auto;
                left: 43px;
            }
        }
        span:lang(en) {
            right: 16px;
            top: 46px;
            font-size: 12px;
            @media (max-width: 1200px) {
                right: 0;
            }
            @media (max-width: 991px) {
                right: 0;
            }
            @media (max-width: 767px) {
                right: auto;
                left: 78px;
            }
        }
    }
} 


.RouteurInfos {
    background: #333333;
    padding-top: 100px;
    @media (max-width: 991px) {
        padding-top: 70px;
    }
    &__title {
        @extend h2;
        text-align: center;
        color: $c-white;
        float: left;
        width: 100%;
    }
    &__subtitle {
        font: 40px/50px $f-secondary!important;
        text-align: center;
        color: $c-white;
        float: left;
        width: 100%;
        text-transform: none;
        margin: 0;
        @media (max-width: 991px) {
            font: 22px/30px $f-secondary!important;
        }
    }

    .TwoCol-LeftImgRightText__leftImg {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        img {
            @media (max-width: 991px) {
                position: relative;
            }
        }
    }

    .TwoCol-LeftImgRightText__rightTxt {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        h3 {
            color: $c-white;
        }
        p {
            color: $c-white;
        }
    }

    .TwoCol-LeftTextRightImg__rightImg {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        img {
            @media (max-width: 991px) {
                position: relative;
                transform: translate(-50%) !important;
                left: 50%;
            }
        }
        video {
            max-width: 100%;
            border: 4px solid #000;
        }
    }

    .TwoCol-LeftTextRightImg__leftTxt {
        width: 49.5%;
        float: none;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
        }
        h3 {
            color: $c-white;
        }
        p {
            color: $c-white;
        }
    }
}

.RouteurOptions {
    padding: 100px 0;
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    &__title {
        @extend h2;
        text-align: center;
        color: $c-white;
        width: 100%;
    }
    &__subtitle {
        font: 40px/50px $f-secondary!important;
        text-align: center;
        color: $c-white;
        width: 100%;
        text-transform: none;
        margin: 0 0 88px;
        @media (max-width: 991px) {
            font: 22px/30px $f-secondary!important;
        }
    }
    .ModernGallery {
        width: 49.5%;
        max-width: 570px;
        display: inline-block;
        vertical-align: middle;
        @media (max-width: 991px) {
            width: 100%;
            left: 50%;
            position: relative;
            transform: translateX(-50%);
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
        .ModernGallery__controls {
            width: auto;
            @media (max-width: 991px) {
                transform: translate(-50%, -50%)!important;
                padding: 0;
            }
            &:focus {
                outline: 2px solid blue;
                outline-offset: 2px;
            }
        }
    }
    &__list {
        width: 49.5%;
        display: inline-block;
        vertical-align: middle;
        padding: 0 0 0 60px;
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 19px
        }
        @media (max-width: 767px) {
            padding: 0 15px
        }
        ul {
            margin: 0 0 40px;
            li {
                background: url('../../../img/ic_check-white.svg') no-repeat left 2px;
                background-size: 16px 16px;
                color: $c-white;
            }
        }
        p {
            color: $c-white;
        }
    }
}

/*BACKUP ROUTEURS*/
.Routeurs {
    padding: 100px 0;
    .Wysiwyg {
        h2 {
            margin: 0 0 25px;
        }
        p {
            &:last-of-type {
                margin-bottom: 0;
            }
        }
    }
    .SinglePrice {
        padding: 28px 0 14px;
        height: auto;
        .Listing__recap {
            flex: 0 0 150px;
        }
        .Listing__apartir {
            text-align: center;
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0;
    }
}

.Wysiwyg {
    .img-routeur {
        max-width: 100%;
    }
}

.page-template-page-routeurs {
    .CardListing {
        .CardItem {
            .CardItem__subTitle {
                @media (max-width: 767px) {
                    margin: 10px 0 0 0;
                    line-height: 16px;
                }
            }
        }
    }
}




.ModernGallery__control {
    &:focus {
        outline: 2px solid blue;
        outline-offset: 2px;
        z-index: 2;
        position: relative;
    }
}

.RouteurOptions {
    .ModernGallery {
        margin-bottom: 60px;
    }
}