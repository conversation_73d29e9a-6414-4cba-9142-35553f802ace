body.page-template-ebox-tv-landing-php, body.page-template-ebox-tv-landing-copy-php {
	@media (min-width: 768px) {
		.container {
			padding-left: 15px !important;
			padding-right: 15px !important;
		}
	}


	.HomeForfaits {
		background: #343434;
		height: auto;
		width: 100%;
		padding: 120px 0;
		display: block;

		@media (max-width: 1199px) {
			padding: 60px 0;
		}
		@media (max-width: 767px) {
			padding: 60px 0 60px;
			margin: 40px 0 0 0;
		}

		&__container {
			position: relative;
			display: flex;

			@media (max-width: 1199px) {
				width: 100%;
				padding: 0 !important;
			}
		}

		&__tv {
			@media (min-width: 1200px) {
				position: absolute;
				width: calc(100% - 60px);
				height: 100%;
				z-index: 1;
				pointer-events: none;

				.HomeForfaits__devices-image {
					width: 100%;
					max-width: 100%;
					transition: none;
					max-height: 100%;
					display: block;
					margin: 0 auto;
				}
			}
			@media (max-width: 1199px) {
				margin-left: -25%;
				flex: 1 1 50%;

				.HomeForfaits__devices-image {
					width: 100%;
					max-width: 100% !important;
				}
			}
		}

		&__deviceslider {
			margin: 0 0 0 10px;

			@media (max-width: 1199px) {
				margin-top: 30px !important;
			}
		}
		&__deviceslider li {
			text-align: center;
		}
		&__deviceslider img {
			width: auto !important;
			margin: 0px auto !important;
		}
		&__deviceslider p {
			color: #ffffff;
			font: 400 16px/16px Gloria Hallelujah !important;
			float: left;
			width: 100%;
			text-align: center;
		}
		&__deviceslider span {
			color: #ffffff;
			font: 700 16px/16px $f-primary !important;
			text-transform: uppercase;
			width: 100%;
			text-align: center;
			margin-top: 10px;
		}
		&__devices {
			position: relative;
			flex: 0 0 auto;
			
			&-image {
				position: relative;
				left: auto !important;
				bottom: auto !important;
				transform: translateX(0);
				transition: $t-primary;

				@media (max-width: 1199px) {
					max-width: 439px!important;
					height: auto!important;
				}
				@media (max-width: 1100px) {
					max-width: 239px!important;
					height: auto!important;
				}
				@media (max-width: 767px) {
					max-width: 206px!important;
					height: auto!important;
				}
			}
		}
		@media (min-width: 767px) and (max-width: 1199px) {
			&__deviceslider button.owl-prev {
				float: left;
				position: absolute;
				top: 30%;
				background: transparent;
				border: none;
			}
			&__deviceslider button.owl-prev span {
				font-size: 60px !important;
				font-weight: normal !important;
			}
			&__deviceslider button.owl-next {
				float: right;
				position: absolute;
				top: 30%;
				right: 45px;
				background: transparent;
				border: none;
			}
			&__deviceslider button.owl-next span {
				font-size: 60px !important;
				font-weight: normal !important;
			}
		}
		@media (max-width: 767px) {
			&__deviceslider button.owl-prev {
				float: left;
				position: absolute;
				top: 30%;
				left: 15px;
				background: transparent;
				border: none;
			}
			&__deviceslider button.owl-prev span {
				font-size: 60px !important;
				font-weight: normal !important;
			}
			&__deviceslider button.owl-next {
				float: right;
				position: absolute;
				top: 30%;
				right: 45px;
				background: transparent;
				border: none;
			}
			&__deviceslider button.owl-next span {
				font-size: 60px !important;
				font-weight: normal !important;
			}
		}
		&__devices {
			float: left;
			margin: 0 60px 0 0;
			flex: 0 0 30%;
			@media (max-width: 1100px) {
				margin: 0;
				flex: 0 0 25%;
				min-width: 175px;
			}
			@media (max-width: 660px) {
				min-width: 1px;
			}
		}
		&__devices>img {
			width: 100%;

			@media (max-width: 660px) {
				width: 150%;
				transform: translate(-30%);
			}
		}
		&__slider {
			float: left;

			&--main {
				padding: 30px;
				
				@media (min-width: 1200px) {
					flex: 1 0 100%;
					padding: 30px 0 150px 22%;
				}
				@media (max-width: 1199px) {
					flex: 1 1 75%;
				}
				@media (max-width: 1100px) {
					padding: 0 30px;
				}
				@media (max-width: 600px) {
					padding: 0 15px;
				}
				@media (max-width: 400px) {
					padding: 0 0px;
				}
			}
		}
		&__deviceslider {
			display: none;
			overflow: hidden;

			@media (max-width: 1199px) {
				position: relative;
				display: block;
				margin: 0 auto;
			}

			.HomeForfaits {
				&__pictos {
					&--item {
						img {
							width: 35px;
							height: 35px;
							margin-bottom: 20px !important;
						}

						> p, > span {
							font-size: 16px !important;
							line-height: 1.4 !important;
						}
					}
				}
			}
		}
		
		&__slider-nav {
			width: 32px;
			height: 32px;
			fill: $c-light-grey;
		}

		&__forfait {
			width: 100%;

			&--title {
				font: 900 50px/70px $f-primary !important;
				color: #ffffff;
				text-transform: uppercase;

				@media (min-width: 1770px) {
					font: 900 75px/90px $f-primary !important;
					color: #ffffff;
					text-transform: uppercase
				}
				@media (max-width: 1769px) {
					font: 900 4vw/1.4 $f-primary !important;
					color: #ffffff;
					text-transform: uppercase
				}
				@media (min-width: 768px) and (max-width: 1199px) {
					font: 900 44px/60px "BrandonGrotesque", sans-serif!important;
				}
				@media (max-width:767px) {
					font: 900 32px/1.4 "BrandonGrotesque", sans-serif!important;
				}
				@media (max-width: 550px) {
					font: 900 24px/40px "BrandonGrotesque", sans-serif!important;
				}
			}

			&--title-abitibi{
				font: 900 50px/70px $f-primary !important;
				color: #ffffff;
				text-transform: uppercase;

				@media (min-width: 1770px) {
					font: 900 68px/90px $f-primary !important;
					color: #ffffff;
					text-transform: uppercase
				}
				@media (max-width: 1769px) {
					font: 900 4vw/1.4 $f-primary !important;
					color: #ffffff;
					text-transform: uppercase
				}
				@media (min-width: 768px) and (max-width: 1199px) {
					font: 900 44px/60px "BrandonGrotesque", sans-serif!important;
				}
				@media (max-width:767px) {
					font: 900 32px/1.4 "BrandonGrotesque", sans-serif!important;
				}
				@media (max-width: 550px) {
					font: 900 24px/40px "BrandonGrotesque", sans-serif!important;
				}
			}
			
			&--title>.is-doodled--splashed-revolution:before {
				width: 110%;
				height: 110%;
				top: 52%;
				@media (max-width: 767px) {
					top: 50%;
					width: 100%;
					height: 100%;
				}
			}

			@media (max-width: 767px) {
				align-items: flex-start;
				padding: 0 0 0 30px;
			}
			@media (max-width: 660px) {
				padding: 0 0 0 10px;
			}
			@media (max-width: 449px) {
				padding: 0 0 0 20px;
			}
			@media (max-width: 369px) {
				padding: 10px 0 0 20px;
			}
		}
		&__pictos {
			display: flex;
			justify-content: space-between;
			width: 100%;
			margin: 50px -15px 0;
			padding: 0;

			@media (max-width: 1199px) {
				display: none;
			}

			&--item {
				display: inline-block;
				text-align: center;
				width: 23%;

				@media (max-width: 1750px) {
					width: 24%;
				}
				@media (max-width: 1199px) {
					width: auto;
				}
			}
			&--item p {
				font: 400 20px/38px Gloria Hallelujah !important;
				color: #ffffff;
			}
			&--item span {
				font: 700 25px/30px $f-primary !important;
				color: #ffffff;
				text-transform: uppercase;
			}
		}

		&__picto-link {
			display: block;
			padding: 15px 0 20px;
			border-radius: 12px;
			background: $c-grey;
			box-shadow: 0 0 0 0 rgba(#000, 0);
			transition: box-shadow 0.2s $cubic;

			&:hover {
				box-shadow: 0 2px 14px 0 rgba(#000, 0.5);
			}

			@media (max-width: 1600px) {
				&.HomeForfaits__picto-link.HomeForfaits__picto-link.HomeForfaits__picto-link {
					p {
						font: 400 15px/25px Gloria Hallelujah !important;
					}
					span {
						font: 700 16px/20px "BrandonGrotesque", sans-serif !important;
					}
				}
			}
		}

		&__pictos-icon {
			display: inline-block;
			margin: 0px auto 20px;
			width: 60px;
			height: 60px;
			fill: #FFF;
		}
	}

	.HomeControl {
		background: #343434;
		height: auto;
		width: 100%;
		display: block;

		&__consec {
			border-top: 2px solid #D8D8D8;
			padding: 120px 0;
			float: left;
			width: 100%;

			@media (max-width: 1199px) {
				padding: 60px 0;
			}
		}
		&__consectitle {
			font: 900 45px/50px "BrandonGrotesque", sans-serif!important;
			padding: 80px 40px 0 0;
			color: #ffffff;
			@media (max-width: 1199px) {
				font: 900 26px/32px "BrandonGrotesque", sans-serif!important;
				padding: 80px 40px 10px 0;
			}
			@media (max-width: 767px) {
				font: 900 22px/24px "BrandonGrotesque", sans-serif!important;
				padding: 0px 40px 10px 0;
			}
		}
		&__consecdesc {
			font: 400 16px/22px "BrandonGrotesque", sans-serif!important;
			color: #ffffff;
		}
		&__consec .text-right>img {
			@media (max-width: 1199px) {
				width: auto;
				max-width: 100%;
			}
			@media (max-width: 767px) {
				max-width: 90%;
			}
		}
		&__pictos {
			float: left;
			width: 100%;
			margin: 50px 0 0 0;
			padding: 0;
			@media (max-width: 1199px) {
				display: none;
			}
			&--item {
				float: left;
				text-align: center;
				width: auto;
				margin-right: 60px;
			}
			&--item>img {
				margin: 0px auto 20px;
				min-height: 59px;
			}
			&--item>p {
				font: 400 20px/38px Gloria Hallelujah !important;
				color: #ffffff;
			}
			&--item>span {
				font: 700 25px/30px $f-primary !important;
				color: #ffffff;
				text-transform: uppercase;
			}
		}

		&__remote-img {
			max-width: 433px;
			display: inline-block;
			
			@media (max-width: 767px) {
				display: block;
				max-height: 320px;
				margin: 30px auto 0;
			}
		}
	}

	.owl-carousel {
		margin: 0 !important;
		padding: 0 30px;
		opacity: 1;
		
		.owlslider {
			display: inline-block;
			margin-top: 30px;
		}

		// .owl-stage {
		// 	display: flex;
		// 	min-width: 100% !important;
		// 	justify-content: space-between;
		// }

		.owl-item {
			text-align: center;
		}

		.owl-prev, .owl-next {
			position: absolute;
			top: 50%;
			cursor: pointer;
			z-index: 1;
		}

		.owl-prev {
			left: 0;
			transform: translateY(-50%);
		}

		.owl-next {
			right: 0;
			transform: translateY(-50%) rotate(180deg);
		}
	}
}