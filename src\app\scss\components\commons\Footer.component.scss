.Footer {
    position: relative;
    overflow-y: hidden;
    padding: 30px 0;
    background: #000;
    z-index: 10;

    &:before {
        width: 100%;
        height: 100%;
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 0;
        //background: url('../../../img/<EMAIL>') center center repeat;
        background-size: 1084px 213px;
        opacity: 0.03;
    }
    &__fournisseur {
        width: 100%;
        padding-bottom: 15px;
        border-bottom: 2px solid $c-grey;
        p {
            margin: 0;
            font: 400 16px/22px $f-primary;
            color: $c-white;
        }
        @media (max-width: 767px) {
            text-align: center;
        }
    }
    &__menu {
        padding: 15px 0;
        border-bottom: 2px solid $c-grey;
        &-nav {
            margin: 0;
            padding: 0;
            &__item {
                display: inline-block;
                color: $c-white;
                font: 400 16px/32px $f-primary;
                padding-right: 40px;
                @media (max-width:767px) {
                    &:nth-child(1),
                    &:nth-child(2),
                    &:nth-child(3) {
                        //display: none;
                    }
                }
                &:last-of-type {
                    padding-right: 0;
                }
            }
            &__link {
                color: $c-white;
                font: 400 16px $f-primary;
                transition: $t-primary;
                &:hover,&:focus {
                    color: $c-white;
                    text-decoration: underline;
                }

            }
            @media (max-width: 767px) {
                text-align: center;
            }

        }
        @media (max-width: 345px) {
            .col-xs-7,
            .col-xs-5 {
                width: 100%;
                text-align: center;
            }
        }
    }
    &__menu-left {
        width: 70%;
        @media (max-width: 767px) {
            width: 100%;
        }
        @media (max-width: 500px) {
            width: 100%;
            text-align: center;
        }
    }
    &__menu-right {
        width: 30%;
        @media (max-width: 767px) {
            width: 50%;
        }
        @media (max-width: 500px) {
            width: 100%;
            text-align: center;
        }
    }
    &__menu-social {
        margin: 0;
        padding: 0;
        height: 32px;
        svg {
            width: 32px;
            height: 32px;
            fill: $c-white;
            transition: $t-primary;
        }
        &__item {
            line-height: 1;
            display: inline-block;
        }
        &__link {
            margin: 0;
            padding: 0;
            height: 100%;
            display: inline-block;
            &:hover {
                svg {
                    fill: $c-medium-grey;
                }
            }
        }
    }
    &__copyright {
        padding-top: 15px;
        span {
            padding: 0 12px;
        }

        p {
            font: 400 12px/16px $f-primary;
            color: $c-white;
        }
        a {
            color: $c-white;
            &:hover {
                text-decoration: underline;
            }
            &:focus {
                color: #FFF;
                text-decoration: underline;
            }
        }
        @media (max-width: 767px) {
            text-align: center;
        }
    }

    &__hard-link, &__hard-link-sep {
        display: inline-block;
    }

    &__hard-link-sep {
        color: $c-medium-grey;
    }

    &__hard-link.Footer__hard-link.Footer__hard-link {
        font-size: 12px;

        &:hover {
            color: $c-light-grey;
            text-decoration: underline;
            transition: color 0.1s linear;
        }

        &--active {
            color: #FFF;

            &:hover {
                color: #FFF;
            }
        }
    }

    &__sticky-tab.StickyTab {
        position: absolute;
        right: 85px;
        z-index: 10;
    }
}


/**
 * HubSpot Form Legacy Styles
 * Styles personnalisés pour les formulaires HubSpot Legacy
 * Compatible avec les classes et structures HubSpot standard
 */

/* ==========================================================================
   RESET ET BASE
   ========================================================================== */

/* Reset pour les formulaires HubSpot */
.hs-form {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    background: white !important;
    padding: 0 20px !important;
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* ==========================================================================
   LABELS
   ========================================================================== */

.hs-form-field label {
    color: #2c2c2c !important;
    font-weight: 700 !important;
    font-size: 28px !important;
    margin-bottom: 16px !important;
    display: block !important;
    line-height: 1.2 !important;
}

/* ==========================================================================
   CHAMPS DE SAISIE
   ========================================================================== */

/* Styles de base pour tous les inputs */
.hs-input,
.hs-form input[type="text"],
.hs-form input[type="email"],
.hs-form input[type="tel"],
.hs-form input[type="url"],
.hs-form input[type="number"],
.hs-form textarea,
.hs-form select {
    border: 1px solid #ABB0B2!important;
    border-radius: 3px !important;
    padding: 16px !important;
    font-size: 16px !important;
    background: white !important;
    transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
    box-sizing: border-box !important;
    width: 100% !important;
    font-family: inherit !important;
    width: 100% !important;
    height: 40px !important;
}

/* États focus pour les inputs */
.hs-input:focus,
.hs-form input[type="text"]:focus,
.hs-form input[type="email"]:focus,
.hs-form input[type="tel"]:focus,
.hs-form input[type="url"]:focus,
.hs-form input[type="number"]:focus,
.hs-form textarea:focus,
.hs-form select:focus {
    border-color: #9ca3af !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1) !important;
}

/* Textarea spécifique */
.hs-form textarea {
    min-height: 120px !important;
    resize: vertical !important;
}

/* Select spécifique */
.hs-form select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'><path d='M7 10l5 5 5-5z'/></svg>") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 16px !important;
    padding-right: 40px !important;
}

/* ==========================================================================
   DISPOSITION DES CHAMPS
   ========================================================================== */

/* Container des champs */
.hs-form-field {
    margin-bottom: 24px !important;
}

.hs-form-field:last-child {
    margin-bottom: 0 !important;
}

/* Disposition en ligne pour email + bouton (si applicable) */
.hs-form-field.hs-form-field-inline {
    display: flex !important;
    align-items: flex-end !important;
    gap: 16px !important;
}

.hs-form-field.hs-form-field-inline .hs-input {
    flex: 1 !important;
    margin-bottom: 0 !important;
}

.hs-form-field.hs-form-field-inline .hs-button {
    flex-shrink: 0 !important;
    margin-top: 0 !important;
}

/* ==========================================================================
   BOUTONS
   ========================================================================== */

/* Bouton principal */
.hs-button,
.hs-form input[type="submit"],
.hs-form button {
    background: #BE2323 !important;
    width: 100% !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    font-weight: 700 !important;
    font-size: 12px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease, transform 0.1s ease !important;
    font-family: inherit !important;
    display: inline-block !important;
    text-decoration: none !important;
    margin-bottom: 10px;
    height: 40px;
    max-width: 118px;
}

/* État hover du bouton */
.hs-button:hover,
.hs-form input[type="submit"]:hover,
.hs-form button:hover {
    background: #b91c1c !important;
    transform: translateY(-1px) !important;
}

/* État actif du bouton */
.hs-button:active,
.hs-form input[type="submit"]:active,
.hs-form button:active {
    transform: translateY(0) !important;
}

/* ==========================================================================
   MESSAGES D'ERREUR
   ========================================================================== */

.hs-error-msgs,
.hs-form .hs-error-msg {
    color: #dc2626 !important;
    font-size: 14px !important;
    margin-top: 8px !important;
    font-weight: 500 !important;
    margin-left: 0 !important;
}

.hs-error-msgs ul,
.hs-form .hs-error-msg ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.hs-error-msgs li,
.hs-form .hs-error-msg li {
    margin-bottom: 4px !important;
}

/* Champs en erreur */
.hs-form .error input,
.hs-form .hs-input.error {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

/* ==========================================================================
   MESSAGES DE SUCCÈS
   ========================================================================== */

.hs-form .submitted-message {
    background: #d1fae5 !important;
    color: #065f46 !important;
    padding: 16px !important;
    border-radius: 8px !important;
    border: 1px solid #a7f3d0 !important;
    margin-top: 16px !important;
    margin-top: 8px!important;
}

.submitted-message p span{
    display: block;
    //margin-bottom: 8px !important;
    font-family: "BrandonGrotesque", sans-serif!important;
}
.submitted-message p{
    display: block;
    margin-bottom: 8px !important;
    font-family: "BrandonGrotesque", sans-serif!important;
}
.submitted-message > p:nth-of-type(2) span {
    margin-bottom: 20px!important;
    font-size: 16px!important;
    font-weight: 700!important;
  }
.submitted-message > p:nth-of-type(2) {
margin-bottom: 20px!important;
font-size: 16px!important;
font-weight: 700!important;
}
/* ==========================================================================
   TEXTE LÉGAL ET RICHTEXT
   ========================================================================== */

.hs-richtext,
.hs-form .hs-richtext {
    font-size: 16px !important;
    color: #374151 !important;
    line-height: 1.6 !important;
    margin-top: 24px !important;
}

.hs-richtext a,
.hs-form .hs-richtext a {
    color: #374151 !important;
    text-decoration: underline !important;
}

.hs-richtext a:hover,
.hs-form .hs-richtext a:hover {
    color: #1f2937 !important;
}

/* ==========================================================================
   CHECKBOXES ET RADIOS
   ========================================================================== */

.hs-form input[type="checkbox"],
.hs-form input[type="radio"] {
    width: auto !important;
    margin-right: 8px !important;
    transform: scale(1.2) !important;
}

.hs-form .hs-form-booleancheckbox label,
.hs-form .hs-form-radio label {
    font-size: 16px !important;
    font-weight: 400 !important;
    display: inline !important;
    margin-left: 8px !important;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 640px) {
    /* Labels plus petits sur mobile */
    .hs-form-field label {
        font-size: 20px !important;
    }
    
    /* Disposition verticale pour les champs en ligne */
    .hs-form-field.hs-form-field-inline {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 12px !important;
    }
    
    /* Bouton pleine largeur sur mobile */
    .hs-form-field.hs-form-field-inline .hs-button {
        width: 100% !important;
    }
    
    /* Inputs plus compacts sur mobile */
    .hs-input,
    .hs-form input,
    .hs-form textarea,
    .hs-form select {
        padding: 12px !important;
        font-size: 16px !important; /* Évite le zoom sur iOS */
    }
    
    /* Boutons plus compacts sur mobile */
    .hs-button,
    .hs-form input[type="submit"],
    .hs-form button {
        padding: 14px 24px !important;
        font-size: 14px !important;
    }
}

/* ==========================================================================
   CONTENEUR PRINCIPAL
   ========================================================================== */

.hubspot-form-container {
    background: white;
    padding: 40px;
    max-width: 700px;
    margin: 40px auto;
    border-radius: 0;
    box-shadow: none;
}

@media (max-width: 768px) {
    .hubspot-form-container {
        padding: 20px;
        margin: 20px auto;
    }
}

/* ==========================================================================
   UTILITAIRES
   ========================================================================== */

/* Classe pour forcer la disposition en ligne */
.hs-form .inline-layout .hs-form-field {
    display: flex !important;
    align-items: flex-end !important;
    gap: 16px !important;
}

.hs-form .inline-layout .hs-input {
    flex: 1 !important;
    margin-bottom: 0 !important;
}

.hs-form .inline-layout .hs-button {
    flex-shrink: 0 !important;
}

/* Classe pour cacher les labels */
.hs-form .hide-labels label {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}
.actions {
    position: relative;
}
.actions:after {
    position: absolute;
    background: url('https://www.ebox.ca/wp-content/themes/ebox2018/assets/img/svg/arrow-black-register.svg') center center no-repeat;
    content: '';
    width: 64px;
    height: 38px;
    background-size: 100%;
    left: 117px;
    top: -20px;
    transform: rotate(25deg);
    @media (max-width: 1168px) {
        display: none;
    }
}

@media (max-width: 767px) {
   .wrapperInfolettre__right {
    margin-top: 25px;
   }
   .hs-button, .hs-form input[type="submit"], .hs-form button {
    max-width: 100%;
   }
}