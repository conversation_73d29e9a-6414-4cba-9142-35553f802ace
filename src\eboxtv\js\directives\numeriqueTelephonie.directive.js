/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { CoreTools } from '@core/helpers';

export class NumeriqueTelephonieDirective extends Directive {

	_$OrderService = OrderOnlineService.getInstance()

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-numeriqueTelephonie]';
    sku_telephonie = '';

    selection = {
        lineCode: "",
        planCode: "",
        planPrice: "rent",
        type: "",
    }

    cardTelephonie = '';

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        this.cardTelephonie = document.querySelectorAll('.bloc-forfait');

        this.initForm();
        this.cardTelephonie.forEach(el => {
            el.addEventListener('click', function(evt){
                evt.preventDefault();
                var classlist = Array.from(evt.currentTarget.classList);
                if(!classlist.includes('bloc-forfait--active')){
                    this.toggleSelection(evt.currentTarget)
                }else{
                    this.deleteSelection(evt.currentTarget);
                }
            }.bind(this))
        });
	}

    initForm(){
        this.selection = this._$OrderService.retPhoneOrder();
        if(this.selection &&!Object.keys(this.selection).length){
            this.selection.planCode = '';
            this.selection.lineCode = '';
        }

        if(this.selection ){
            this.cardTelephonie.forEach(el => {
                if(this.selection.planCode != ""){
                    this.sku_telephonie = this.selection.planCode; 
                }else{
                    this.sku_telephonie = this.selection.lineCode; 
                }  

                if( el.getAttribute('data-sku') == this.sku_telephonie ){
                    el.classList.add('bloc-forfait--active');
                }
            });
        }

    }

    toggleSelection(target){
        this.cardTelephonie.forEach(el => {
            el.classList.remove('bloc-forfait--active');
        });
        target.classList.add('bloc-forfait--active');
        if(target.getAttribute('data-sku') !== 'none'){
            this.selection.lineCode = target.getAttribute('data-linecode');
            this.selection.planCode = target.getAttribute('data-plancode');
            this.selection.planPrice = target.getAttribute('data-price');
            this.selection.type = target.getAttribute('data-type');

            var title = target.querySelectorAll("h3").item(0).innerHTML;

            const expirationDate = new Date();
            expirationDate.setDate(expirationDate.getDate() + 3);

            /*CookiesService.setCookies({
                name: 'CommandeStarted',
                value: 'oui',
                expires: expirationDate
            });*/

            this._$OrderService.saveEquipementPhone([
                {
                    buyRent: "rent",
                    code: "EBX00299",
                    discount: null,
                    price: "5.00",
                    qty: 1,
                    service: "voip",
                    sit: null,
                    type: "grandStream"
                }]);

        }
        else{
            this.selection = {
                type: 'none',
                planCode: 'none',
                lineCode: 'none',
                planPrice: '0.00',
            }

            this._$OrderService.saveEquipementPhone([]);
        }
         
        if(CoreTools.lang == 'fr'){
            this.srSpeak(title + ' selectionné', 'assertive');
        }else{
            this.srSpeak(title + ' selected', 'assertive');
        }

        this._$OrderService.savePhoneService(this.selection);
    }

    deleteSelection(target){
        this.cardTelephonie.forEach(el => {
            el.classList.remove('bloc-forfait--active');
        });

        this.selection = {
            type: 'none',
            planCode: 'none',
            lineCode: 'none',
            planPrice: '0.00',
        }
        
        if(CoreTools.lang == 'fr'){
            this.srSpeak('Forfait téléphonie supprimé', 'assertive');
        }else{
            this.srSpeak('Phone plan deleted', 'assertive');
        }

        this._$OrderService.savePhoneService(this.selection);
        this._$OrderService.saveEquipementPhone([]);
        
    }

    srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}

}
