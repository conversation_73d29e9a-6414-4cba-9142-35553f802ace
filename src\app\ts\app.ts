import { App } from '@core/bootstrap';
import { ToggleClassOnClickDirective } from '@common/ts/directives/toggle-class-on-click.directive';
import { OpenSubmenuOnClick } from './directives/open-submenu-on-click.directive';
import { OwlCarouselDirective } from './directives/owl-carousel.directive';
import { SVGIconDirective } from './directives/svg-icon.directive';
import { ExpandToFullWidthDirective } from './directives/expand-to-full-width.directive';
import { AddClassOnScrollDirective } from './directives/add-class-on-scroll.directive';
import { AddBackLink } from './directives/add-back-link-submenu.directive';
import { MenuItemHasChildren } from './directives/menu-item-has-children.directive';
import { MenuCloserDirective } from './directives/menu-closer.directive';
import { GformOnValidateDirective } from './directives/gform-on-validate.directive';
import { ValidationController } from '@common/js/directives/validation-controller.directive.js';
import { ToggleClassOnChangeDirective } from '@common/js/directives/modals/toggle-class-on-change.directive.js';
import { EnableOnValueDirective } from '@common/js/directives/modals/enable-onvalue.directive.js';
import { EnableOnSpecificValueDirective } from '@common/js/directives/modals/enable-onspecificvalue.directive.js';
import { ClearableDirective } from '@common/js/directives/clearable/clearable.directive.js';
import { QualificationBannerDirective } from '@common/ts/directives/qualification-banner.directive';
import { ReturnToReferer } from './directives/return-to-referer.directive';
import { GoogleMapDirective } from '@common/ts/directives/google-map/google-map.directive';
import { AutoscrollDirective } from '@common/ts/directives/autoscroll.directive';
import { ShowOnInitDirective } from '@common/ts/directives/show-on-init/show-on-init.directive';
import { PersonalInfoDirective } from "../../eboxtv/js/directives/personnal-info.directive";
import { PaiementDirective } from "../../eboxtv/js/directives/paiement.directive";
import { CommandeTelephoniqueDirective } from "../../eboxtv/js/directives/commande-telephonique.directive";
import { BoutonCommandeDirective } from "../../eboxtv/js/directives/bouton-commande.directive";
import { PopupVitesseDirective } from "../../eboxtv/js/directives/popup-vitesse.directive";
import { PaiementCommandeDirective } from "../../eboxtv/js/directives/commande-paiement.directive";
// import { CommandeSidePanelDirective } from "../../eboxtv/js/directives/commande-side-pannel.directive";
// import { CommandePriceBannerDirective } from "../../eboxtv/js/directives/commande-price-banner.directive";
// import { CommandeTotalOrderCounter } from "../../eboxtv/js/directives/commande-total-order-counter.directive";
// import { CommandeOrderCounterDirective } from "../../eboxtv/js/directives/commande-order-counter.directive";
// import { CommandeCostCounterDirective } from "../../eboxtv/js/directives/commande-cost-counter.directive";
import { CommandeInternetDirective } from "../../eboxtv/js/directives/commande-internet.directive";
import { NumeriqueDesoceurDirective } from '@eboxtv/js/directives/numerique-decodeur.directive';
import { NumeriquecloudSpaceDirective } from '@eboxtv/js/directives/numerique-cloudSpace.directive';
import { NumeriqueInternetDirective } from '@eboxtv/js/directives/numeriqueInternet.directive';
import { NumeriqueTelephonieDirective } from '@eboxtv/js/directives/numeriqueTelephonie.directive';
import { NumeriqueSelectChannelNext } from '@eboxtv/js/directives/numerique-select-channel-next.directive';
import { NumeriqueCart } from '@eboxtv/js/directives/numerique-cart.directive';
import { NumeriquePopupDirective } from '@eboxtv/js/directives/numerique-popup.directive';
import { DecodeurDirective } from "@eboxtv/js/directives/decodeur.directive";
import { TimelineDirective } from '../../eboxtv/js/directives/timeline.directive';
import { tvStepValidator } from '@eboxtv/js/directives/tv-step-Validator.directive';
import { footerRedirectionDirective } from "../../eboxtv/js/directives/footerRedirect.directive";
import { JobApplicationDirective } from '@eboxtv/js/directives/job-application-form.directive';
// Polyfills
import 'regenerator-runtime/runtime';
import 'core-js/es6/set';
import 'core-js/es6/symbol';
import 'core-js/es6/promise';
import 'core-js/es7/object';
import 'core-js/modules/es6.array.from';

// Vendors
import 'bootstrap/dist/js/bootstrap';
import 'gsap/CSSPlugin';
import '@common/js/jQuery.isWithin';
import { SetCookieDirective } from '@common/js/directives/set-cookie.directive';
import { IsStickyDirective } from '../../eboxtv/js/directives/is-sticky.directive';
import { _BubbleDirective } from '../../eboxtv/js/directives/_bubble/_bubble.directive';
import { AddClassOnLoadDirective } from '@common/ts/directives/add-class-on-load.directive';
import { LocalizerDirective } from '@common/ts/directives/localizer.directive';
import { LocalisationDisplayDirective } from '@common/ts/directives/localisation-display.directive';
import { ShowOnLocalizedDirective } from '@common/ts/directives/show-on-localized/show-on-localized.directive';
import { HideOnLocalizedDirective } from '@common/ts/directives/hide-on-localized/hide-on-localized.directive';
import { FallOnLoadDirective } from '@app/ts/directives/fall-on-load/fall-on-load.directive';
import { LandFromScrollDirective } from '@common/ts/directives/land-from-scroll.directive';
import { ParallaxItemDirective } from '@common/ts/directives/parallax-item.directive';
import { PlainSectionToggleDirective } from '@common/ts/directives/plain-section-toggle.directive';
import { ShowIfCookieDirective } from '@common/ts/directives/show-if-cookie/show-if-cookie.directive';
import { AddClassIfCookieDirective } from '@common/ts/directives/add-class-if-cookie.directive';
import { ContactFormDirective } from '@common/ts/directives/contact-form.directive';
import { PoponURLParamDirective } from '@common/ts/directives/popon-url-param.directive';
import { GoToQualified } from '@app/ts/directives/go-to-qualified.directive';
import { QualifiedSpeedsComponent } from '@app/ts/components/qualified-speeds/qualified-speeds.component';
import { ShowIfCallback } from '@common/ts/directives/show-if-callback.directive';
import { TabControllerDirective } from '@eboxtv/js/directives/tabs/tab-controller.directive';
import { MaterialInputDirective } from '@common/ts/directives/material-input/material-input.directive';
import { popUpListing } from './misc/popUpListing';
import { CloseButtonDirective } from './directives/close-btn.directive';
import { OrderButtonDirective } from '@common/ts/directives/order-button.directive';
import { EboxAutocompleteDirective } from './directives/ebox-autocomplete/ebox-autocomplete.directive';
import { FormResetDirective } from './directives/form-reset.directive';
import { AutoscrollToInputDirective } from './directives/autoscroll-to-input.directive';
import { AdjustToEnvDirective } from './directives/adjust-to-env.directive';
import { HideForQualifiedDirective } from '@common/ts/directives/hide-for-qualified/hide-for-qualified.directive';
import { WPRedirectDirective } from '@common/ts/directives/wp-redirections.directive';
import { EBOXPhoneNumberDirective } from '@common/ts/directives/ebox-phone-number.directive';
import { InternetPackageFilterDirective } from './directives/internet-package-filter.directive';
import { InternetProviderTabDirective } from './directives/internet-provider-tab.directive';
import { from } from 'rxjs/observable/from';

new WPRedirectDirective(document.body);

OrderButtonDirective.ALL_DISABLED = false;

const EboxApp: App = new App({
	directives: [
		ToggleClassOnClickDirective,
		QualifiedSpeedsComponent,
		OwlCarouselDirective,
		SVGIconDirective,
		ExpandToFullWidthDirective,
		AddClassOnScrollDirective,
		MenuCloserDirective,
        AddBackLink,
        GformOnValidateDirective,
        MenuItemHasChildren,
        {
            use: OpenSubmenuOnClick,
            selector: '.NavPrinc .menu > .menu-item-has-children > a'
        },
		{
			use: ValidationController,
			isSingleton: true,
			selector: ValidationController.selector
		},
		ToggleClassOnChangeDirective,
		EnableOnValueDirective,
		EnableOnSpecificValueDirective,
		ClearableDirective,
        QualificationBannerDirective,
		ReturnToReferer,
		GoToQualified,
		GoogleMapDirective,
		AutoscrollDirective,
		ShowOnInitDirective,
		SetCookieDirective,
		IsStickyDirective,
		_BubbleDirective,
		AddClassOnLoadDirective,
		LocalizerDirective,
		LocalisationDisplayDirective,
		ShowOnLocalizedDirective,
		HideOnLocalizedDirective,
		FallOnLoadDirective,
		LandFromScrollDirective,
		ParallaxItemDirective,
		PlainSectionToggleDirective,
		ShowIfCookieDirective,
		AddClassIfCookieDirective,
		_BubbleDirective,
		ContactFormDirective,
		PoponURLParamDirective,
		ShowIfCallback,
		TabControllerDirective,
		_BubbleDirective,
		MaterialInputDirective,
		CloseButtonDirective,
		OrderButtonDirective,
		EboxAutocompleteDirective,
		FormResetDirective,
		AutoscrollToInputDirective,
		AdjustToEnvDirective,
		HideForQualifiedDirective,
		EBOXPhoneNumberDirective,
		InternetPackageFilterDirective,
		InternetProviderTabDirective,
		PersonalInfoDirective,
		PaiementDirective,
		CommandeInternetDirective,
		NumeriqueDesoceurDirective,
		NumeriquecloudSpaceDirective,
		NumeriqueInternetDirective,
		NumeriquePopupDirective,
		NumeriqueSelectChannelNext,
		NumeriqueCart,
		NumeriqueTelephonieDirective,
		CommandeTelephoniqueDirective,
		BoutonCommandeDirective,
		PopupVitesseDirective,
		PaiementCommandeDirective,
		// CommandeSidePanelDirective,
		// CommandePriceBannerDirective,
		// CommandeTotalOrderCounter,
		// CommandeOrderCounterDirective,
		// CommandeCostCounterDirective,
		DecodeurDirective,
		TimelineDirective,
		tvStepValidator,
		footerRedirectionDirective,
		JobApplicationDirective
	] as any[],
	trailers: [
		popUpListing
	]
}).bootstrap(false);

(<any>window).App = EboxApp;
// jQuery(document).ready(Tools.findOverflowingElements);

declare global {
	interface JQuery {
		modal(args?: any): JQuery;
	}
}