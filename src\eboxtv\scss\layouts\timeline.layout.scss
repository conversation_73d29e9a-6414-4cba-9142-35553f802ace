.timeline {
    display: flex;
    position: relative;
    justify-content: space-between;
    align-content: center;
    width: 100%;
    margin: 0 auto;
    padding: 40px 0 50px;
    margin-bottom: 50px;
    
    @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
        padding: 50px 0;
    }
    @media (max-width: 730px) {
        height: auto;
        padding: 35px 0;
    }

    &__step {
        display: inline-block;
        position: relative;
        text-align: left;

        &:not(.timeline__step--current) {
            &:hover {
                .timeline__step-icon {
                    animation: pulse 0.4s steps(2);
                    fill: $c-primary-opacity;
                }
            }
        }

        @media (max-width: 730px) {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0;
            text-align: center;
            margin: 0;
        }

        &--current {
            // pointer-events: none;
            .timeline {
                &__token {
                    &:after {
                        background-position: center -5%;
                        transform: none !important;
                    }
                    @media (max-width: 730px) {
                        border-width: 11px;
                        width: 26px;
                        height: 26px;
                    }
                }

                &__step-icon {
                    fill: $c-white;
                }
            }
        }

        &--active {
            .timeline {
                &__token {
                    &:after {
                        background-position: center 32.5%;
                    }
                    @media (max-width: 730px) {
                        background-color: $c-primary;
                        background-image: none !important;
                    }
                }

                &__step-icon {
                    fill: $c-white;
                }
            }
        }

        &--disabled {
            pointer-events: none;

            .timeline {
                &__token {
                    &:after {
                        background-position: center 108%;
                    }
                    @media (max-width: 730px) {
                        border-color: $c-primary-opacity;
                    }
                }
                &__step-icon {
                    fill: $c-primary-opacity;
                }
            }

            &:hover {
                .timeline {
                    &__token {
                        opacity: 0.8;
                    }
                    &__step-icon {
                        animation: none;
                    }
                }
            }
        }

        &:not(.timeline__step--current, .timeline__step--active, .timeline__step--disabled) {
            &:hover {
                .timeline {
                    &__token {
                        &:after {
                            background-position: center 108%;
                        }
                    }
                }
            }
        }
    }
    
    &__step-title {
        font: 700 16px/22px $ebtv-f-primary;
        color: $c-black;
        margin-top: -6px;

        @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
            display: none;
        }
    }

    &__link {
        display: inline-block;
        text-align: center;

        &:hover, &:focus, &:active {
            text-decoration: none;
            color: $c-black;
        }
    }

    &__token {
        position: relative;
        display: inline-block;
        width: 70px;
        height: 120px;
        &:after {
            content: '';
            position: absolute;
            display: inline-block;
            height: 110px;
            width: 110px;
            top: 50%;
            left: 50%;
            margin: -55px 0 0 -55px;
            background: {
                size: 80px;
                image: url('../../img/circle-sprite.png');
                repeat: no-repeat;
                position: center 70%;
            }
            z-index: 1;
        }

        /* Makes the circles look all different by
        giving them a random rotation. */
        // @for $i from 1 through 6 {
        //     $path: &;
        //     @at-root {
        //         .timeline__step {
        //             &:nth-child(#{ $i }) {
        //                 #{ $path } {
        //                     &:after {
        //                         transform: rotate(#{random(180)}deg);
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }

        @media (max-width: 730px) {
            width: 20px;
            height: 20px;
            background-image: url('~@common/img/back-body-tv.jpg') !important;
            background-color: $c-white;
            border: 2px solid $c-primary;
            border-radius: 100%;
            z-index: 2;

            &:after {
                content: none;
            }
        }
    }

    &__step-icon {
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -22px 0 0 -22px;
        width: 44px;
        height: 44px;
        fill: $c-primary;
        z-index: 2;

        &:after {
            content: none;
        }

        @media (max-width: 730px) {
            display: none;
        }
    }
}