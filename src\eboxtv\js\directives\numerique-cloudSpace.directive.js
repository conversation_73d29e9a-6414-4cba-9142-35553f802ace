/* global jQuery */
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { TVOptionsService } from '@eboxtv/ts/services/TVOptions.service';
export class NumeriquecloudSpaceDirective extends Directive {

	_$OrderService = OrderOnlineService.getInstance()
	_$tvOptionsService = TVOptionsService.getInstance()

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-numeriqueCloudSpace]';
    sku_cloudspace = '';
    price_cloudspace = '';
    title_cloudspace = '';
    id_cloudspace = '';

    cardInfoNuagique = '';
    selection = {
        ID : '',
        price : '',
        sku : 1,
        title : 0,
        min: 0
    };

	constructor(host) {
		super(host, []);
		this._onInit();
	}

	_onInit() {
        this.cardInfoNuagique = document.querySelectorAll('.item-infoNuagique');

        this.initForm();
        this.cardInfoNuagique.forEach(el => {
            el.addEventListener('click', function(evt){
                evt.preventDefault();
                var classlist = Array.from(evt.currentTarget.classList);
                if(!classlist.includes('tv-items-cart--active')){
                    this.toggleSelection(evt.currentTarget)
                }
            }.bind(this))
        });
	}

    initForm(){
        // const tvOptions = this._$OrderService.retTVOption();
        const tvOptions = LocalStorage.get('eboxTVOptions');
        tvOptions && Object.keys(tvOptions).length ? this.selection = tvOptions.cloudSpace : false;
            if(this.selection && (this.selection.sku === 0)){
                this.selection.sku = 'none';
            }


        if(this.selection ){
            this.cardInfoNuagique.forEach(el => {
                if( el.getAttribute('data-sku') == this.selection.sku ){
                    el.classList.add('tv-items-cart--active');
                }
            });
        }

    }

    toggleSelection(target){
        this.cardInfoNuagique.forEach(el => {
            el.classList.remove('tv-items-cart--active');
        });
        target.classList.add('tv-items-cart--active');
        if(target.getAttribute('data-sku') !== 'none'){
            if(!this.selection){
                this.selection = {}
            }
            this.selection.sku = target.getAttribute('data-sku');
            this.selection.ID = target.getAttribute('data-id');
            this.selection.price = target.getAttribute('data-price');
            this.selection.title = target.getAttribute('data-title');
            this.selection.min = target.getAttribute('data-minimum');
        }
        else{
            this.selection = {
                ID : '',
                price : 0,
                sku : 0,
                title : 0,
                min: 0
            };
        }
        const options = {
            cloudSpace : this.selection
        }
        this._$tvOptionsService.optionChangeSubject$.next(this.selectedOptions);

        this._$OrderService.saveTVOption(options)

    }

    isCloudSpaceRequired(){

        var isRequired = false;
        var selection_infoNuagique = this._$OrderService._reteboxTVOptions();
        if(  !(selection_infoNuagique && !jQuery.isEmptyObject(selection_infoNuagique)) ){
            isRequired = false;
        }

    }




}
