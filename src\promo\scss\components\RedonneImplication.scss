.RedonneImplication {
	padding: 75px 0 46px;
	overflow: hidden;

	@media (max-width: 767px) {
		padding: 66px 0;
	}
	
	&__text {
		margin-bottom: 65px;

		@media (max-width: 767px) {
			display: none;
		}
	}

	&__how-to-title {
		position: relative;
		display: table;
		margin: 0 auto 53px;
		font: 900 36px/1.4 $f-primary;
		color: $c-primary-dark;

		&:before, &:after {
			content: '';
			display: block;
			position: absolute;
			top: 12px;
			width: 52px;
			height: 50px;
			margin: 0 15px;
			background: {
				repeat: no-repeat;
				size: contain;
				position: center;
			};
		}

		&:before {
			background-image: url('../../img/Facon_gauche.svg');
			right: 100%;
		}
		&:after {
			background-image: url('../../img/Facon_droite.svg');
			left: 100%;
		}

		.doodled {
			&:after {
				content: '';
				display: block;
				bottom: -1px;
				left: 50%;
				transform: translateX(-50%);
				height: 8px;
				width: 100%;
				background: {
					image: url('../../img/facon_underline.svg');
					repeat: no-repeat;
					size: 100% 8px;
					position: center;
				};
			}
		}

		@media (max-width: 767px) {
			font-size: 32px;
		}
		@media (max-width: 649px) {
			margin: 0 0 51px;
			
			.doodled {
				display: table;
			}

			&:before {
				content: none;
			}
			&:after {
				margin-left: -12px;
			}
		}

		@include applyAtRoot('html:lang(en)') {
			@media (min-width: 650px) {
				br {
					display: none;
				}
			}
			@media (max-width: 649px) {
				.doodled {
					display: inline;
				}

				&:after {
					margin-left: 1px;
				}
			}
		}
	}

	&__how-to-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -75px;

		@media (max-width: 991px) {
			margin: 0 -50px;
		}
		@media (max-width: 767px) {
			margin: 0 -30px;
		}
	}

	&__how-to-item {
		position: relative;
		display: inline-block;
		flex: 0 0 50%;
		padding: 0 75px;

		@media (min-width: 768px) {
			&:not(:last-child) {
				&:after {
					content: '';
					position: absolute;
					display: block;
					top: 50%;
					right: 0;
					transform: translateY(-50%);
					width: 8px;
					height: 100%;
					background: {
						image: url('../../img/underline-sep.svg');
						repeat: no-repeat;
						size: 100% 100%;
						position: center;
					};
				}
			}
		}

		@media (max-width: 991px) {
			padding: 0 50px;
		}
		@media (max-width: 767px) {
			padding: 0 30px;
			flex: 0 0 100%;
			
			&:not(:last-child) {
				margin-bottom: 68px;
			}
		}
	}

	&__how-to-itemtitle {
		font: 900 36px/1.2 $f-primary;
		color: $c-primary;
		margin-bottom: 36px;

		@media (max-width: 899px) {
			font-size: 30px;
		}
		@media (max-width: 767px) {
			margin-bottom: 24px;
		}
		@media (max-width: 649px) {
			font-size: 27px;
		}
	}

	&__how-to-description {
		margin-bottom: 40px;
		
		@media (max-width: 767px) {
			margin-bottom: 24px;
		}
	}

	&__legend {
		margin-top: 38px;
		
		@media (max-width: 767px) {
			margin-top: 24px;
		}
	}

	&__social-list {
		font-size: 0;
	}

	&__social-item {
		display: inline-block;
		
		&:not(:last-child) {
			margin-right: 14px;
		}
	}

	&__social-item-icon {
		display: inline-block;
		width: 40px;
		height: 40px;
		fill: $c-primary-dark !important;
		transition: fill 0.1s linear;
	}

	&__social-link {
		&:hover {
			.RedonneImplication__social-item-icon {
				fill: $c-primary !important;
			}
		}
	}

	.cform__btn-wrapper {
		margin-bottom: 0;
	}
}