.channel-list {
    position: relative;
    margin-bottom: 80px;
    padding: 0;
    font-size: 0;
    
    &:last-of-type {
        margin-bottom: 0;
    }

    &--row20 {
        margin: 0 -30px -30px;

        .channel-list {
            &__item  {
                margin: 0 30px 30px;
            }
        }
    }

    &--all-clickable {
        cursor: pointer;
        transition: background 0.1s linear;

        &:hover, &:focus {
            background: rgba($c-white, 0.05);
        }
    }

    &--print {
        display: none;

        @media print {
            display: inline-block;
        }
    }

    &--resume {
        .choice-list__item {
            position: relative;
            display: inline-block;
            margin-bottom: 9px;
            margin: 0 30px 30px;

            .chosen-channel {
                position: relative;
                display: inline-block;
                background-size: 50px;
                border-radius: 8px;
                width: 90px;
                height: auto;
                cursor: pointer;

                &:after {
                    content: '';
                    display: block;
                    width: 100%;
                    padding-top: 100%;
                }
            }

            .channel-list.is-children-list {
                display: inline-block;
                margin-left: 30px;
            }
            
            @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
                margin: 0 4.2% 4%;
                width: 24.9%;
                .chosen-channel {
                    background-size: 50%;
                }
            }
        }
    }

    &--transparent {
        &.channel-list--opened {
            .channel-list__header {
                background: none;
            }
        }
    }

    &__header {
        padding: 0 0 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid $c-white;
        margin-bottom: 40px;
        height: 40px;
        color: $c-white;
        transition: all 0.1s linear;

        &--no-border {
            border: none;
            padding-bottom: 0;
        }

        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            padding: 6px 20px;
            margin-bottom: 0;
        }
    }

    &:not(.channel-list--plain) {
        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            .channel-list__body {
                padding: 30px 60px 30px 20px;
            }
        }
    }

    &__title {
        line-height: 1;
    }

    &__title-count {
        display: inline-block;
        font: 400 16px/22px $ebtv-f-primary;
        margin-left: 10px;
    }

    &__title, &__select-all-title {
        color: currentColor;
    }

    &__checkbox {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 10px !important;
    }

    &__list {
        margin: 0 -30px -30px -30px;

        &--sub {
            display: inline-block;
            margin: 0 0 0 30px;

            .channel-list__item:last-child {
                margin-right: 0;
            }
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            margin: 0 -15px -30px;
        }
        @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
            margin: 0 -4.6% -30px;
            font-size: 0;
            cursor: pointer;

            &--sub {
                margin: 0 0 0 15px;
            }
        }
    }

    &__item {
        display: inline-block;
        margin: 0 30px 30px;
        
        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            margin: 0 15px 30px;
            .channel {
                &__logo-container {
                    width: 81px;
                    height: 81px;
                }
            }
        }
        @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
            margin: 0 4.2% 4.2% !important;
            width: 24.9%;

            .channel {
                width: 100%;

                &__logo-container {
                    width: 100%;
                    height: auto;
                    padding: 0;

                    &:after {
                        content: '';
                        display: block;
                        width: 100%;
                        padding-top: 100%;
                    }
                }

                &__logo {
                    width: 60%;
                    max-height: none;
                }

                .channel__details-btn-icon {
                    display: none;
                }
            }
        }
    }

    &__toggle-btn {
        display: none;
        appearance: none !important;
        background: none;
        border: none;
        padding: 0;
        font-size: 0 !important;
        line-height: 1;

        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            display: inline-block;
        }
    }

    &__toggle-icon {
        display: inline-block;
        fill: currentColor;
        width: 20px;
        height: 20px;
        transform: rotate(0);
    }

    &__select-all-label {
        display: flex;
        align-items: center;
        user-select: none;
        margin-bottom: 0;
        font: 700 16px/22px $ebtv-f-primary;

        &--bundle {
            position: absolute;
            bottom: -2px;
            right: -2px;
            color: $c-primary;
            font-family: $ebtv-f-primary;
            vertical-align: middle;
            background: $c-white;
            padding: 10px 30px 8px;
            margin: 0;
            border-radius: 8px 0;
            cursor: pointer;
            transition: all 0.1s linear;
        }

        &--premium {
            background: $c-white;
            color: $c-primary;
        }

        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
           @at-root {
               &:not(#{ & }--bundle), &:not(#{ & }--bundle) {
                   display: none !important;
               }
           }
        }
    }

    &__select-all-title {
        display: inline-block;
        &--selected {
            display: none;
        }
    }

    &__logo-container {
        position: relative;
    }

    &__link-icon {
        position: absolute;
        display: inline-block;
        width: 30px;
        height: 30px;
        fill: $c-light-grey;
        top: 50%;
        left: 100%;
        margin: 0;
        transform: translate(55%, -50%);

        &--red {
            fill: $c-primary;
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            width: 30%;
            height: 30%;
            transform: translate(30%, -50%);
        }
        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            left: 95%;
        }
    }


    // BUNDLE
    &--bundle {
        padding: 40px 40px 60px;
        box-shadow: 0 0 0 2px $c-white;
        border-radius: 8px;

        @media (max-width: map-get($ebtvBreakpoints, laptop)) {
            padding: 30px 30px 60px;
        }
        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            padding: 30px 20px;
        }


        .channel-list {
            &__header {
                border-bottom: none;
                padding-bottom: 0;
                height: 30px;

                @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
                    margin-bottom: 30px;
                }
                @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
                    padding: 0;
                }
            }

            &__checkbox {
                position: absolute;
                visibility: hidden;
                width: 1px;
                height: 1px;
            }

            &__list {
                @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
                    padding: 0 0 50px;
                }
            }

            &__select-all-icon {
                position: relative;
                display: inline-block;
                vertical-align: middle;
                width: 20px;
                height: 20px;
                bottom: 2px;
                fill: currentColor;
                margin-right: 3px;

                &--selected {
                    display: none;
                }
            }

            &__title {
                @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
                    padding: 0;
                }
            }

            // &__list {
            //     margin: 0 -12px -30px;
            // }

            // &__item {
            //     margin: 0 12px 30px;
            // }
        }

        &.channel-list--selected {
            box-shadow: 0 0 0 2px $c-primary;

            .channel-list {
                &__select-all-label {
                    &--bundle {
                        background: $c-primary;
                        color: $c-white;
                    }
                }

                &__select-all-title {
                    display: none;
                    &--selected {
                        display: inline-block;
                    }
                }

                &__select-all-icon {
                    display: none;
                    &--selected {
                        display: inline-block;
                    }
                }
            }
        }
    }

    
    // PREMIUM
    &--premium {
        box-shadow: 0 0 0 2px $c-white;

        &.channel-list--selected {
            .channel-list {
                &__select-all-label {
                    &--bundle {
                        color: $c-white;
                    }
                }

                &__select-all-title {
                    display: inline-block;
                    &--selected {
                        display: none;
                    }
                }
            }
        }
    }

    @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
        box-shadow: 0 0 0 1px $c-white;
        border-radius: 5px;
        margin-bottom: 40px;

        &--opened {
            border-width: 2px;

            .channel-list {
                &__header {
                    background: $c-white;
                    border-radius: 0;
                    color: $c-black;
                }

                &__toggle-icon {
                    transform: rotate(-180deg);
                }
            }
        }
    }
}