#__bs_notify__ {
	top: auto !important;
	right: auto !important;
	bottom: 0;
	left: 0;
}

html,
body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    //background: url('../../img/white-pattern.png') repeat;
    background: #FFF;
}

body.page-template-page-faq,
body.page-template-page-soutien {
    background: $c-white;
}

@-moz-document url-prefix() {
    body {
        font-weight: 400 !important;
    }
}

html {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    font-weight: 400;
}

.border-right {
    border-right: 1px solid #979797;
    @media (max-width: 767px) {
        border-right: 0;
    }
}

.zeropaddingtop {
    padding-top: 0!important
}

.zeropaddingbottom {
    padding-bottom: 0!important
}

.zeromarginbottom {
    margin-bottom: 0!important
}

.single-telephonies .paddingtop100 {
    padding-top: 100px!important;
    @media (max-width: 991px) {
        padding-top: 70px!important;
    }
    @media (max-width: 767px) {
        padding-top: 40px!important;
    }
}

.light-grey {
	color: $c-light-grey
}

iframe {
	border: 0;
}

.numtel {
    color: $c-primary;
    transition: $t-primary;
    &:hover {
        color: $c-grey;
    }
}

.tablepress {
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border:1px solid #343434!important;
    border-spacing:0!important;
    font-family: $f-primary!important;
    font-size:19px!important;
    overflow: scroll!important;
    @media (max-width: 767px) {
        font-size:15px!important;
    }
    td {
        padding:15px 10px!important;
        a {
            color:#8f8f8f!important;
            &:hover {
                color:#3d3d3d!important;
            }
        }
    }
    .odd {
        td {
            background:#f5f5f5!important;
        }
    }

}
body .tablepress th {
    border:none!important;
    background:#343434!important;
    color:#fff!important;
    padding:12px 10px!important;
}

select, button, textarea, input, a, [tabindex] {
    outline: none;
}
.ulTwoColumns {
    columns: 2;
    @media (max-width: 767px) {
        columns: 1;
    }
}

.flex {
    display: flex;
    @media (max-width: 991px) {
        display: block;
    }
}