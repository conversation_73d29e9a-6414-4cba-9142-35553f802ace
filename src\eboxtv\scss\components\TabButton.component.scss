.TabButton {
	appearance: none;
	border: none;
	background: none;
	border-radius: 6px 0 0 6px;
	font-size: 0;
	padding: 0;
	text-align: left;
	overflow: hidden;
	box-shadow: 0 2px 6px rgba(#000, 0.76);

	&__content {
		background: $c-black;
		padding: 14px 6px 12px 8px;
	}

	&__title {
		font: 100 8px/11px $ebtv-f-primary;
		margin-bottom: 3px;
		color: $c-white;
	}

	&__price {
		font: 900 22px/16px $ebtv-f-primary;
		white-space: nowrap;
		color: $c-white;

		sup {
			position: relative;
			display: inline-block;
			font: 900 10px/8px $ebtv-f-primary;
			top: -0.7em;
			margin-left: 2px;

			&:after {
				content: 'par mois';
				position: absolute;
				display: block;
				left: 2px;
				top: 100%;
				font: 400 4px/1 $ebtv-f-secondary;
			}
		}

		&:lang(en) {
			sup:after {
				content: 'per month';
			}
		}
	}

	&__foot {
		background: $c-primary;
		padding: 7px 6px 7px 8px;
		color: #FFF;
		white-space: nowrap;
	}

	&__foot-title, &__foot-icon {
		display: inline-block;
		vertical-align: middle;
	}

	&__foot-title {
		font-size: 10px;
		line-height: 1;
		margin-right: 3px;
	}

	&__foot-icon {
		width: 10px;
		height: 10px;
		fill: currentColor;
		animation: arrowRight 0.9s steps(3) infinite;
	}
}