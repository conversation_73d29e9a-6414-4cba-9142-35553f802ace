/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../services/TVOrder.service';
import { PriceCalcService } from '../services/PriceCalc.service';
import { Tools } from '@common/ts/services/Tools.service';
import { TweenLite } from 'gsap/TweenLite';
import { Power1 } from 'gsap/EasePack';
import { CoreTools } from '@core/helpers';

export class CommandePriceBannerDirective extends Directive {
	static selector = '[commande-price-banner]';
	static SHOWN_CLIPPED_PATH = [0, 0, 100, 0, 100, 100, 0, 100];
	static HIDDEN_CLIPPED_PATH = [0, 0, 0, 0, 0, 100, 0, 100];

	Subscriptions
	_tvOrderChangeSub;

	Services
	$tvOrder = TVOrderService.getInstance();
	$priceCalc = PriceCalcService.getInstance();

	// Alias' values
	_nextStepValue = this.$tvOrder.nextStep;
	_showValue;

	// Elements
	$parent = this.$host.parents(this.attrs.parentSelector);
	autoHide = this.attrs.autoHide || (!this.attrs.autoHide && (!this.$parent.length && !this.attrs.shownClassName));
	$classChangingElement = this.attrs.parentSelector && this.$parent.length ? this.$parent : this.$host;
	$remoteFlags = this.attrs.remoteFlagsSelector ? jQuery(this.attrs.remoteFlagsSelector) : null;


	// Getters and Setters
	get cost() {
		return this.$priceCalc.getCostByStep(this.nextStep, true, true)
			+ this.$priceCalc.totals.channels['premium'] + this.$priceCalc.totals.channels['pre-selected'] +
			PriceCalcService.BASE_COST;
	}

	get nextStep() {
		return this._nextStepValue;
	}
	set nextStep(val) {
		if (val !== this._nextStepValue && val !== undefined) {
			this._nextStepValue = val;
			this._render();
		}
	}

	get phrase() {
		const selectedChannels = this.$tvOrder.selectedChannels;
		let channelsCount = selectedChannels.others.size + selectedChannels['a-la-carte'].size;

		const remaining = this.nextStep - channelsCount;
		if (Tools.lang === 'fr')
			return `Ajoute <strong class="Banner__count">${ remaining + ' ' + CoreTools.pluralize(remaining, 'chaîne', 'chaînes') }</strong> pour un `;
		else
			return `Add <strong class="Banner__count">${ remaining + ' ' + CoreTools.pluralize(remaining, 'channel', 'channels') }</strong> for a `;
	}

	get pricePhrase() {
		const localizeCost = Tools.convertPriceToLocal(this.cost);

		if (Tools.lang === 'fr')
			return `${ localizeCost } / mois`;
		else
			return `${ localizeCost } / month`;
	}

	get template() {
		return `
			<div class="Banner__wrapper">
				<div class="Banner__title-container">
				
					<h6 class="Banner__title" price-banner>
						${ this.phrase } 

						<span class="Banner__price is-doodled is-doodled--underlined">
							<svg class="Banner__splash">
								<use xlink:href="#icon-mini-splash"></use>
							</svg>
							
							<span>${ CoreTools.translate('total de', 'total of') } <strong>${ this.pricePhrase }</strong></span>
							
							<svg class="Banner__splash Banner__splash--right">
								<use xlink:href="#icon-mini-splash"></use>
							</svg>
						</span>
					</h6>
				</div>
			</div>
		`;
	}

	get show() {
		return this._showValue;
	}
	set show(val) {
		if (val !== this._showValue) {
			this._showValue = val;

			if (val)
				this._showBanner();
			else
				this._hideBanner();
		}
	}

	constructor(host) {
		const bundle = LocalStorage.get('selectedBundles');
        if(bundle != '113570' && bundle != '113581'){
			super(host, [
				{name: 'banner-parent-selector', as: 'parentSelector'},
				{name: 'auto-hide', type: 'eval', default: true},
				'shown-class-name',
				'remote-flags-selector',
				'flag-shown-classname'
			]);
			this._onInit();
		}
	}

	_onInit() {
		this._initSubscriptions();
	}

	onAppInit() {
		this._onChannelChange();
	}

	_initSubscriptions() {
		this._tvOrderChangeSub = this.$tvOrder.selectedChannelsChange$.subscribe(
			this._onChannelChange.bind(this),
			this._onChannelChangeError.bind(this)
		);
	}

	_onChannelChange() {
		let selectedALaCarteCount = this.$tvOrder.getTotalSelectedChannelSize('a-la-carte');
		this.nextStep = this.$tvOrder.nextStep;
		this.show = selectedALaCarteCount >= 1 && selectedALaCarteCount !== this.$tvOrder.currentStep;

		this._render();
	}

	_onChannelChangeError(err) {
		if (err.type === 'ratio')
			console.error(err.data);
	}

	_render() {
		this.host.innerHTML = this.template;
	}

	_showBanner() {
		const { shownClassName } = this.attrs;
		const from = [...CommandePriceBannerDirective.HIDDEN_CLIPPED_PATH];
		const to = [...CommandePriceBannerDirective.SHOWN_CLIPPED_PATH];

		if (shownClassName) {
			this._addClass();
			return;
		}

		TweenLite.set(this.host, {clearProps: 'display'});
		TweenLite.to(from, 0.3, {
			...to,
			onUpdate: this._setClipPath.bind(this, from),
			ease: Power1.easeOut,
			delay: 0.2
		});
	}

	_hideBanner() {
		const { shownClassName } = this.attrs;
		if (shownClassName) {
			this._removeClass();
			return
		}

		TweenLite.set([this.host, this.$remoteFlags], {display: 'none'});
		this._setClipPath(CommandePriceBannerDirective.HIDDEN_CLIPPED_PATH);
	}

	_setClipPath(to) {
		const clipPath = `polygon(${ to[0] }% ${ to[1] }%, ${ to[2] }% ${ to[3] }%, ${ to[4] }% ${ to[5] }%, ${ to[6] }% ${ to[7] }%)`;

		TweenLite.set([this.host, this.$remoteFlags], {
			clipPath,
			webkitClipPath: clipPath
		});
	}

	_addClass() {
		let $el;

		if (this.$parent && this.$parent.length)
			$el = this.$parent;
		else
			$el = this.$host;

		this.$host.show()
		$el.addClass(this.attrs.shownClassName);
	}

	_removeClass() {
		let $el;

		if (this.$parent && this.$parent.length)
			$el = this.$parent;
		else
			$el = this.$host;


		this.$host.hide();
		$el.removeClass(this.attrs.shownClassName);
	}
}