import { CommandeOrderCounterDirective } from './commande-order-counter.directive';
import { Tools } from '@common/ts/services/Tools.service';
import { PriceCalcService } from '@eboxtv/js/services';
import { OrderOnlineService } from "@eboxtv/js/services/orderOnline.service";
import { TVOrderService } from "@eboxtv/js/services/TVOrder.service";
export class CommandeTotalOrderCounter extends CommandeOrderCounterDirective {
	static selector = '[commande-total-order-counter]';

	_premiumCount = 0;
	get count() {}
	set count(val) {}
	$orderOnline = OrderOnlineService.getInstance();
	$tvOrder = TVOrderService.getInstance();
	monthCost = 0;
	uniqueCost = 0;

	constructor(host) {
		super(host, [
			'premium-item-name',
			{name: 'show-count', type: 'eval', default: true},
			{name: 'include-receiver', type: 'eval', default: false},
			{name: 'include-options', type: 'eval', default: false}

		]);
		this.$orderOnline.cartMonthlyPrice.subscribe((price) => {
			const selectedChannels = this.$tvOrder.selectedChannels;
			this.monthCost = price;
			this._onOrderChange(selectedChannels);
		})

		this.$orderOnline.cartUniquePrice.subscribe((price) => {
			const selectedChannels = this.$tvOrder.selectedChannels;
			this.uniqueCost = price;
			this._onOrderChange(selectedChannels);
		})
	}

	_onOrderChange(selectedChannels) {
		const {'a-la-carte': cardChannels, others, premium, base} = selectedChannels;

		this._render(base.size + cardChannels.size + others.size + this._$TVOrder.bundledChannelIDs.length, premium.size);
	}
	
	_render(baseChannelCount, premiumChannelCount) {
		if (baseChannelCount === undefined || premiumChannelCount === undefined) return;
		const { showCount, includeReceiver, includeOptions } = this.attrs;
		let { total } = this._$priceCalc.totals;
		let channelName = this.attrs.itemName;
		let premiumName = this.attrs.premiumItemName;
		if (includeReceiver) {
			total += PriceCalcService.RECEIVER_COST;
		}
		if (includeOptions) {
			total += this._$priceCalc.totals.options.cloudSpace;
		}
		
		if (baseChannelCount > 1) channelName += 's';
		if (premiumChannelCount > 1 && premiumName !== 'premium') premiumName += 's';
			total += this.monthCost;
			total += this.uniqueCost;
		if (showCount)
			this.host.innerHTML = `${ baseChannelCount + premiumChannelCount } ${ channelName }<br/><strong>${ Tools.convertPriceToLocal(total) }<strong>`;
		else
			this.host.innerHTML = `TOTAL<br/><strong>${ Tools.convertPriceToLocal(total) }<strong>`;
	}
}