/* global jQuery */
import { Directive } from '@core/directive';
import { CookiesService } from '@common/ts/services/Cookies.service';
export class chatBubbleDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE


    //SELECTEUR DE LA DIRECTIVE
	static selector = '[chat-bubble]';

	constructor(host) {
		super(host, []);

		this._onInit();
	}

	_onInit() {
        const chatCookie = CookiesService.getCookies('chatClosed', true);
        if(chatCookie){
            jQuery('#lhc_need_help_container').hide();
        }


        document.getElementById('lhc_need_help_close').addEventListener('click', function(evt){
            evt.preventDefault();
            CookiesService.setCookies({
                name : 'chatClosed',
                value : 'true',
                expires: 'session'
            })
            const expirationDate = new Date();
            expirationDate.setDate(expirationDate.getDate() - 10);
        })
        jQuery('input[name="choixDecodeur"]').parent().parent().removeClass('TVForfaitItem__active');
        jQuery('input[name="choixDecodeur"]:checked').parent().parent().addClass('TVForfaitItem__active');

	}






}


