/* global jQuery*/
import { ToggleClassOnClickDirective } from '@common/ts/directives/toggle-class-on-click.directive';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service';

export class EBTVToggleClassOnClickDirective extends ToggleClassOnClickDirective {
	static selector = '[ebtv-toggle-class-on-click]';

	// PRIVATE PROPERTIES //
	_scrolledElement = this.attrs.scrolledElementSelector ? document.querySelector(this.attrs.scrolledElementSelector) : null;

	// INIT //
	constructor(host) {
		super(host, [
			{name: 'ebtv-toggle-class-on-click', as: 'className'},
			'scrolled-element-selector'
		]);
	}

	removeClass() {
		this.scrollScrolledElement();
		this.$targetElement.removeClass(this.attrs.className);
	}

	scrollScrolledElement() {
		if (this._scrolledElement && this._scrolledElement.scrollTop > 0)
			this._scrolledElement.scrollTop = 0;
	}
}