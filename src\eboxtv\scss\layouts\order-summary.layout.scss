@import '~@bootstrap/mixins/clearfix';

$summary_sizes: (
    desktop: 300px,
    tablet: 320px,
    mobile: 320px
);

$listingHeights: (
    closed: 195px,
    opened: 400px
);

@keyframes unveil {
    from {
        clip-path: polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%);
    }
    to {
        clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
    }
}

@mixin bannerClosed {
    display: none;
}

@mixin bannerOpened {
    display: block;
}

$mobileHeaderHeight: 42px;

@mixin underlineTitle() {
    &:after {
        content: '';
        position: absolute;
        display: block;
        left: 50%;
        bottom: 0;
        width: 95%;
        height: 1px;
        transform: translateX(-50%);
        background: $c-grey-faded;
    }
}

.order-summary {
    position: relative;
    display: inline-flex;
    width: map-get($summary_sizes, desktop);
    min-width: map-get($summary_sizes, desktop);
    z-index: 5;
    flex: 0 0 300px;

    &__SectionLoading {
        text-align: center;
        padding: 20px;

        .SectionLoading {
            &__Loading {
                color: #FFF;
            }
        }
    }

    &--show-banner {
        .order-summary {
            &__tab-button, &__body {
                .Banner {
                    @include bannerOpened;
                }
            }
        }
    }

    &__close-btn {
        position: absolute;
        display: none;
        top: 29px;
        right: 12px;
        appearance: none;
        background: none;
        line-height: 1;
        border: none;
        padding: 0;
        cursor: pointer;
        z-index: 1;

        &:hover {
            .order-summary__close-icon {
                fill: $c-light-grey;
            }
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            display: inline-block;
            top: 19px;
        }
    }

    &__close-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        fill: $c-white;
        cursor: inherit;
    }

    &__tab-button {
        position: absolute;
        display: inline-block;
        right: 100%;
        top: 50%;
        transform: translate(0, -50%);
        transition: transform 0.2s $cubic 0.2s;

        .Banner {
            position: absolute;
            top: 10px;
            left: -8px;
            width: calc(100% + 8px);
            @include bannerClosed;

            &__wrapper {
                padding: 1px 5px;
                text-align: right;

                &:before {
                    border-width: 0 8px 8px 0;
                }
            }

            &__title, &__icon {
                display: inline-block;
                vertical-align: middle;
            }

            &__title {
                font: 400 8px/16px $ebtv-f-primary;
            }
        }

        @media (min-width: map-get($ebtvBreakpoints, tabletLandscape) + 1) {
            display: none;
        }

        @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
            @at-root {
                body.is-search-results-opened, body.is-search-input-focused {
                    .order-summary {
                        &__tab-button {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    &__tab-icon {
        display: inline-block;
        width: 30px;
        height: 30px;
        fill: currentColor;
        margin-bottom: 5px;
    }

    &__tab-count {
        font: 700 12px/18px $ebtv-f-primary;
        margin: 0;
    }

    &__wrapper {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        z-index: 0;
        width: 100%;
    }

    &__sticky-track, &__sticky-wrapper {
        width: 100%;
    }

    &__sticky-wrapper {
        height: 100%;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        position: relative;
        max-height: 100vh;
    }

    &__sticky-track {
        position: absolute;
        background: $c-grey;
        height: 100%;
        right: 0;
    }

    &__head {
        position: relative;
        padding: 12px 20px 12px;
        margin-bottom: -15px;
        overflow: hidden;
        background: {
            image: url('../../img/triangle-bottom-bg.svg');
            repeat: no-repeat;
            position: center bottom;
            size: 102%;
        };
        z-index: 1;
        &:before {
            content: '';
            position: absolute;
            display: block;
            top: -30px;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 0;
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            margin-bottom: 0;
            padding: 18px 20px 35px;
        }
    }

    &__title {
        position: relative;
        font: 700 16px/22px $ebtv-f-primary;
        color: $c-white;
        z-index: 1;
        text-align: center;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            font-size: 20px;
            line-height: 30px;
        }
    }

    &__title-summary {
        display: block;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        color: $c-white;

        strong {
            font-size: 22px;
            line-height: 26px;
        }
    }

    &__body {
        padding: 25px 0 0;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        padding-left: 20px;
        box-sizing: content-box;
        width: 100%;
        right: 20px;
        position: relative;
        overflow: auto;

        .Banner {
            top: 0;
            @include bannerClosed;
        }
    }

    &__footer {
        position: relative;
        width: 100%;
        margin-top: auto;
        flex-shrink: 0;
        padding: 20px 0;
        background: $c-black;
        box-shadow: 0 -4px 8px rgba(#000, 0.2);
        z-index: 1;

        &-wrapper {
            display: flex;
            justify-content: center;
            padding: 0 15px;
            @include clearfix;
        }

        > .row {
            width: 100%;
        }

        &-section {
            display: flex;
            align-items: center;
            justify-content: center;
            float: left;
            padding: 15px;
            padding: 4px 0;
            border-right: 1px solid #FFF;
            text-align: center;

            &:first-child {
                width: 39%;
            }

            &:last-child {
                width: 61%;
                border: none;
            }
        }

        @at-root {
            .order-summary__sticky-track {
                &.is-affixed {
                    .order-summary__body {
                        .Banner {
                            display: none !important;
                        }
                    }

                    .order-summary__footer {
                        .order-summary__footer-banner-container{
                            display: block;
                        }
                    }
                }
            }
        }

        @media (min-width: 1025px) {
            .order-summary__footer-banner-container {
                display: block;
            }
        }
        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            .Banner {
                opacity: 0;
                transition: opacity 0.25s $cubic;
            }

            @at-root {
                .order-summary {
                    &--opened {
                        .order-summary {
                            &__footer {
                                .Banner {
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &__section {
        position: relative;
        padding: {
            right: 20px;
            left: 20px;
        };

        &--nolist {
            padding-bottom: 30px;
        }

        &--continue-btn {
            position: absolute;
        }

        @media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
            padding: {
                right: 15px;
                left: 15px;
            };
        }

        &--a-la-carte {
            padding-top: 0;
            padding-bottom: 15px;
        }
    }

    &__count-container {
        color: $c-white;
        font-family: $ebtv-f-primary;
        text-align: center;
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        border-bottom: 1px solid $c-white;
        padding-bottom: 5px;

        &--black {
            color: #626262;
            border-bottom: none;

            .order-summary__count-title {
                color: currentColor;
            }
        }

        &--forfaits {
            border-color: #C7C7C7;
        }

        &--last {
            border-bottom: 3px solid #000 !important;
            padding-bottom: 10px;
        }

        &--child {
            padding-left: 24px;

            .order-summary__count-title {
                font: 600 12px/18px $ebtv-f-primary;
            }
        }
    }

    &__count-title, &__count {
        display: inline-block;
    }

    &__count-title {
        position: relative;
        font: 700 16px/20px $ebtv-f-primary;
        display: inline-block;
        font-family: inherit;
        color: #C7C7C7;
        margin: 0;

        &--long {
            font-size: 14px;
        }

        @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
            padding-bottom: 0;
        }
    }

    &__count {
        font: 700 14px/16px $ebtv-f-primary;

        @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
            margin-top: 0;
        }
    }

    @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
        &__count, &__count-title {
            display: inline-block;
        }

        &__count-title {
            margin-right: 2px;
        }

        &__count-container {
            // @include underlineTitle;
            text-align: left;
        }
    }

    &__choice-list-container {
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 30px 0;
        box-sizing: content-box;
        transition: height 0.25s $cubic;

        &--forfaits {
            padding: 17px 0;
        }

        @media (max-width: #{ map-get($ebtvBreakpoints, tabletLandscape) }) {
            padding-bottom: 30px;
        }

        .order-summary {
            &__see-more-btn {
                display: none;
            }

            &__choice-list-wrapper {
                position: relative;
                display: flex;
                flex-direction: column;
            }
        }

        &--long {
            padding-bottom: 50px;
            max-height: map-get($listingHeights, closed);

            .order-summary {
                &__choice-list-wrapper {
                    overflow: hidden;
                    padding-bottom: 65px;

                    &:after {
                        content: '';
                        position: absolute;
                        display: block;
                        width: 100%;
                        height: 50%;
                        bottom: -2px;
                        background: linear-gradient(0deg, $c-grey 5%, rgba($c-grey, 0.9) 40%, rgba($c-grey, 0));
                        pointer-events: none;
                        z-index: 1;
                    }
                }

                &__choice-list {
                    padding-bottom: 0;
                }

                &__see-more-btn {
                    display: block;

                    .eboxtv-button-title {
                        display: inline-block;
                        padding: 5px;
                        border: 1px solid #FFF;
                        border-radius: 100%;
                        font-size: 0;
                        transition: background 0.1s linear;

                        &:hover {
                            background: #FFF;

                            .order-summary__see-more-icon {
                                fill: $c-grey;
                            }
                        }

                        &--opened {
                            display: none;
                        }
                    }
                }
            }
        }

        &--show-all {
            max-height: map-get($listingHeights, opened);
            padding-bottom: 60px;

            .order-summary {
                &__choice-list-wrapper {
                    padding-bottom: 0;
                    &:after {
                        height: 50px;
                        bottom: 0;
                    }
                }

                &__choice-list {
                    overflow: auto;
                    padding-bottom: 40px;

                    &.is-children-list {
                        overflow: visible;
                    }
                }

                &__see-more-btn {
                    .eboxtv-button-title {
                        display: none;

                        &--opened {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }

    &__choice-list {
        margin: -3%;
        font-size: 0;

        .choice-list {
            &__item {
                &--no-result {
                    position: absolute;
                    display: block;
                    width: 100%;
                    margin: 0;
                }
            }
        }

        .is-children-list {
        	display: inline-block;
        	position: absolute;
        	top: 0;
        	left: 0;
        	width: 100%;
        	height: 100%;
        }

        @media (max-width: #{ map-get($ebtvBreakpoints, fullDesktop) }) {
            margin: 0 -3% -10px;
        }
    }

    &__empty-list-message {
        font: 400 12px/14px $ebtv-f-primary;

        @media (max-width: #{ map-get($ebtvBreakpoints, tabletLandscape) }) {
            font: 400 14px/16px $ebtv-f-primary;
        }
    }

    &__choice-item {
        position: relative;
        display: inline-block;
        margin: 3%;
        width: 14%;
        float: left;
        will-change: transform, opacity;

        &.choice-list {
            &__item {
                &--is-children {
                    position: absolute;
                    top: 0;
                    left: 0;
                    display: inline-block;
                    width: 100%;
                    pointer-events: none;
                    transform: translate(0, 0);
                    transition: all 0.2s $cubic;

                    @for $i from 1 through 6 {
                        $travel: #{ $i * 3px };
                        &:nth-child(#{ $i }) {
                            transform: translate($travel, $travel);
                            opacity: #{ 1 - ($i * (1 / 4)) };
                            transition-delay: #{ 0.025 * ($i - 1) }s;
                        }
                    }
                }
            }
        }

        .chosen-channel {
            top: 0;
            left: 0;
            width: 100%;
            height: auto;
            will-change: transform;
            animation: pop 0.35s cubic-bezier(.55, 0, .1, 1);
            &:after {
                content: '';
                position: relative;
                display: block;
                width: 100%;
                padding-top: 100%;
                z-index: -1;
            }

            &:hover {
                + .is-children-list {
                    .choice-list {
                        &__item {
                            &--is-children {
                                // @for $i from 1 through 6 {
                                //     $travel: #{ $i * 3px };
                                //     &:nth-child(#{ $i }) {
                                //         transform: translate($travel, $travel);
                                //         opacity: #{ 1 - ($i * (1 / 4)) };
                                //         transition-delay: #{ 0.025 * ($i - 1) }s;
                                //     }
                                // }

                                .chosen-channel__delete-button {
                                    opacity: 0.9;
                                }
                            }
                        }
                    }
                }
            }
        }

        @media (max-width: #{ map-get($ebtvBreakpoints, fullDesktop) }) {
            width: 19%;
            margin: 0 3% 10px;
        }
    }

    &__see-more-btn {
        position: absolute;
        display: flex;
        justify-content: center;
        bottom: 0;
        width: 100%;
        z-index: 1;
        appearance: none;
        background: none;
        border: none;
    }

    &__see-more-icon {
        display: inline-block;
        width: 15px;
        height: 15px;
        fill: #FFF;
        transition: fill 0.1s linear;
    }

    &__navbutton {
        text-align: center;

        &:hover {
            box-shadow: none;
            transform: none;
        }

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            &:before, &:after {
                content: none;
            }

            &:hover {
                background: $c-white;
                color: $c-primary;

                .eboxtv-navbutton__icon-container {
                    border-color: $c-primary-opacity;
                }
            }
        }
    }

    .inner-wrapper-sticky {
        height: 100%;
        max-height: 100vh;
        top: 0 !important;
    }

    @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
        position: fixed;
        right: 0;
        top: 0;
        height: 100vh;
        width: map-get($summary_sizes, tablet);
        min-width: map-get($summary_sizes, tablet);
        transform: translateX(map-get($summary_sizes, tablet));
        padding-top: 0;
        z-index: 100;
        box-sizing: content-box;
        padding-left: 20px;

        // &.is-full-opened {
        //     overflow: auto;
        // }

        &__wrapper {
            position: relative;
        }

        &__sticky-track, &__sticky-wrapper {
            height: 100vh;
        }

        .inner-wrapper-sticky {
            height: auto;
            background: $c-grey;
        }

        &--opened {
            .order-summary {
                &__wrapper {
                    box-shadow: 0 2px 14px rgba(#000, 0.7);
                }

                &__body {
                    .Banner {
                        opacity: 1;
                    }
                }
            }
        }

        &--tab-closed, &--opened {
            .order-summary {
                &__tab-button {
                    transform: translate(150%, -50%);
                }
            }
        }

        &__section {
            .Banner {
                @include bannerClosed;
            }
        }

        .inner-wrapper-sticky {
            padding-bottom: $mobileHeaderHeight;
        }
    }
    @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
        width: map-get($summary_sizes, mobile);
        min-width: map-get($summary_sizes, mobile);
    }
}

#wrapper-footer-top, #wrapper-footer-bottom {
    position: relative;
    z-index: 5;
}

a.disabled, a.disabled>* {
    opacity: 0.5;
    color: lightgray;
    pointer-events: none;
    cursor: default;
}

.noticeRed{
    color: $c-primary-new;
}

.surMesure {
    margin-bottom: 40px;
}

.voip {
    margin-bottom: 100px;
    @media (max-width: 767px) {
        margin-bottom: 70px
    }
}

.btnSubmit .ButtonEffect--mention:active,
.btnSubmit .ButtonEffect--mention:focus {
    color: #C7C7C7 !important;
    background-color: $c-primary-new !important;
  // place your 'default' styling over here
}