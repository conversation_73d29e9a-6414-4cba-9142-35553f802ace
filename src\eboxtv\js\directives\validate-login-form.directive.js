/* global jQuery */
import { Directive } from '@core/directive';

export class ValidateLoginFormDirective extends Directive {

	static selector = '[validate-login-form]';

	_$codeclient = jQuery('#codeclient');
	_$password = jQuery('#password');

	constructor(host) {
		super(host, ['validate-login-form']);
		this._init();
	}

	_init() {
		this._bindEvents();
	}


	_bindEvents() {
		this.$host.on('submit', this._validateForm.bind(this));
		this._$codeclient.on('keyup', this._resetCodeRequired.bind(this));
		this._$password.on('keyup', this._resetPasswordRequired.bind(this));
	}

	_validateForm(event) {
		/* GET INPUTS VALUE */
		const codeclientvalue = this._$codeclient.val();
		const passwordvalue = this._$password.val();

		if ( codeclientvalue === '' || passwordvalue === '') {

			event.preventDefault();

			if (codeclientvalue === '') {
				this._$codeclient.addClass('ebtv-required');
				jQuery('label[for="codeclient"]').text('Requis : Code client');
			}

			if (passwordvalue === '') {
				this._$password.addClass('ebtv-required');
				jQuery('label[for="password"]').text('Requis : Mot de passe');
			}
		}
	}

	/* RESET FIELD */
	_resetCodeRequired() {
		this._$codeclient.removeClass('ebtv-required');
		jQuery('label[for="codeclient"]').text('Code client');
	}
	_resetPasswordRequired() {
		this._$password.removeClass('ebtv-required');
		jQuery('label[for="password"]').text('Mot de passe');
	}
}