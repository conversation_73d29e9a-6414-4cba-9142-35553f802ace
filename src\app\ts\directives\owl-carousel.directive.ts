import { Directive } from '@core/directive.js';
import 'owl.carousel/dist/assets/owl.carousel.css';
import 'owl.carousel';

export class OwlCarouselDirective extends Directive {
    static selector: string = '[okt-owl-carousel], [owl-carousel], [data-owl-carousel], [data-okt-owl-carousel]';

    renderDebounce: any;

    private prevBtn: NodeListOf<HTMLElement> = this.attrs.prevBtnSelector ? this.host.parentElement.querySelectorAll(this.attrs.prevBtnSelector) : null;
    private nextBtn: NodeListOf<HTMLElement> = this.attrs.nextBtnSelector ? this.host.parentElement.querySelectorAll(this.attrs.nextBtnSelector) : null;
    private $carouselHost?: JQuery = this.attrs.applyTo ? this.$host.find(this.attrs.applyTo) : this.$host;

    attrs: {
        owlOptions: any;
        prevBtnSelector?: string;
        nextBtnSelector?: string;
        applyTo?: string;
    };

	constructor(host: HTMLElement) {
		super(host, [
            {name: 'data-owl-options', as: 'owlOptions', type: 'eval', default: {}},
            {name: 'data-owl-prev', as: 'prevBtnSelector'},
            {name: 'data-owl-next', as: 'nextBtnSelector'},
            {name: 'data-apply-to', as: 'applyTo'}
		]);
    }

	onAppInit(): void {
		if ('owlCarousel' in jQuery.fn) {
			this.setOwlClass();
            this.initCarousel();
            this.bindEvents();
        }
    }

    private bindEvents(): void {
        window.addEventListener('resize', this.onResize.bind(this));

        if (this.prevBtn) {
            OwlCarouselDirective.bindEventToBtn(this.prevBtn, 'click', this.onPrevClick.bind(this));
        }

        if (this.nextBtn) {
            OwlCarouselDirective.bindEventToBtn(this.nextBtn, 'click', this.onNextClick.bind(this));
        }
    }

    private onPrevClick(evt: MouseEvent): void {
        evt.preventDefault();
        this.$carouselHost.trigger('prev.owl.carousel');
        var activeSlide = this.$carouselHost.find(".owl-item.active");
        if(activeSlide.attr('data-description') != ""){
            this.srSpeak(activeSlide.find('img').attr('data-description'), "polite");
        }

    }

    private onNextClick(evt: MouseEvent): void {
        evt.preventDefault();
        this.$carouselHost.trigger('next.owl.carousel');
        var activeSlide = this.$carouselHost.find(".owl-item.active");
        if(activeSlide.attr('data-description') != ""){
            this.srSpeak(activeSlide.find('img').attr('data-description'), "polite");
        }
    }

    private onResize(): void {
        clearTimeout(this.renderDebounce);
        this.renderDebounce = setTimeout( () => {
            this.$carouselHost.trigger('destroy.owl.carousel');
            this.initCarousel();
        }, 100);
    }

	private setOwlClass(): void {
		this.$host.addClass('owl-carousel');
	}

	private initCarousel(): void {
		this.$carouselHost.owlCarousel({
			...this.attrs.owlOptions
        });
    }
    
    private static bindEventToBtn(btnNodeList: NodeListOf<HTMLElement>, eventName: string, handler: Function): void {
        for (let i: number = 0; i < btnNodeList.length; i++) {
            const btnElement: HTMLElement = btnNodeList[i];

            btnElement.addEventListener(eventName, handler as any);
        }
    }

    private srSpeak(text:any, priority:any) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}