import { Directive } from '@core/directive';
import { EboxAutocompleteResultsComponent } from './ebox-autocomplete-results/ebox-autocomplete-results.component';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { CoreTools } from '@core/helpers';

export class EboxAutocompleteDirective extends Directive {
	public static selector: string = 'input[data-ebox-autocomplete]';

	//#region Host and attributes types
	public host: HTMLInputElement;

	public $host: JQuery<HTMLInputElement>;

	public attrs: {
		action: string;
		resultsContainerClass?: string;
	};
	//#endregion

	//#region Private properties
	private $wrapper: JQuery = jQuery('<div class="ebox-autocomplete"/>');

	private $resultsContainer: JQuery = jQuery('<div class="ebox-autocomplete__results" />');

	private resultsComponent: EboxAutocompleteResultsComponent;

	private searchRequest: JQuery.jqXHR;

	private inputDebounce: number;
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLInputElement) {
		super(host, [
			{name: 'data-ebox-autocomplete-action', as: 'action'},
			'results-container-class'
		]);
		this.init();
	}
	//#endregion

	//#region Private methods
	private init(): void {
		this.injectResultsContainer();
		this.createResultsComponent();
		this.bindEvents();
		this.update();
	}

	private injectResultsContainer(): void {
		this.$wrapper.insertBefore(this.host);
		this.$wrapper.append(this.$host);
		this.$wrapper.append(this.$resultsContainer);
	}

	private createResultsComponent(): void {
		this.resultsComponent = new EboxAutocompleteResultsComponent(this.$resultsContainer[0]);
		this.resultsComponent.onAppInit();
	}

	private bindEvents(): void {
		this.$host.on('input', this.onHostInput.bind(this));
		this.$host.on('focus', this.onHostFocus.bind(this));
		window.addEventListener('click', this.onWindowClick.bind(this));
	}

	private onWindowClick(evt: MouseEvent): void {
		const target = evt.target as HTMLElement;

		if (target !== this.host && target !== this.resultsComponent.host && !this.resultsComponent.host.contains(target)) {
			this.resultsComponent.hide();
		}
	}

	private onHostInput(): void {
		this.update();	
	}

	private onHostFocus(): void {
		if (this.host.value && (this.resultsComponent.state.results.length && !this.resultsComponent.isOpened)) {
			const { results, isLoading } = this.resultsComponent.state;

			this.resultsComponent.show();
			
			if (!isLoading && !results.length) {
				this.lauchSearch();
			}
		}
	}

	private update() {
		this.updateResultsComponentDisplay();
		this.updateHostClassName();

		if (this.host.value.length) {
			clearTimeout(this.inputDebounce);
			this.inputDebounce = setTimeout(this.lauchSearch.bind(this), 250);
		}
	}

	private updateResultsComponentDisplay() {
		if (!this.resultsComponent.attrs.isLoading) {
			this.resultsComponent.setState({
				isLoading: true
			});
		}

		if (this.host.value.length && !this.resultsComponent.isOpened) {
			this.resultsComponent.show();
		} else if (!this.host.value && this.resultsComponent.isOpened) {
			this.resultsComponent.hide();
		}
	}

	private updateHostClassName(): void {
		const className: string = 'ebox-autocomplete--filled';
		const hasClass: boolean = this.$wrapper.hasClass(className);

		if (this.host.value.length && !hasClass) {
			this.$wrapper.addClass(className);
		} else if (!this.host.value.length && hasClass) {
			this.$wrapper.removeClass(className);
		}
	}

	private lauchSearch(): void {
		this.search()
			.then(this.onSearchSuccess.bind(this))
			.catch(this.onSearchError.bind(this));
	}

	private search(): Promise<EboxAutocompleteResult[]> {
		this.resultsComponent.setState({isLoading: true});

		if (this.searchRequest && this.searchRequest.status === undefined) {
			this.searchRequest.abort();
		}

		var province = CookiesService.getCookies('eboxprovince');

		return new Promise((resolve, reject) => {
			this.searchRequest = jQuery.ajax({
				url: ajax_object.ajax_url,
				method: 'POST',
				dataType: 'JSON',
				data: {
					action: this.attrs.action,
					search: this.host.value.trim(),
					post_type: 'faq',
					province: province
				},
				success: resolve,
				error: reject
			});
		});
	}

	private onSearchSuccess(results: EboxAutocompleteResult[]): void {

		// SRSPEAK the results
		var resultSearchLabel = CoreTools.translate('résultats selon votre recherche, appuyez sur la touche tab pour voir les détails.','results according to your search, press tab to see details.');
		this.srSpeak(results.length + " " + resultSearchLabel, "polite");


		this.resultsComponent.setState({
			results,
			isLoading: false
		});
	}

	private onSearchError(err: any): void {
		if (err && err.statusText === 'abort') return;
		console.error(err);
		this.resultsComponent.setState({isLoading: false});
	}
	//#endregion

	srSpeak(text:any, priority:any) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}

export interface EboxAutocompleteResult {
	title: string;
	url: string;
}
