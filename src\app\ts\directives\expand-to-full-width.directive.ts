import { Directive } from '@core/directive.js';
import * as TimelineLite from 'gsap/TimelineLite';
import { TweenLite } from 'gsap/TweenLite';
import { Power1 } from 'gsap/EasePack';

export class ExpandToFullWidthDirective extends Directive {
	public static selector: string = '[expand-to-full-width]';
	public attrs: {
		bgColor: string;
		contentSelector?: string;
		eventSuffix?: string;
		containerClassName?: string;
	};

	public static BG_COLOR: string = '#FFF';
	public static readonly EVENT_NAMESPACE: string = 'expandtofullwidth';
	private static SVG_CIRCLE: string = `
		<svg height="1" width="1">
			<circle cx="50%" cy="50%" r="0" />
		</svg>
	`;

	private $SVGElement: JQuery;
	private $circle: JQuery;
	private $contentContainer: JQuery = jQuery(this.attrs.contentSelector);
	private $closeBtn: JQuery = jQuery('[expand-closer]', this.$contentContainer);
	private isOpened: boolean = false;
	private get hostCenter(): Coords {
		const hostRect: ClientRect = this.host.getBoundingClientRect();
		return {
			x: ~~(hostRect.left + (this.host.clientWidth / 2)),
			y: ~~(hostRect.top + (this.host.clientHeight / 2))
		};
	}

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'expand-content-selector', as: 'contentSelector', required: true},
			{name: 'expand-bg-color', as: 'bgColor', default: ExpandToFullWidthDirective.BG_COLOR},
			{name: 'expand-event-suffix', as: 'eventSuffix', default: ''},
			{name: 'expended-container-classname', as: 'containerClassName'}
		]);
		this.onInit();
	}

	private onInit(): void {
		this.injectSVG();
		this.bindEvents();
		this.setBasicContainerStyles();
	}

	private bindEvents(): void {
		const $window: JQuery = jQuery(window);

		this.$host.on('click', this.onHostClick.bind(this));
		this.$closeBtn.on('click', this.closeSVG.bind(this));
		$window.on('keydown', this.onWindowKeydown.bind(this));
		$window.on('resize', this.onWindowResize.bind(this));
	}

	private setBasicContainerStyles(): void {
		TweenLite.set(this.$contentContainer, {
			position: 'fixed',
			visibility: 'hidden',
			top: 0,
			left: 0,
			width: '100vw',
			height: '100vh',
			alpha: 0.0001
		});
	}

	private onHostClick(evt: MouseEvent): void {
		const hostRect: ClientRect = this.host.getBoundingClientRect();
		evt.preventDefault();
		this.setSVGPosition(evt.clientX, evt.clientY);
		this.openSVG(evt.clientX, evt.clientY);
	}

	private onWindowKeydown(evt: KeyboardEvent): void {
		if (evt.which === 27) this.closeSVG();
	}

	private onWindowResize(): void {
		if (!this.isOpened) return;
		
		const hostRect: ClientRect = this.host.getBoundingClientRect();
		const hostCenter: Coords = this.hostCenter;
		const distancesFromCorners: CornerDistances = ExpandToFullWidthDirective.getDistanceFromCorners(hostCenter);
		const r: number = Math.max.apply(null, distancesFromCorners) * 1.1;

		this.setSVGPosition(hostCenter.x, hostCenter.y);
		TweenLite.set(this.$circle, {
			attr: { r }
		});
	}

	private injectSVG(): void {
		this.$SVGElement = jQuery(ExpandToFullWidthDirective.SVG_CIRCLE);
		this.$circle = this.$SVGElement.find('circle');

		TweenLite.set(this.$SVGElement, {
			position: 'fixed',
			zIndex: 99999,
			overflow: 'visible',
			fill: this.attrs.bgColor
		});
		TweenLite.set(this.$circle, {
			attr: {
				r: 0
			},
			willChange: 'opacity',
			alpha: 0.75
		});
		jQuery(document.body).append(this.$SVGElement);
	}

	private setSVGPosition(x: number, y: number): void {
		TweenLite.set(this.$SVGElement, {
			top: y,
			left: x
		});
	}

	private openSVG(mouseX: number, mouseY: number): void {
		const tl: any = new TimelineLite();
		const hostRect: ClientRect = this.host.getBoundingClientRect();
		const distancesFromCorners: CornerDistances = ExpandToFullWidthDirective.getDistanceFromCorners({
			x: mouseX, y: mouseY
		});
		const r: number = Math.max.apply(null, distancesFromCorners);
		
		tl.to(this.$circle, 0.4, {
			attr: { r },
			alpha: 1,
			ease: Power1.easeOut
		})
		.add(this.showContent.bind(this), 0.15)
		.add(this.triggerEvent.bind(this, 'open'));;
		this.isOpened = true;
	}

	private closeSVG(): void {
		const tl: any = new TimelineLite();
		const hostCenter: Coords = this.hostCenter;

		tl.add(this.hideContent.bind(this))
		.set(this.$SVGElement, {
			top: hostCenter.y,
			left: hostCenter.x
		}, 0)
		.to(this.$circle, 0.25, {
			attr: {
				r: 0
			},
			alpha: 0.75,
			ease: Power1.easeOut
		}, 0)
		.add(this.triggerEvent.bind(this, 'close'));
		this.isOpened = false;
	}

	private showContent(): void {
		TweenLite.set(this.$contentContainer, {
			visibility: 'visible'
		});
		TweenLite.to(this.$contentContainer, 0.25, {
			alpha: 1,
			y: 0,
			onComplete: () => {
				if (this.attrs.containerClassName)
					this.$contentContainer.addClass(this.attrs.containerClassName);
			}
		});
	}

	private hideContent(): void {
		if (this.attrs.containerClassName)
			this.$contentContainer.removeClass(this.attrs.containerClassName);
		
		TweenLite.to(this.$contentContainer, 0.15, {
			alpha: 0.0001,
			onComplete: () => {
				TweenLite.set(this.$contentContainer, {
					visibility: 'hidden'
				});
			}
		});
	}

	private static getDistanceFromCorners(coords: Coords): number[] {
		const windowPoints: WindowCornersCoords = ExpandToFullWidthDirective.getWindowCornersCoords();
		let   output: number[] = [];

		for (const key in windowPoints) {
			output.push(
				~~Math.hypot(coords.x - windowPoints[key].left[0], coords.y - windowPoints[key].left[1]),
				~~Math.hypot(coords.x - windowPoints[key].right[0], coords.y - windowPoints[key].right[1])
			);
		}
		
		return output;
	}

	private static getWindowCornersCoords(): WindowCornersCoords {
		return {
			top: {
				left: [0, 0],
				right: [window.innerWidth, 0]
			},
			bottom: {
				left: [0, window.innerHeight],
				right: [window.innerWidth, window.innerHeight]
			}
		};
	}

	private triggerEvent(eventName: 'open'|'close'): void {
		const event: string = `${ eventName }.${ ExpandToFullWidthDirective.EVENT_NAMESPACE }.${ this.attrs.eventSuffix }`;
		jQuery(window).trigger(event);
	}
}

export interface Coords {
	[key:string]: any;
	x: number;
	y: number;
}

export interface CornerDistances {
	[key:string]: any;
	top?: {
		left: number;
		right: number;
	};
	bottom?: {
		left: number;
		right: number;
	}
}

export interface WindowCornersCoords {
	[key:string]: any;
	top: {
		left: [number, number];
		right: [number, number];
	};
	bottom: {
		left: [number, number];
		right: [number, number];
	}
}