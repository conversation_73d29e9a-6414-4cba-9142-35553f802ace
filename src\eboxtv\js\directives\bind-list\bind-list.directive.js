/* global jQuery */
import { Directive } from '@core/directive';
import { TVOrderService } from '../../services/TVOrder.service';
import { ChannelsService } from '../../services/channels.service';
import { Tools } from '@common/ts/services/Tools.service';
import { BundledChannelManager } from '@common/ts/services/BundledChannelManager';

/**
 * Note: Full Array of available channels is provided via the bind-full-list host element's
 * attribute. The directive's instance will then always try to render a list of selected channels
 * within this value.
 * 
 * IMPORTANT! See 'NOTE FOR DEPENDING DIRECTIVES' at begining of TVOrderService file.
 * {@link '../../services/TVOrder.service.js'}
 */

export class BindListDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[bind-list]';
	
	// PRIVATE PROPERTIES //
	/**
	 * @private
	 * TVOrder Service
	 */
	_$TVOrder = TVOrderService.getInstance();

	_$channels = ChannelsService.getInstance();
	
	_$emptyMessageContainer = this.$host.siblings('[bind-list-empty]');

	_currentSelectedChannels = {};

	bundleManager = BundledChannelManager.getInstance();

	/**
	 * @private
	 * Subscription to TVOrderService.selectedChannelsChange$.
	 * 
	 * @type {Subscription}
	 */
	_channelChangeSub;

	/**
	 * @private
	 * @type {WPPost[]}
	 * Alias value for _chosenList's getter
	 */
	_chosenListValue = [];

	// GETTERS AND SETTERS //
	/**
	 * @private
	 * @type {WPPost[]}
	 * Will update the host's HTML content every time its
	 * value is changed.
	 */
	get _chosenList() {
		return this._chosenListValue;
	}
	set _chosenList(val) {
		if (val !== this._chosenListValue) {
			const listHasChanged = BindListDirective.chosenListHasChanged(val, this._chosenListValue);
			
			// Re-render the list only if its items have changed
			if (listHasChanged) this._renderList(val);

			this._chosenListValue = val;
		}
	}

	
	// PUBLIC PROPERTIES //
	fullList = this._$channels.getAll((this.attrs.listType === 'others' ?
		'a-la-carte' : this.attrs.listType === 'pre-selected' ?
			'all' : this.attrs.listType)) || [];


	// ON INIT //
	constructor(host) {
		super(host, [
			{name: 'list-type', required: true},
			{name: 'item-class-name', default: ''},
			{name: 'disabled', type: 'eval', default: false},
			{name: 'item-title', default: 'Retirer la chaîne %channelName'},
			{name: 'show-costs', type: 'eval', default: false},
			{name: 'show-extra', type: 'eval', default: true},
			{name: 'show-children', type: 'eval', default: false},
			{name: 'item-tag', default: 'li'},
			{name: 'nested-children', type: 'eval', default: true},
			{name: 'show-print-titles', type: 'eval', default: false},
			{name: 'is-resume', type: 'eval', default: false}
		]);
		
		// Initializing the directive's core methods
		this._init();
	}
	_init() {
		this._subscribeToChannelOrderChange();
		this._bindEvents();
	}

	/**
	 * @private
	 * Binding host's events.
	 * 
	 * @return {void}
	 */
	_bindEvents() {
		this._bindItemClick();
	}

	/**
	* @private
	* Specifically binding click on host's children items. Each item represents
	* a channel to remove from the list when it's clicked. To make the process easier
	* and not bind and unbind every new and deleted elements, we listen to a click on
	* the host element, then we react only to its '.chosen-channel' children. An anonymous
	* function has been used in order to capture the this value representing the children
	* element that has been clicked.
	* 
	* @return {void}
	*/
	_bindItemClick() {
		const _this = this;

		this.$host.on('click', '.chosen-channel', function() {
			if (_this.attrs.disabled) return;
			const postID = jQuery(this).attr('data-channel-id');

			if (postID) {
				const type = _this.attrs.listType === 'others' ? 'a-la-carte' : _this.attrs.listType;
				_this._$TVOrder.removeChannel(parseInt(postID), type);
			}
		});
	}

	/**
	 * @private
	 * Subscribing to TVOrderService.selectedChannelsChange$.
	 * 
	 * @return {void}
	 */
	_subscribeToChannelOrderChange() {
		this._channelChangeSubscription = this._$TVOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);
	}


	/**
	 * @private
	 * Reacts to TVOrderService.selectedChannelsChange$ BehaviorSubject change. This method will
	 * filter from the this.fullList property to retrieve only the post objects
	 * which have their ID inside of the given idsSet value.
	 * 
	 * @param {object} selectedChannels Object containings all selected channels seperated by
	 * 										 channel-type (type_chaine wordpress taxonomy).
	 * @return {void}
	 */
	_onOrderChange(selectedChannels) {
		let newChosenList;
		this._currentSelectedChannels = selectedChannels;

		if (this.attrs.listType !== 'pre-selected') {
			const selectedChannelsByType = selectedChannels[this.attrs.listType];

			newChosenList = this.fullList.filter(currItem =>
				selectedChannelsByType.has(currItem.ID)
			);
		} else {
			const selectedBundles = this._$TVOrder.selectedBundles;
			const channels = [];
			
			selectedBundles.forEach(bundle => {
				bundle = this._$channels.getBundleById(bundle);

				if (bundle) {
					for (let _i = 0; _i < bundle.channels.length; _i++) {
						channels.push(bundle.channels[_i]);
					}
				}
			});
			
			newChosenList = this.fullList.filter(currItem => ~channels.indexOf(currItem.ID));
		}

		if (this.attrs.isResume) {
			const currentBundle = this._$TVOrder.getSelectedBundle();

			if (currentBundle && currentBundle.isFeatured) {
				newChosenList = [...newChosenList, ...this._$channels.getAll('base')];
			}
		}

		this._chosenList = newChosenList;
	}

	/**
	 * @private
	 * Checks first if the current host's children should be their by validating if their 
	 * data-channel-id value is within a WPPost Object in the chosenList WPPost[].
	 * @param {WPPost[]} chosenList 
	 */
	_renderList(chosenList = this._chosenList) {
		if (this.attrs.listType !== 'pre-selected') {
			const selectedChannelsByType = this._currentSelectedChannels[this.attrs.listType];
			/* Looping through each host's .chosen-channel child element to check if
			its data-channel-id value is still within a WPPost Object in the chosenList WPPost[]. */
			this.$host.find('.chosen-channel').each((i, el) => {
				const $this = jQuery(el);
				const currentID = parseInt($this.attr('data-channel-id'));

				/* If we cannot find a matching WPPost Object having the same ID
				as current element data-channel-id value, we remove that element. */
				if (!selectedChannelsByType.has(currentID))
					$this.parents('li').remove();
			});
		}

		if (!this.attrs.disabled) {
			if (!chosenList.length && this._$emptyMessageContainer.length) {
				this.$host.hide();
				this._$emptyMessageContainer.show();
				return;
			} else {
				this.$host.show();
				this._$emptyMessageContainer.hide();
			}
		}

		/* Looping through the new chosenList WPPost[] to add new channels. */
		for (const currentPost of chosenList) {
			const $liElement = jQuery(this._liTemplate(false, currentPost));

			/* If we cannot find a child element with the data-channel-id attribute value
			of the currentPost's id, we create it. */
			if (!this.$host.find(`[data-channel-id="${ currentPost.ID }"]`).length) {
				this.$host.append($liElement);
			}
		}
	}

	_liTemplate(isChildren, post) {
		const { disabled, itemClassName, showCosts, itemTitle, showChildren, nestedChildren, showExtra, showPrintTitles } = this.attrs;
		const hasExtra = post.extraCost && showExtra;

		if (post.children)
			post.children = Object.values(post.children);

		const button = `
			<button class="chosen-channel__delete-button">
				<svg class="chosen-channel__icon">
					<use xlink:href="#icon-close"></use>
				</svg>
			</button>
		`;

		return `
			<${ this.attrs.itemTag } class="${ itemClassName } choice-list__item${ isChildren ? ' choice-list__item--is-children' : '' }">
				<div class="chosen-channel${ disabled ? ' chosen-channel--disabled chosen-channel--chosen' : '' }${ hasExtra ? ' chosen-channel--has-extra' : '' }"
					 style="background-image: url('${ post.logo }');"
					 title="${ disabled ? post.post_title : itemTitle.replace('%channelName', post.post_title) }"
					 data-channel-id="${ post.ID }">
					 ${ (!disabled ? button : '') }

					${ showPrintTitles ? `<p class="chosen-channel__print-title">${ post.post_title }</p>` : '' }

					${ hasExtra ? `
					<div class="chosen-channel__extra-container">
						<span class="chosen-channel__extra-cost">+ ${ Tools.convertPriceToLocal(post.extraCost, false) }</span>
					</div>` : '' }
				</div>

				${ showChildren && post.children ?
					(nestedChildren ? `<ul class="${ this.host.className } is-children-list">` : '') +
						post.children.map(this._liTemplate.bind(this, true)).join('') +
						(nestedChildren ? '</ul>' : '')
				: '' }

				${ showCosts && !isChildren ? `<p class="chosen-channel__cost small">${ Tools.convertPriceToLocal(post.cost) }</p>` : '' }
			</${ this.attrs.itemTag }>
		`;
	}

	static chosenListHasChanged(newValue, oldValue) {
		if (newValue.length !== oldValue.length) return true;

		/* From now on, we know that both arrays have the same length
		so we know we can compare using their indexes.*/
		const newIDs = newValue.map(channel => parseInt(channel.ID));
		const oldIDs = oldValue.map(channel => parseInt(channel.ID));

		for (let i = 0; i < newIDs.length; i++) {
			const currentNewID = newIDs[i];

			if (oldIDs.indexOf(currentNewID) === -1)
				return true;
		}

		return false;
	}
}