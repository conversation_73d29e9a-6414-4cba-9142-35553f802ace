.ebtv-resume-page__section {
    &--border-right {
        border-right: 1px solid rgba($c-grey, 0.3);
        @media (max-width: 767px) {
            border-right: 0;
        }
    }
    .ebtv-section-head__title--resume {
        color: $c-primary;
    }
    .ebtv-section-head__icon--resume {
        width: 40px;
        height: 40px;
        margin-bottom: 0;
        margin-right: 10px;
    }
}

.ebtv-section-body {
    h3 {
        font-size: 25px;
        line-height: 30px;
        @media(max-width: 767px) {
            font-size: 22px;
        }
    }
    &__section-title {
        font-size: 25px;
        margin-bottom: 20px;
        @media (max-width: 767px) {
            font-size: 22px;
        }
    }
    &__section-title-note  {
        font-weight: 400;
        @media (max-width: 767px) {
            display: block;
        }
        &--option {
            @media (max-width: 991px) {
                display: block;
            }
        }
    }
    &__section-content  {
        width: 100%;
        text-align: center;
        padding-top: 30px;
        img {
            max-width: 100%;
            height: auto;
        }
    }
}

.page-template-ebox-tv-resume  {
    h1 {
        margin-bottom: 100px;
        @media (max-width:767px) {
            margin-bottom: 60px;
        }
    }
    .ebtv-resume-page__section  {
        .ebtv-section-head {
            border-bottom: 1px solid $c-primary;
        }
    }
    .ebtv-section-body-container {
        margin-bottom: 60px;
        &:nth-child(4) {
            margin-bottom: 100px;
        }
    }

    .resume-list {
        margin-left: -20px;
        margin-right: -20px;
        li {
            margin: 20px;
        }
    }
}

.custom-list  {
    margin-top: 20px;
    li {
        padding-left:25px;
        background: url('~@common/img/ic_arrow-right.svg') left 2px no-repeat;
        background-size: 16px;
        font-family: $ebtv-f-primary;
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 6px;
    }
}

// LISTING CHAINE APP WEB
.application-web {
    margin-top: 20px;
    li {
        display: inline-block;
        .channel {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 90px;
            height: 90px;
            border: 4px solid $c-primary;
            border-radius: 8px;
        }
    }
}

.customers {
    border-bottom: 1px solid $c-light-grey;
    @media (max-width: 767px) {
        border-bottom: 0px;
    }
    li {
        display: inline-block;
        margin-right: 15px;
        &:first-child {
            margin-left: -15px;
        }
        .icon-ic_customer {
            width: 44px;
            height: 44px;
            fill: $c-light-grey;
            &--paid {
                fill: $c-primary;
            }
        }
    }
}


.options-resume {
    display: flex;
    @media (max-width: 767px) {
        display: inline-block;
        width: 100%;
    }
    .ebtv-section-body-container {
        margin-bottom: 0;
    }
}

.legend {
    @media (max-width: 767px) {
        padding: 15px;
        border: 1px solid rgba($c-grey, 0.3);
    }
    .legend-list {
        margin-left: -8px;
    }
    &__title {
        font: 700 18px/20px $ebtv-f-primary;
    }
    &__user {
        background: url('../../img/ic_customer.svg') left top no-repeat;
        font: 500 12px/24px $ebtv-f-primary!important;
        position: relative;
        padding-left: 25px;
        &:first-child {
            margin-bottom: 20px;
        }
        svg {
            position: absolute;
            left: 0;
            top: -3px;
            width: 24px!important;
            height: 24px!important;
            vertical-align: middle!important;
            margin-bottom: 0!important;
        }
        &--included {
            svg {
                fill: $c-light-grey!important;
            }
        }
    }
}
.legend__user:last-of-type {
    margin-bottom: 0;
}
.ebtv-resume-page__section--border-right {
    .ebtv-section-body-container {
        @media (max-width: 767px) {
            padding-bottom: 40px;
            margin-bottom: 50px;
            border-bottom: 1px solid rgba($c-grey, 0.3);
        }
    }
}

.page-resume {
    .ebtv-section-head {
        padding-bottom: 5px;
    }
    .ebtv-section-head__title {
        @media (max-width: 767px) {
            font-size: 22px;
            // line-height: 40px;
        }
    }
    .ebtv-section-head__price-container {
        align-items: flex-end;
    }
    .ebtv-section-head__price-container__price {
        font-size: 20px;
        line-height: 24px;
        @media (max-width: 767px) {
            font-size: 16px;
        }
    }

    &__decoder-img {
        width: 100%;
        max-width: 100%;
    }

    &__decoder-description {
        text-align: justify;
        font: 400 14px/18px $ebtv-f-primary !important;
    }
}

.nb-user {
    padding-left: 138px;
    @media (max-width:991px) {
        padding-left: 70px;
    }
    @media (max-width:767px) {
        padding-left: 15px;
    }
}
.nb-recep {
    @media (max-width:991px) {
        padding-right: 50px;
    }
    @media (max-width:767px) {
        padding-right: 15px;
    }
}
.no-padding-left {
    padding-left: 0;
}