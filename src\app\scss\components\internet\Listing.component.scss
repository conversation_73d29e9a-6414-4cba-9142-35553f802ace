.Listing {
    &__item {
        min-height: 172px;
        background: $c-white;
        border-radius: 12px;
        box-shadow: 0px 2px 6px rgba(#000, 0.6);
        width: 100%;
        position: relative;
        margin: 15px 0 60px;

        &--haspromo {
            .Listing__promo {
                opacity: 1;
            }
            .Listing__apartir {
                margin-left: 0;
            }
            .Listing__prix {
                color: $c-primary;
                margin-left: 0;
                span {
                    right: -18px;

                    @include applyAtRoot('html:lang(en-CA)') {
                        right: -14px;
                        top: 31px;
                    }
                }

            }
            .Listing__prix--promo {
                display: block;
            }
        }
        @media (max-width: 1199px) {
            height: auto;
        }
        @media (min-width:768px) and (max-width: 991px) {
            min-height: 288px;
            flex: 0 0 48%;
            width: 48%;
        }
        @media (max-width: 767px) {
            min-height: 288px;
        }

        &.has-tvduo {
            position: relative;

            &:after {
                content: '';
                position: absolute;
                display: block;
                top: 0;
                right: 0;
                width: 82px;
                height: 72px;
                margin: 1px -9px 0 0;
                background: {
                    image: url('../../../img/promoduo-banner.svg');
                    repeat: no-repeat;
                    size: 100% 100%;
                    position: left top;
                };
                transform: rotate(90deg);

                @include applyAtRoot('html:lang(en)') {
                    background-image: url('../../../img/promoduo-banner-en.svg');
                }
            }

            .Listing__promo {
                display: none;
            }
        }
    }
    &__fournisseur {
        width: 40px;
        height: 40px;
        background: url('../../../img/Module/Tag/round-fournisseur.svg') center center no-repeat;
        background-size: 100%;
        display: flex;
        position: absolute;
        left: 20px;
        top: -20px;
        justify-content: center;
        align-items: center;
        color: $c-white;
        font-family: $f-primary;
        font-weight: 900;
        font-size: 16px;
    }
    &__item-content {
        display: flex;
        min-height: 172px;
        @media (max-width: 1199px) {
            min-height: 240px;
            flex-wrap: wrap;
        }
        @media (max-width: 1199px) {
            height: auto;
            flex-wrap: wrap;
        }
    }
    &__name {
        display: flex;
        justify-content: center;
        align-items: center;
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
        background: $c-medium-grey;
        width: 184px;
        p {
            margin: 0;
            font: 700 18px/25px $f-primary;
            color: $c-white;
            text-align: center;
            span {
                text-transform: uppercase;
                display: block;
                @media (max-width: 1199px) {
                    display: inline-block;
                }
            }
        }
        @media (max-width: 1199px) {
            flex: 0 0 100%;
            border-bottom-left-radius: 0;
            border-top-right-radius: 12px;
            padding-top: 10px;
            padding-bottom: 10px;
        }
        @media (max-width: 991px) {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
    &__wrapper-content {
        padding-top: 20px;
        padding-bottom: 20px;
        display: flex;
        justify-content: space-evenly;
        width: calc(100% - 184px);
        @media (max-width: 1199px) {
            width: 100%;
        }
        @media (max-width: 991px) {
            flex-wrap: wrap;
            padding-top: 0;
        }
    }




    &__prix-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
    &__apartir {
        font:400 12px/16px $f-primary;
        color: $c-medium-grey;
        margin-left: 20px;
        @media (max-width: 991px) {
            margin-left: 0;
        }
    }
    &__redprice {
        &:after {
            content: "*";
            font-size: 16px;
            color: $c-primary;
            position: absolute;
            right: -8px;
            top: 16px;
            display: inline-block;
        }
    }
    &__prix {
        font:900 46px $f-primary;
        color: $c-grey;
        position: relative;
        margin-left: 20px;
        @media (max-width: 991px) {
            margin-left: 0;
        }
        sup {
            font-size: 16px;
            vertical-align: super;
            top: 3px;
        }
        span {
            position: absolute;
            right: -17px;
            top: 30px;
            font: 300 10px $f-secondary;

            sup {
                font-size: 6px;
                position: absolute;
                top: 7px;
                font-family: inherit;
                margin-left: 2px;
            }
        }
        &--promo {
            font:900 22px $f-primary;
            margin-right: 10px;
            position: relative;
            display: none;
            sup {
                font-size: 12px;
            }
            &:after {
                content: "";
                width: 100%;
                height: 2px;
                background: $c-primary;
                opacity: 0.7;
                position: absolute;
                left: 0;
                top: 14px;
                transform: rotate(-25deg);
            }
        }
    }



    &__recap {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 0 0 210px;
        padding: 0 30px;
        .CapacityItem__title-exp {
            text-align: center;
        }
        @media (max-width: 991px) {
            flex: 1;
            align-items: center;
            padding-top: 20px;
            border-top: 1px solid $c-light-grey;
        }
    }

    &__promo {
        width: 82px;
        height: 72px;
        background: url('../../../img/Module/Flag/promo-fr.svg') center center no-repeat;
        background-size: 100%;
        position: absolute;
        right: -4px;
        top: -4px;
        display: inline-block;
        opacity: 0;
        z-index: 3;
        @media (max-width: 1199px) {
            background: url('../../../img/Module/Flag/promo-fr-red.svg') center center no-repeat;
        }
    }
}

.wrapper__listing {
    border-bottom: 1px solid #979797;
    padding-bottom: 45px;
    margin-bottom: 0px;
    @media (min-width:768px) and (max-width: 991px) {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    @media (max-width: 767px) {
        padding-bottom: 40px;
        margin-bottom: 20px;
        border: 0;
    }
    &--last {
        border-bottom: 0;
        margin-bottom: 0;
        padding-bottom: 0;
        .Listing__item {
            &:last-of-type {
                margin-bottom: 0!important;
            }
        }
    }

}

.recap__promo {
    .Listing__prix {
        font: 900 37px $f-primary;
    }
    .Listing__prix--promo  {
        font: 900 18px $f-primary;
    }
}

.icon-promo-tag {
    max-width: 170px;
    max-height: 80px;
    margin-top: 35px;
    width: 100%;
    height: auto;
}

.SingleListingPromos {
    padding: 30px 0 0;
    li {
        font: 700 16px $f-primary;
        padding: 4px 0 0 20px;
        color: #343434;
        position: relative;
        &:before {
            content: "";
            background: url('../../../img/ic_check-red.svg') center center repeat;
            width: 16px;
            height: 16px;
            left: -8px;
            background-size: 100%;
            display: inline-block;
            position: absolute;
        }
    }

}


body:not(.single-internet) {
    .Listing__recap {
        .CapacityItem__title-exp {
            @media (min-width: 768px) and (max-width: 991px) {
                display: inline-block;
            }
            @media (max-width: 500px) {
                display: inline-block;
            }
        }
    }
}

