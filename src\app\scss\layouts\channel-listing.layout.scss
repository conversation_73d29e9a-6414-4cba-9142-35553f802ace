.channel-listing {
	&__section {
		position: relative;
		padding-top: 100px;
		padding-bottom: 100px;
		
		.row-fluid {
			@include clearfix;
		}

		&:after {
			content: '';
			position: absolute;
			display: block;
			top: 100%;
			left: 50%;
			transform: translateX(-50%);
			background: $c-grey;
			width: calc(100% - 30px);
			height: 1px;
		}

		&:last-child, &--no-border {
			&:after {
				content: none;
			}
		}

		&--black {
			//background: url('~@common/img/ebox-texture-background--black.jpg');
			background-color: $c-grey;
		}

		&--search-form {
			padding-bottom: 0;
		}

		@media (max-width: 991px) {
			padding-top: 50px;
			padding-bottom: 50px;
		}
		@media (max-width: 767px) {
			padding-top: 30px;
			padding-bottom: 30px;
		}
	}

	&__section-title {
		margin-bottom: 70px;

		&--white {
			color: $c-white;
		}

		@media (max-width: 991px) {
			margin-bottom: 40px;
		}

		@media (max-width: 767px) {
			margin-bottom: 20px;
		}
	}

	&__channel-list {
		@media (max-width: 991px) {
			margin-bottom: 20px;
		}

		.channel-list {
			&__header {
				color: $c-medium-grey;
				border-color: currentColor;
			}
			&__title {
				color: currentColor;
			}

			@media (min-width: 601px) and (max-width: 991px) {
				&__body {
					padding: 0 !important;
				}
			}

			@media (max-width: 600px) {
				&__header {
					padding: 0 0 5px;
				}
				&__body {
					padding: 30px 0 0 !important;
				}
			}
		}
	}
}