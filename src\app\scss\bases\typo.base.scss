* {
	font-family: $f-primary;
}

h1, h2, h3, h4, h5, h6, p, blockquote, article, address {
	margin: 0;
	padding: 0;
}

ul, ol {
	list-style: none;
	margin: 0;
	padding: 0;
}

a {
	&:hover, &:active, &:focus {
		color: currentColor;
		text-decoration: none;
	}
}

h1 {
	font: 800 50px/55px $f-primary;
	text-transform: uppercase;
}

h2 {
	font: 800 45px/50px $f-primary;
	text-transform: uppercase;
}

h3 {
	font: 700 35px/40px $f-primary;
}

h4 {
	font: 700 25px/30px $f-primary;
}

h5,
.style-h5 {
	font: 700 20px/24px $f-primary;
}

h6 {
	font: 400 18px/20px $f-primary;
}

p {
	font: 16px/22px $f-primary;
	a {
		color: $c-primary;
		transition: $t-primary;
	}
}

a.common-link {
	color: currentColor;
	text-decoration: underline;
	transition: color 0.1s linear;
	
	&:hover {
		color: $c-primary;
	}
}

.bold, strong, .italic, em {
	font-size: inherit;
	font-weight: inherit;
	font-family: inherit;
}

.bold, strong {
	font-weight: 700;
}

.italic, em {
	font-style: italic;
}

.page-title {
	font: 400 60px/65px $f-primary;
}

.quote {
	@extend .italic;
	font: 400 30px/35px $f-primary;
}

ul {
	margin: 0 0 0 20px;
}

li {
	font: 16px $f-primary;
}

ol {
	margin: 0 0 0 15px;
	li {
		list-style-type: decimal;
		margin: 0 0 20px 0;
		line-height: 22px;
		a {
			color: $c-primary;
			&:hover {
				color: $c-grey;
			}
		}
	}
}

.small.small.small.small {
	font: 400 12px/16px $f-primary;
}

@media (max-width: #{ map-get($breakpoints, tabletLandscape) }) {
	h1, .page-title {
		font: 400 60px/62px $f-primary;
	}

	h1 {
		font: 800 35px/38px $f-primary;
		text-transform: uppercase;
	}

	h2 {
		font: 800 26px/32px $f-primary;
		text-transform: uppercase;
	}

	h3 {
		font: 700 26px/32px $f-primary;
	}

	h4 {
		font: 700 22px/26px $f-primary;
	}
}

@media (max-width: #{ map-get($breakpoints, mobileLandscape) }) {
	.page-title {
		font: 400 45px/48px $f-primary;
	}

}

@media (max-width: #{ map-get($breakpoints, mobile) }) {
	.page-title {
		font: 500 36px/40px $f-primary;
	}

	h1 {
		font: 800 24px/28px $f-primary;
		text-transform: uppercase;
	}

	h2 {
		font: 800 22px/24px $f-primary;
		text-transform: uppercase;
	}

	h3 {
		font: 700 24px/26px $f-primary;
	}

	h4 {
		font: 700 22px/26px $f-primary;
	}
}

@media (max-width: 400px) {
	.page-title {
		font: 500 30px/32px $f-primary;
	}
}

.uppercased {
	text-transform: uppercase;
}
