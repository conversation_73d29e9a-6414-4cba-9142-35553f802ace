.TwoCol-LeftImgRightTextGrey {
    padding: 80px 0;
    background: url('../../../img/black-pattern.png') left top repeat;
    width: 100%;
    display: block;
    position: relative;
    &.LightGrey {
        background: url('../../../img/grey-pattern.png') left top repeat;
        .TwoCol-LeftImgRightTextGrey__leftImg {
            padding: 0 35px 0 15px;
            @media (max-width: 767px) {
                padding: 0 15px;
            }
            img {
                width: 100%;
            }
        }
        .TwoCol-LeftImgRightTextGrey__rightTxt {
            padding: 0 0 0 35px;
            @media (max-width: 767px) {
                padding: 0 15px;
            }
        }
    }
    &__wrapper {
        display: flex;
        @media (max-width: 991px) {
            display: inline-block;
        }
    }
    &__title {
        font: 700 35px/40px $f-primary;
        color: $c-white;
        text-transform: uppercase;
        margin: 0;
        @media (max-width: 991px) {
            font: 700 16px/20px $f-primary;
        }
        @media (max-width: 767px) {
            margin: 0 auto;
        }
    }
    &__sous_titre {
        font: 400 30px/35px $f-secondary;
        color: $c-white;
        margin: 0 0 25px 0;
        @media (max-width: 991px) {
            font: 300 16px/20px $f-secondary;
        }
        @media (max-width: 767px) {
            margin: 0 auto 25px;
        }
    }
    &__leftImg {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 15px;
        img {
            max-width: 100%;
            height: auto;
            @media (max-width: 991px) {
                height: auto;
                max-height: 100%;
                width: auto;
                max-width: 100%;
            }
            @media (max-width: 767px) {
                width: 80%;
                height: auto;
                margin: 0 auto;
                display: block;
            }
        }
        @media (max-width: 991px) {
            width: 40%;
            padding: 0 22px;
            margin: 0;
        }
        @media (max-width: 767px) {
            width: 100%;
            padding: 0 15px;
        }
    }
    &__rightTxt {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 15px 0 45px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        @media (max-width: 991px) {
            width: 60%;
            padding: 0 19px;
            text-align: left;
            margin-top: 30px;
        }
        @media (max-width: 767px) {
            width: 100%;
            padding: 0 15px;
            text-align: center;
        }
        .Wysiwyg {
            width: 100%;
            color: $c-white;
            @media (max-width: 767px) {
                text-align: left;
            }
            .icon-text {
                float: left;
                margin: -2px 10px 0 0
            }
            h2, h3, h4 {
                margin: 0 0 60px;
                @media (max-width: 1024px) {
                    margin: 0 0 10px;
                }
            }
            h5 {
                margin: 0 0 10px;
            }
            ul {
                margin: 0;
                li {
                    background: url('~@app/img/ic_check-white.svg') no-repeat left 2px;
                    background-size: 16px 16px;
                }
            }
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0 70px;
    }
    .ButtonEffect {
        @media (max-width:991px) {
            margin: 0;
        }
        @media (max-width:767px) {
            margin: 0 auto;
        }
    }
    &--white {
        background: transparent;
        .TwoCol-LeftImgRightTextGrey__title {
            color: $c-grey;
        }
        .ButtonEffect {
            margin-top: 24px;
        }
    }
    &--single-internet {
        .TwoCol-LeftImgRightTextGrey__title {
            font: 900 42px/50px $f-primary;
            width: 100%;
            @media (max-width: 991px) {
                font: 700 16px/20px $f-primary;
                margin: 0 auto;
            }
        }

    }
}
