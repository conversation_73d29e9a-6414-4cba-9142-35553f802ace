/* global jQuery */
import { TweenLite } from 'gsap/TweenLite';
import { Power2 } from 'gsap/EasePack';
import { Directive } from '@core/directive';

export class StickyResumeDirective extends Directive {
	static selector = '[sticky-resume]';

	_isSticky = false;
	_scrollIsBound = false;
	_$cageElement = jQuery(this.host).parent();
	_mediaMatchValue = this.attrs.mediaQuery ? window.matchMedia(this.attrs.mediaQuery).matches : true;

	get _mediaMatch() {
		return this._mediaMatchValue;
	}
	set _mediaMatch(val) {
		if (val !== this._mediaMatchValue) {
			this._mediaMatchValue = val;
			this._onMediaMatchChange(val);
		}
	}

	get _basePosition() {
		return this._$cageElement[0].getBoundingClientRect().bottom;
	}

	constructor(host) {
		super(host, [
			{name: 'sticky-media-query', as: 'mediaQuery'}
		]);
		this._init();
	}

	_init() {
		this._bindEvents();
		this._setPosition();
	}

	_bindEvents() {
		jQuery(window).on('resize', this._onResize.bind(this));
		this.$host.on('opening', this._onHostOpening.bind(this));

		if (this._mediaMatch)
			this._bindScrollEvent();
	}

	_bindScrollEvent() {
		jQuery(window).on('scroll.stickyResume', this._onScroll.bind(this));
		this._scrollIsBound = true;
	}

	_unbindScrollEvent() {
		jQuery(window).off('scroll.stickyResume');
		this._scrollIsBound = false;
	}

	_onHostOpening() {
		if (this._basePosition < window.innerHeight && this._mediaMatch)
			this._scrollToCageBottom();
	}

	_onResize() {
		if (this.attrs.mediaQuery !== undefined)
			this._mediaMatch = window.matchMedia(this.attrs.mediaQuery).matches;

		this._setPosition();
	}

	_onScroll(evt) {
		this._setPosition();
	}

	_onMediaMatchChange(val) {
		if (!val) {
			this._reset();
			this._unbindScrollEvent();
		} else if (!this._scrollIsBound) {
			this._bindScrollEvent();
		}
	}

	_reset() {
		this._unsetSticky();
	}

	_setPosition() {
		if (!this._mediaMatch) return;

		if (this._basePosition < window.innerHeight && this._isSticky)
			this._unsetSticky();
		else if (this._basePosition >= window.innerHeight && !this._isSticky)
			this._setSticky();
	}

	_setSticky() {
		this.host.style.position = 'fixed';
		this._isSticky = true;
	}

	_unsetSticky() {
		this.host.style.position = '';
		this._isSticky = false;
	}

	_scrollToCageBottom() {
		const baseOffsetBottom = this._$cageElement.offset().top + this._$cageElement.height();

		TweenLite.to(jQuery('html, body'), 0.4, {
			scrollTop: baseOffsetBottom - window.innerHeight,
			ease: Power2.easeOut
		});
	}
}