.CallTo {
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 0;
    &__plus {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 30px;
        height: 30px;
        background: $c-primary;
        justify-content: center;
        align-items: center;
        display: none;
        svg {
            width: 15px!important;
            height: 15px!important;
            display: inline-block;
            fill: $c-white!important;
        }
        @media (max-width: 767px) {
            display: flex;
        }
    }
    &__linkmobile {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 4;
        display: none;
        @media (max-width:767px) {
            display: inline-block;
        }
    }
    &__item {
        display: flex;
        text-align: center;
        flex: 0 0 33%;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        background: $c-white;
        overflow: hidden;
        height: 492px;
        transform: scale(1,1);
        box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.5);
        transition: $t-primary;
        svg {
            width: 80px;
            height: 80px;
            fill: $c-black;
            margin-bottom: 20px;
            transition: $t-primary;
            @media (max-width: 1024px) {
                margin-bottom: 0;
                width: 60px;
                height: 60px;
            }
            @media (max-width: 767px) {
                margin-bottom: 0;
                width: 40px;
                height: 40px;
            }
        }
        @media (max-width: 1024px) {
            padding: 15px 24px;
            flex: 0 0 32%;
            height: 300px;
        }
        @media (max-width: 767px) {
            padding: 0px 0px 50px;
            min-height: 174px;
        }

        &:first-of-type {
            @media (max-width: 767px) {
                flex: 0 0 100%;
                margin-bottom: 10px;
                padding-bottom: 0;
                height: 174px;
                .ItemContainer__left {
                    svg {
                        margin-top: 4px;
                        height: 60px!important;
                        width: 60px!important;
                    }
                }
                .CallTo__title {
                    margin-top: -25px;
                }
                .ItemContainer__right {
                    margin-top: -18px;
                }
            }
        }
        &:last-of-type,
        &:nth-of-type(2) {
            @media (max-width: 767px) {
                flex: 0 0 100%;
                height: 174px;
            }
        }

    }
    &__back {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        transition: all 0.4s $cubic;
        transform: scale(1,1);

        &--internet {
            //background: url('../../../img/callto-internet-back.jpg') center center no-repeat;
            //background-size: cover;
            /*@media (max-width: 767px) {
                background: $c-grey;
            }*/
        }
        &--television {
            //background: url('../../../img/callto-television-back.jpg') center center no-repeat;
            //background-size: cover;
            /*@media (max-width: 767px) {
                background: $c-black;
            }*/
        }
        &--telephonie {
            //background: url('../../../img/callto-telephonie-back.jpg') center center no-repeat;
            //background-size: cover;
            /*@media (max-width: 767px) {
                background: $c-grey;
            }*/
        }
    }
    &__title {
        margin: 0;
        font: 900 60px/64px $f-primary;
        text-transform: uppercase;
        color: $c-black;
        position: relative;
        pointer-events: none;
        display: inline-block;
        transition: $t-primary;
        &:after {
            background: url('../../../img/line-black.svg') center center no-repeat;
            content: "";
            left: 50%;
            transform: translateX(-50%);
            position: absolute;
            bottom: -9px;
            width: 100%;
            background-size: 100%;
            box-sizing: border-box;
            height: 10px;
            display: block;

        }
        span {
            font: 300 20px/30px $f-secondary;
            position: absolute;
            right: 0;
            bottom: -50px;
            text-transform: none;
            white-space: nowrap;
            //transition: $t-primary;
            &:before {
                width: 32px;
                height: 32px;
                content: "";
                background: url('../../../img/Icon/drawing-arrow04-black.svg') left top no-repeat;
                position: absolute;
                transform: rotate(-26deg);
                left: -38px;
                top: -5px;
                @media (max-width: 1199px) {
                    top: -10px;
                }
                @media (max-width: 767px) {
                    width: 12px;
                    height: 12px;
                    background-size: 100%;
                    left: -18px;
                    top: -2px;

                }
            }
            @media (max-width: 1199px) {
                font: 300 12px/22px $f-secondary;
                bottom: -40px;
            }
            @media (max-width: 767px) {

                font: 300 10px/12px $f-secondary;
                bottom: -25px;
            }
        }
        @media (max-width: 1330px) {
            font: 900 55px/64px $f-primary;
        }
        @media (max-width: 1199px) {
            font: 900 35px/40px $f-primary;
        }
        @media (max-width: 767px) {
            margin-top: 10px;
            font: 900 24px/24px $f-primary;
        }
    }
    .ButtonEffect {
        margin-top: 120px;
        @media (max-width: 1024px) {
            width: 100%;
            margin-top: 75px;
        }
    }
    @media (max-width: 767px) {
        flex-wrap: wrap;
    }
}
.ItemContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    @media (max-width: 991px) {
        width: 100%;
    }
    @media (max-width: 767px) {
        width: auto;
    }
    &--internet {
        @media (max-width: 767px) {
            display: flex;
            flex-direction: row;
            .CallTo__title {
                width: 132px;

            }
        }
    }
}
.CallTo__item {
    &:hover {
        .CallTo__back {
            transform: scale(1.1,1.1) rotate(-1deg);
            @media (max-width: 991px) {
                transform: scale(1) rotate(0deg);
            }

        }
        background: $c-primary;
        svg {
            fill: $c-white;
        }
        .CallTo__title {
            color: $c-white;
            span {
                //color: $c-white;
                &:before {
                    background: url('../../../img/Icon/drawing-arrow04-white.svg') left top no-repeat;
                }
            }
            &:after {
                background: url('../../../img/line-white.svg') center center no-repeat;
                background-size: 100%;
            }
        }
        .ButtonEffect {
            color: $c-primary;
            &:before {
                background: $c-white;
            }
            &:hover {
                color: $c-white;
            }
        }
        .Callto__plus {
            background: $c-white;
            svg {
                fill: $c-primary!important;
            }
        }
    }
}

.duet-date__cell {
    min-width: auto;
}
.duet-date__dialog {
    z-index: 9999;
}

.page-id-7551 {
    .ButtonEffect:focus {
        outline: 2px solid #BE2323!important;
    }
}

.page-template-page-accueil-affaires {
    .CallTo__title {
        font: 900 35px/40px "BrandonGrotesque", sans-serif;
    }
}
