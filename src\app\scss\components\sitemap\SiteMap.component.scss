.siteMap {
    padding: 100px 0;
    @media (max-width: 991px) {
        padding: 70px 0;
        .col-md-12 {
            padding: 0 20px;
        }
    }
    @media (max-width: 767px) {
        .col-md-12 {
            padding: 0 15px;
        }
    }
    .main-sitemap-ul {
        margin: 0;
        > .page_item_has_children {
            width: 33.33%;
            float: left;
            @media (max-width: 991px) {
                width: 100%;
                margin: 0 0 20px;
                &:last-of-type {
                    margin: 0;
                }
            }
            > a {
                font-weight: 800;
                font-size: 28px
            }
            &:first-of-type {
                a {
                    font-size: 20px;
                }
                ul.children {
                    margin: 0!important;
                }
            }
        }
        .page_item a{
            text-decoration: underline;
        }
        .province a{
            font-weight: 800;
            font-size: 28px;
            color: #343434;
        }
        li {
            font: 600 18px/22px $f-secondary;
            color: $c-grey;
            list-style: none;
            margin: 0 0 10px 0;
            padding: 0;
            a {
                color: $c-grey;
                transition: $t-primary;
                &:hover {
                    color: $c-primary;
                }
            }
            ul.children {
                margin-top: 15px!important;
                li {
                    margin: 0 0 15px;
                    @media (max-width: 991px) {
                        &:last-of-type {
                            margin: 0;
                        }
                    }
                    > a {
                        font-size: 24px;
                        line-height: 26px;
                    }
                    ul.children {
                        margin-top: 7px!important;
                        li {
                            > a {
                                font-size: 20px;
                                line-height: 22px;
                                color: $c-medium-grey;
                                &:hover {
                                    color: $c-primary;
                                }
                            }
                        }
                        ul.children {
                            margin-top: 7px!important;
                            li {
                                > a {
                                    font-size: 16px;
                                    font-weight: 500;
                                    line-height: 18px;
                                    color: $c-medium-grey;
                                    &:hover {
                                        color: $c-primary;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}