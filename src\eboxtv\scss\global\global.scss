.textured-bg {
    background: {
        size: initial;
    };

    &--black {
        background-image: url('~@common/img/ebox-texture-background--black.jpg');
        color: $c-white;
    }

    &--black-dark {
        background-image: url('~@app/img/Body.png');
        color: $c-white;
    }

    &--red {
        background-image: url('~@common/img/ebox-texture-background--red.jpg');
        color: $c-black;
    }
}

template, .template {
    display: none;
}

.clearfix {
    &:after {
      content: "";
      display: table;
      clear: both;
    }
}

.bs-container.bs-container {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    width: 100%;
    max-width: 1170px;
    margin: 0 auto;

    @media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
    @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
}

.container-lg {
    @media (min-width: 992px) {
        width: 100%;
        max-width: 1200px;
    }
}


// Everything inside ebox-tv parent page or in its children pages
body.parent-pageid-4577, body.page-id-4685 {
    #sdrn_bar {
        z-index: 100 !important;
    }
}