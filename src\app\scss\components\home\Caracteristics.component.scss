.HomeCaracteristics {
	height: auto;
	width: 100%;
	display: block;
	padding: 80px 0 0;
	
	@media (min-width: 768px) and (max-width: 1199px) {
		padding: 60px 0 0;
	}
	&__section {
		padding: 0 15px;
	}
	&__title {
		font: 900 45px/50px "BrandonGrotesque", sans-serif!important;
		text-align: center;
		color: #ffffff;
		float: left;
		width: 100%;
		@media (min-width: 768px) and (max-width: 1199px) {
			font: 900 26px/32px "BrandonGrotesque", sans-serif!important;
		}
		@media (max-width: 767px) {
			font: 900 22px/24px "BrandonGrotesque", sans-serif!important;
			text-align: left;
		}
	}
	&__desc, &__desc-title {
		font: 400 16px/22px "BrandonGrotesque", sans-serif!important;
		color: #C7C7C7;
		text-align: center;
		float: left;
		width: 100%;
		margin-top: 20px;
		@media (max-width: 767px) {
			text-align: left;
		}
	}

	&__desc {
		a {
			color: inherit;
			text-decoration: underline;
			transition: color 0.1s linear;

			&:hover {
				color: #FFF;
				text-decoration: underline;
			}
		}
	}

	&__col-title {
		margin-bottom: 40px;

		.title {
			font: 700 20px/24px "BrandonGrotesque", sans-serif!important;
			color: #C7C7C7;
			text-transform: uppercase;
		}

		&-link {
			display: inline-block;
			margin-top: 8px;
			color: $c-light-grey;
			text-decoration: underline;
			transition: color 0.1s linear;

			&:hover,
			&:focus {
				color: #FFF;
				text-decoration: underline;
			}
		}
	}

	&__desc-title {
		margin-top: -25px;
		@media (max-width: 767px) {
			margin-bottom: 15px;
		}
	}

	&__imgsection {
		text-align: center;
		color: #C7C7C7;
		padding: 80px 0;
		float: left;
		width: 100%;

		@media (max-width:1199px){
			padding:60px 0;
			border-bottom:1px #979797 solid;
		}

		@media (max-width:767px){
			padding:60px 0 0;
			border-bottom:none;
		}
	}

	&__imgsection > .border {
		border-right: 1px solid #979797;

		@media (max-width:767px) {
			border-right:none;
				border-bottom: 1px solid #979797;
				padding: 0 0 60px 0;
				margin-bottom: 60px;

		}

	}

	&__left {
		max-width: 500px;
		margin: 0 auto;
	}

	&__block-title {
		.Circle {
			width: 130px;
			height: 130px;
			background-size: 130px 130px;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			top: -65px;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			&--red {
				background: url('../../../img/back-red.png') center center no-repeat;
			}
			&--grey {
				background: url('../../../img/back-grey.png') center center no-repeat;
			}
			&__price  {
				position: relative;
				color: $c-white;
				margin-top: 5px;
				padding-top: 5px;
				border: none!important;
				font: 900 45px/36px $f-primary!important;
				width: 62px;
				margin: 0!important;
				sup {
					position: relative;
					top: -13px;
					font: 900 17px $f-primary!important;
				}
			}
			&__mois {
				position: absolute;
				font: 400 8px/12px $f-secondary;
				left: 2px;
				bottom: -8px;
				width: 40px;
		
			}
		}
	}
	&__devices-image {
		max-width: 100%;
		
		@media (max-width: 1199px) {
			width: 100%;
		}
		&.logos-appareils {
			margin: 50px 0 80px;
			@media (max-width: 991px) {
				margin: 40px 0 60px;
			}
		}
	}

	.more-devices {
		position: relative;
		display: none;
		a {
			color: $c-light-grey;
			&:hover {
				svg {
					fill: $c-primary;
				}
				p {
					color: $c-primary;
				}
			}
		}
		svg {
			width: 16px;
			height: 16px;
			fill: $c-light-grey;
			display: inline;
			margin: 0 5px 0 0;
			vertical-align: middle;
			transition: all 0.3s $cubic;
		}
		p {
			font: 700 16px/16px $f-primary;
			display: inline;
			vertical-align: middle;
			color: $c-light-grey;
			transition: all 0.3s $cubic;
		}
	}

	&__right {
		max-width: 370px;
		margin: 0 auto;
	}

	@media (max-width: 767px) {
		&__right, &__left {
			width: 100%;
			float: none;
			text-align: center;
			max-width: none;

			img {
				width: 370px;
				max-width: 100%;
			}
		}

		&__devices-image {
			&.logos-appareils {
				margin: 10px 0 40px;
			}
		}
	}
}