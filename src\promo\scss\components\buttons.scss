.ebox-btn {
	display: inline-block;
	appearance: none;
	border: none;
	background: $c-primary-dark;
	font: 900 18px/45px $f-primary;
	text-transform: uppercase;
	color: #FFF;
	padding: 11px 20px;
	height: auto;
	line-height: 1;
	min-width: 216px;
	text-align: center;
	cursor: pointer;
	letter-spacing: 1px;
	will-change: transform;
	transform: scale(1);
	transition: all 0.25s $cubic;

	&:hover {
		background: $c-primary;
		color: $c-white;
		text-decoration: none;
		// transform: scale(1.05);
	}

	&:focus {
		background: $c-primary-dark;
		color: #FFF;
		text-decoration: none;
	}
}