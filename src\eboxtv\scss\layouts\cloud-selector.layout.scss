.cloud-selector {
	&__choice {
		&-list {
			margin: 0;
			padding: 0;
		}

		&-item {
			font: 600 16px/24px $f-primary;

			&:not(:last-child) {
				margin-bottom: 16px;
			}
		}
	}

	&__radio-label {
		display: flex;
		align-items: center;
		cursor: pointer;
	}

	&__radio {
		position: absolute;
		visibility: hidden;

		&:checked {
			+ .cloud-selector__fake-radio {
				&:after {
					transform: scale(1);
				}
			}
		}
	}

	&__fake-radio {
		position: relative;
		display: inline-block;
		flex-shrink: 0;
		align-self: baseline;
		top: 3px;
		width: 16px;
		height: 16px;
		color: $c-primary;
		border: 2px solid currentColor;
		border-radius: 100%;
		margin-right: 8px;
		
		&:after {
			content: '';
			display: block;
			position: absolute;
			top: 50%;
			left: 50%;
			width: 8px;
			height: 8px;
			margin: -4px 0 0 -4px;
			transform: scale(0);
			border-radius: 100%;
			background-color: currentColor;
			transition: transform 0.2s $cubic;
		}
	}
}

.none-available-text{
	margin-bottom: 30px;
}