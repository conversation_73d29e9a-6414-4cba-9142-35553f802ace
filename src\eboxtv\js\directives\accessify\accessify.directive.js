/* global jQuery */
import { Directive } from '@core/directive';
import { CoreTools } from '@core/helpers';

export class AccessifyDirective extends Directive {
	static selector = '[data-accessify]';
	static defaultOptions = {
		events: ['enter', 'space'], // Which events to trigger click on
		trigger: ['click'],
		blurTrigger: [],
		prevent: [], // Which native event to prevent actions
		tabindex: 0, // Custom tabindex to give to host element
		disabled: false
	};

	constructor(host) {
		super(host, [
			{name: 'data-accessify', as: 'options', type: 'eval', default: {}},
			{name: 'data-tabindex', as: 'tabindex', force: true}
		]);
		this._onInit();
	}

	_onInit() {
		this.attrs.tabindex = '0';
		this._mergeOptions();
		this._bindEvents();
	}

	_mergeOptions() {
		this.attrs.options = jQuery.extend({},
			AccessifyDirective.defaultOptions,
			this.attrs.options
		);
	}

	_bindEvents() {
		this.$host.on('keydown', this._onHostKeydown.bind(this));
		this.$host.on('blur', this._onHostBlur.bind(this));
	}

	_onHostKeydown(evt) {
		const { options } = this.attrs;
		if (options.disabled) return;

		if (options.prevent.length && CoreTools.isKey(options.prevent, evt.which)) {
			evt.preventDefault();
		} else if (CoreTools.isKey(options.events, evt.which)) {
			evt.preventDefault();
			this._triggerMultipleEvents(options.trigger);
		} else if (CoreTools.isKey('esc', evt.which)) {
			this.host.blur();
			this.$host.trigger('blur');
		}
	}

	_onHostBlur() {
		this._triggerMultipleEvents(this.attrs.options.blurTrigger);
	}

	_triggerMultipleEvents(evtArr) {
		if (evtArr.length) {
			evtArr.forEach(evt => {
				this.$host.trigger(evt);
			});
		}
	}
}