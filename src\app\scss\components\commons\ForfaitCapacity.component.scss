.ForfaitCapacity {
    width: 100%;
    //height: 325px;
    background: $c-white;
    border-radius: 12px;
    box-shadow: 0 2px 6px 0 rgba(#000, 0.5);
    padding: 50px 40px 40px;
    .col-md-4 {
        svg {
            max-width: 100%;
        }
    }

    .CapacityItem {
        @media (max-width: 991px) {
            flex: 1 1 auto;
            //height: 60px;
            margin-right: 0;

            &:nth-child(1) {
                min-height: 60px;
                background: none;
                color: $c-white;
            }

            &:nth-child(2) {
                min-height: 60px;
                background: none;
                border-right: 0px!important;
                color: $c-white;
            }
        }
    }

    @media (max-width: 991px) {
        height: auto;
        padding: 30px 15px;
    }
}

.WrapperCapacity {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid $c-light-grey;
    padding-bottom: 30px;

    @media (max-width: 991px) {
        justify-content: space-between;

        .CapacityItem {
            flex: 0 0 auto !important;

            &__wrapper {
                width: 140px;

                @media (max-width: 499px) {
                    width: 85px;
                }
            }
        }
    }
}

.SuperOff {
    padding-top: 30px;
    display: flex;
    &--disabled {
        opacity: 0.3;
        input[type=checkbox] {
            pointer-events: none;
        }
    }
    &__checkbox {
        margin-right: 10px;
        input[type=checkbox] {
            appearance: none;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 6px;
            border: 1px solid #979797;
            position: relative;
            transition: $t-primary;
            &:after {
                opacity: 0;
                transition: $t-primary;
            }
            &:checked {
                border: 1px solid $c-primary;
                &:after {
                    content: '';
                    width: 20px;
                    height: 20px;
                    position: absolute;
                    border-radius: 10px;
                    background: url('../../../img/Icon/ic_check_list.svg') center center no-repeat;
                    left: 50%;
                    opacity: 1;
                    top: 50%;
                    transform: translate(-50%, -50%);
                }

            }
        }
    }
    &__explication {
        &-title {
            font: 700 20px/24px $f-primary;
            margin: 0;
        }
        &-subtitle {
            font: 400 16px/22px $f-primary;
            color: $c-medium-grey;
            margin: 0;
        }

    }
}