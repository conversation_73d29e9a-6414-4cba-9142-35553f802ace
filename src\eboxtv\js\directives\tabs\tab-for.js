/* global jQuery */

export class TabFor {
	get isOpened() {
		return this.$host.attr('data-tab-opened') !== undefined;
	}
	set isOpened(val) {
		if (val !== this._isOpenedValue) {
			this._isOpenedValue = val;

			this._checkStatus();
		}
	}

	constructor(host, child, activeClassName = 'active') {
		this.host = host;
		this.$host = jQuery(this.host);
		this.$child = jQuery(child);
		this.activeClassName = activeClassName;

		this._init();
	}

	_init() {
		this._checkStatus();
	}

	_checkStatus() {
		const noOpened = !document.querySelectorAll('[data-tab-for][data-tab-opened]').length;
		if (noOpened) return;

		if (this.isOpened)
			this.open();
		else
			this.close();
	}

	open() {
		this.$child
			.show()
			.attr('aria-hidden', 'false');
		this.$host.attr('data-tab-opened', '')
			.addClass(this.activeClassName)
			.attr('aria-selected', 'true');
		this._isOpenedValue = true;
	}

	close() {
		this.$child
			.hide()
			.attr('aria-hidden', 'true');
		this.$host.removeAttr('data-tab-opened')
			.removeClass(this.activeClassName)
			.attr('aria-selected', 'false');
		this._isOpenedValue = false;
	}
}