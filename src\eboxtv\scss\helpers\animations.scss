$cubic: cubic-bezier(.55, 0, .1, 1);

/* Used when the channel choices are added. */
@keyframes pop {
    0% {
        transform: scale(0) rotate(0deg);
    }

    50% {
        transform: scale(1.2) rotate(5deg);
    }
}

@keyframes expand {
    from {
        clip-path: circle(0% at 50% 50%);
    }

    to {
        clip-path: circle(100% at 50% 50%);
    }
}

@keyframes tilt {
	0% {
		transform: rotate(-20deg);
	}

	15% {
		transform: rotate(20deg);
	}

	30% {
		transform: rotate(0deg);
	}
}

@keyframes arrowRight {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(5px);
    }
}