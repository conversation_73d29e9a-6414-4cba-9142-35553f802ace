@import '~@eboxtv/scss/layouts/order-tabs.layout.scss';

.TextIconBtn {
	display: inline-block;
	color: $c-primary;
	cursor: pointer;
	transition: color 0.1s linear;

	&__icon, &__text {
		display: inline-block;
		vertical-align: middle;
	}

	&__icon {
		width: 16px;
		height: 16px;
		fill: currentColor;
	}

	&__text {
		font: 700 16px/22px $f-primary;
		color: currentColor;
	}

	&:hover {
		color: $c-black;
	}
}

body.page-template-page-forfaits {
	.order__tabs {
		position: relative;
		left: auto;
		top: auto;
		transform: none;
	}

	.order-tabs {
		&__tab {
			padding: 15px 25px;
			text-transform: uppercase;
			font-weight: 900;
			width: 180px;

			&--active {
				padding: 20px;
				background-image: url('~@common/img/ebox-texture-background--red.jpg');
			}

			&-detail {
				text-transform: none;
				font-weight: 400;
			}

			@media (max-width: 991px) {
				font: 900 14px/18px $f-primary;
				width: 100px;
				padding: 10px 20px !important;

				&--active {
					padding: 15px !important;
				}

				&-detail {
					display: none;
				}
			}
			@media (max-width: 740px) {
				padding: 25px 15px;
			}
		}
	}

	.ListingForfaits {
		&__listing-container {
			padding: 60px;
			border: 1px solid $c-light-grey;
			border-radius: 8px;

			@media (max-width: 991px) {
				padding: 30px;
			}
			@media (max-width: 740px) {
				padding: 25px 15px;
			}
			@media (max-width: 400px) {
				padding: 15px 10px;
				// border: none;
			}

			&--red {
				border-color: $c-primary;
			}
		}
	}

	.PostCardItem {
		margin-right: 3%;
		width: 31.3334%;

		&__bottom {
			display: flex;
			flex-direction: column;
			align-items: center;
			min-height: 1px !important;
		}

		&:nth-child(3n+3) {
			margin-right: 0;
		}

		.TextIconBtn {
			margin-top: auto;
			margin-bottom: 40px;
		}

		.Bubble {
			@media (max-width: 991px) {
				max-height: 100vh;
				overflow: auto;
			}

			&--step-up {
				@media (min-width: 1025px) {
					margin-bottom: 80px;
				}
				@media (max-width: 1024px) {
					margin-bottom: 0;
				}
			}
		}

		&.PostCardItem.PostCardItem.PostCardItem {
			@media (max-width: 1199px) {
				margin: 0 2% 50px 0;
				width: 49%;
	
				&:nth-child(3n+3) {
					margin-right: 2%;
				}
				&:nth-child(2n+2) {
					margin-right: 0;
				}
			}
			@media (max-width: 685px) {
				margin: 0 0 50px;
				width: 100%;
	
				&:nth-child(3n+3) {
					margin-right: 0;
				}
				&:nth-child(2n+2) {
					margin-right: 0;
				}
			}
		}
	}
}