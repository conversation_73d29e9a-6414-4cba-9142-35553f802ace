@keyframes spin {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}
@keyframes fadeIn {
    from { opacity: 0; }
  }

.HomeSlider {
    position: relative;
    width: 100%;
    height: 709px;
    overflow: hidden;

    // Christmas ribbon on home slider
    /*&:after {
        content: '';
        position: absolute;
        display: block;
        width: 20vw;
        height: 20vw;
        min-width: 160px;
        min-height: 160px;
        max-width: 340px;
        max-height: 340px;
        top: -10px;
        left: -10px;
        background: {
            image: url('../../../img/christmas-ribbon.svg');
            size: contain;
            position: center;
            repeat: no-repeat;
        };
    }*/

    @media (max-width:1500px) {
        height: 609px;
    }
    @media (max-width:1199px) {
        height: 355px;
    }
    @media (max-width:767px) {
        text-align: center;
        height: 400px;
    }
    @media (max-width:767px) {
        height: 350px;
    }
    &__title {
        text-transform: uppercase;
        text-align: right;
        font: 900 80px/114px $f-primary;
        color: $c-medium-grey;
        display: inline-block;
        padding-top: 110px;
        padding-left: 6vw;

        .is-doodled--splashed-revolution {
            &:before {
                top: 50%;
            }
        }
        .block {
            display: block;
            @media (max-width: 767px) {
                display: inline;
            }
        }
        .blackout {
            color: $c-grey;
            @media (max-width: 767px) {
                color: $c-white;
            }
        }
        @media (max-width:1750px) {
            font: 900 70px/104px $f-primary;
        }
        @media (max-width:1500px) {
            font: 900 50px/74px $f-primary;
        }
        @media (max-width:1350px) {
            font: 900 45px/60px $f-primary;
            padding-top: 120px;
        }
        @media (max-width:1199px) {
            font: 900 35px/50px $f-primary;
            padding-top: 47px;
        }
        @media (max-width:1024px) {
            text-align: right;
        }
        @media (max-width: 1199px) and (min-width: 992px) {
            padding-left: 15%;
        }
        @media (max-width: 767px) {
            font: 900 20px/28px $f-primary;
            color: $c-white;
            padding-left: 0;
            padding-right: 0;
            text-align: center;
            max-width: 330px;

            span {
                color: $c-white;

                &.is-doodled {
                    &--underlined {
                        &:after {
                            background-image: url('~@common/img/line.svg') !important;
                        }
                    }
                }
            }
        }
        @media (max-width: 767px) {
            padding-top: 28px;
        }
        &:lang(en) {
            font: 900 90px/104px $f-primary;
            @media (max-width: 1560px) {
                font: 900 50px/74px $f-primary;
            }
            @media (max-width:1199px) {
                font: 900 35px/50px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 20px/28px $f-primary;
            }
        }
    }

    &__slide-btn {
        &--mobile {
            display: none !important;
        }



        @media (max-width: 767px) {
            display: none !important;

            &--mobile {
                position: relative;
                left: auto;
                bottom: auto;
                margin: 0 auto;
                display: block !important;
            }
        }
    }

    &__slide {
        width: 100%;
        height: 709px;
        @media (max-width:1500px) {
            height: 609px;
        }
        @media (max-width:1199px) {
            height: 355px;
        }

        &--business-tv.HomeSlider__slide--business-tv.HomeSlider__slide--business-tv {
            padding: 0;
            background: $c-medium-grey;

            @media (min-width: 1351px) {
                display: flex;
                align-items: center;
            }
            @media (max-width: 767px) {
                padding-bottom: 40px;
            }

            .HomeSlider__title.HomeSlider__title.HomeSlider__title {
                line-height: 1.2;
                width: 45%;

                span.grey {
                    color: $c-light-grey;
                }

                @media (max-width: 600px) {
                    font: 900 32px/38px $f-primary, sans-serif;
                }
                @media (max-width: 499px) {
                    font: 900 20px/28px $f-primary, sans-serif;
                }
            }
        }

        &--slideOne {
            overflow: hidden;
            background: url('../../../img/<EMAIL>') right top -15px no-repeat;
            background-size: 1194px 528px;
            .HomeSlider__title {
                transform: translateY(-120%);
                transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55),
                            opacity 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                transition-delay: 0.4s;
            }
            .mobile-device {
                width: 468px;
                height: 306px;
                background: url('../../../img/slideshow/slide-1-moblie-layout.png') center center no-repeat;
                background-size: 100%;
                display: none!important;
                max-width: 100%;
                margin: 30px auto;
                &.device-en {
                    background: url('~@app/img/slideshow/slide-1-moblie-layout-en.png') center center no-repeat;
                    background-size: 100%;
                    @media (max-width: 767px) {
                        width: 209px;
                        height: 136px;
                        display: block!important;
                    }
                }
                @media (max-width: 835px) {
                    //
                }
                @media (max-width: 767px) {
                    width: 209px;
                    height: 136px;
                    display: block!important;
                }
            }
            .television {
                background: url('../../../img/slideshow/slide-1-television.png') center center no-repeat;
                background-size: 100%;
                width: 687px;
                height: 424px;
                position: absolute;
                right:155px;
                top: 104px;
                z-index: 1;
                transition: transform 0.5s $cubic, opacity 0.5s $cubic;
                transition-delay: 0.2s;
                transform: scale(2);
                transform-origin: center;
                &:lang(en) {
                    background: url('../../../img/slideshow/slide-1-television-en.png') center center no-repeat;
                    background-size: 100%;
                }
                @media (max-width: 1750px) {
                    width: 587px;
                    height: 362px;
                }
                @media (min-width: 1200px) and (max-width: 1500px) {
                    width: 450px;
                    height: 278px;
                    right: 220px;
                    top: 130px;
                }
                @media (max-width: 1199px) {
                    width: 300px;
                    height: 185px;
                    top: 47px;
                }
                @media (max-width: 835px) {
                    right: 75px;
                }
                @media (max-width: 767px) {
                    display: none;
                }
            }
            .ontariopromo {
                background: url('../../../img/ontario-promo.svg') center center no-repeat;
                width: 532px;
                height: 464px;
                position: absolute;
                right:295px;
                top: 104px;
                z-index: 1;
                transition: transform 0.5s $cubic, opacity 0.5s $cubic;
                transition-delay: 0.2s;
                transform: scale(2);
                transform-origin: center;
                @media (max-width: 1660px) {
                    right: 145px;
                }
                @media (max-width: 1500px) {
                    background-size: 90%;
                    width: 380px;
                    height: 331px;
                    right: 265px;
                }
                @media (max-width: 1199px) {
                    width: 250px;
                    height: 218px;
                    right: 215px;
                    top: 54px;
                }
                @media (max-width: 991px) {
                    width: 250px;
                    height: 218px;
                    right: 95px;
                    top: 54px;
                }
                @media (max-width: 767px) {
                    position: relative;
                    right: auto;
                    left: 50%;
                    top: auto;
                    margin-top: 20px;
                    margin-bottom: 20px;
                    width: 150px;
                    height: 131px;
                    transform: translateX(-50%)!important;
                }

            }
            .laptop {
                background: url('../../../img/slideshow/slide-1-laptop.png') center center no-repeat;
                width: 363px;
                height: 220px;
                position:absolute;
                background-size: 100%;
                top: 331px;
                right: 48px;
                z-index: 2;
                transform: translateX(120%);
                transition: transform 0.6s $cubic, opacity 0.6s $cubic;
                transition-delay: 0.4s;
                @media (max-width: 1750px) {
                    width: 313px;
                    height: 190px;
                }
                @media (min-width: 1200px) and (max-width: 1500px) {
                    width: 275px;
                    height: 167px;
                    right: 75px;
                    top: 280px;
                }
                @media (max-width: 1199px) {
                    width: 160px;
                    height: 97px;
                    top: 146px;
                    right: 100px;
                }
                @media (max-width: 835px) {
                    right: 10px;
                }
                @media (max-width: 767px) {
                    display: none;
                }
            }
            .mobile {
                background: url('../../../img/slideshow/slide-1-mobile.png') center center no-repeat;
                background-size: 100%;
                width: 203px;
                height: 249px;
                position:absolute;
                transform: translateX(-120px);
                top: 331px;
                right: 698px;
                opacity: 0;
                z-index: 2;
                transition: transform 0.6s $cubic, opacity 0.6s $cubic;
                transition-delay: 0.8s;
                &.mobile-en {
                    background: url('../../../img/slide1-ipad-iphone2x.png') center center no-repeat;
                    background-size: 100%;
                    @media (max-width: 1750px) {
                        width: 163px;
                        height: 200px;
                        right: 648px;
                    }
                    @media (min-width: 1200px) and (max-width: 1500px) {
                        width: 157px;
                        height: 193px;
                        right: 575px;
                        top: 280px;
                    }
                    @media (max-width: 1199px) {
                        width: 86px;
                        height: 106px;
                        top: 146px;
                        right: 390px;
                    }
                    @media (max-width: 835px) {
                       right: 310px;
                    }
                    @media (max-width: 767px) {
                        display: none;
                    }
                }
                @media (max-width: 1750px) {
                    width: 163px;
                    height: 200px;
                    right: 648px;
                }
                @media (min-width: 1200px) and (max-width: 1500px) {
                    width: 157px;
                    height: 193px;
                    right: 575px;
                    top: 280px;
                }
                @media (max-width: 1199px) {
                    width: 86px;
                    height: 106px;
                    top: 146px;
                    right: 390px;
                }
                @media (max-width: 835px) {
                   right: 310px;
                }
                @media (max-width: 767px) {
                    display: none;
                }
            }
            .decodeur {
                background: url('../../../img/slideshow/slide-1-decodeur.webp') center center no-repeat;
                background-size: 100%;
                width: 133px;
                height: 52px;
                position:absolute;
                transform: translateY(300%);
                opacity: 0;
                top: 540px;
                right: 430px;
                z-index: 2;
                transition: transform 0.4s $cubic, opacity 0.4s $cubic;
                @media (max-width: 1750px) {
                    top: 480px;
                    right: 382px;
                }
                @media (min-width: 1200px) and (max-width: 1500px) {
                    width: 100px;
                    height: 37px;
                    right: 396px;
                    top: 430px;
                }
                @media (max-width: 1199px) {
                    width: 59px;
                    height: 22px;
                    top: 240px;
                    right: 276px;
                }
                @media (max-width: 835px) {
                    right: 196px;
                }
                @media (max-width: 767px) {
                    display: none;
                }
            }
            .btn-container {
                position: relative;
                display: block;
                opacity: 0;
                transition-delay: 1s;

                @media (max-width:1500px) {
                    margin-top: 35px;
                }
                @media (max-width:1350px) {

                }
                @media (max-width:1199px) {

                }
                @media (max-width: 767px) {
                    margin: 0 auto;
                }
            }
            @media (max-width: 1750px) {
                background-position: top -120px right -100px;
            }
            @media (max-width: 1199px) {
                background-position: top -330px right -230px;
            }
            @media (max-width: 991px) {
                background-position: top -310px right -230px;
            }
            @media (max-width: 835px) {
                background-position: top -310px right -330px;
            }
            @media (max-width: 767px) {
                background: url('../../../img/<EMAIL>') center top -190px no-repeat;
                //background-size: 100%;
            }
            &--ontario {
                .doodled-arrow {
                    &:after {
                        right: -60px;
                        @media (max-width: 1199px) {
                            left: auto;
                            right: -32px;
                        }
                    }
                }
            }

        }
        // SLIDER RECOMMANDE AMI
        &--ami {
            background: url('~@app/img/amis-group.png') left bottom no-repeat;
            background-color: #BE2323;
            display: flex;
            //justify-content: center;
            box-shadow: inset 0 5px 30px 0 rgba(0,0,0,0.45);
            .HomeSlider__content-center {
                margin-left: 40%;
                @media (max-width:1500px) {
                    margin-left:30%;
                }
                @media (max-width: 767px) {
                    margin-left: 0;
                    width: 100%;
                }
            }
            @media (max-width:1500px) {
                background: url('~@app/img/amis-group.png') 70% left bottom no-repeat;
                background-size: 60%, auto;
                background-color: #BE2323;
            }
            .HomeSlider__slide-text-ami {
                font: 900 100px/100px $f-primary;
                color: $c-white;
                text-transform: uppercase;
                margin-bottom: 78px;
                position: relative;
                .is-ami {
                    position: relative;
                    &:after {
                        content: "";
                        width: 139px;
                        height: 193px;
                        background: url('~@app/img/slideshow/arrow-ami.svg') left top no-repeat;
                        background-size: 100%;
                        position: absolute;
                        right: -80px;
                        top: -11px;
                        @media (max-width: 1199px) {
                            width: 71%;
                            height: 120%;
                            right: -50px;
                            top: -6px;
                        }
                    }
                }

                @media (max-width: 1199px) {
                    font: 900 60px/60px $f-primary;
                    margin-bottom: 28px;
                }
            }
            .HomeSlider__slide-text-ami-rounded {
                color: $c-primary-darker;
                position: relative;
                &:after {
                    width: 183px;
                    height: 175px;
                    content: "";
                    background: url('~@app/img/slideshow/splash-1.svg') left top no-repeat;
                    background-size: 100%;
                    position: absolute;
                    left: -40px;
                    top: -15px;
                    animation: spin 5s infinite linear;
                    @media (max-width: 1199px) {
                        left: -20px;
                        top: 0px;
                        width: 190%;
                        height: 110%;
                    }
                }
            }

            .is-left-doodled {
                position: relative;
                &:before {
                    content: "";
                    width: 72px;
                    height: 155px;
                    background: url('~@app/img/left-doodled.svg') left top no-repeat;
                    background-size: 100%;
                    position: absolute;
                    right: -30px;
                    top: 76px;
                    animation: splash 0.8s infinite steps(3);
                    @media (max-width: 1199px) {
                        width: 180%;
                        height: 100%;
                        right: -16px;
                        top: 46px;
                    }
                }
            }
            .HomeSlider__slide-btn {
                @media (max-width: 767px) {
                    display: inline-block!important;
                }
            }

        }
        // SLIDER SUPER ZONE
        &--superzone {
            background: url('~@app/img/super-zone-maps.png') left top no-repeat, url('~@app/img/grey-pattern.png') left top repeat;
            display: flex;
            justify-content: center;
            box-shadow: inset 0 5px 30px 0 rgba(0,0,0,0.45);
            @media (max-width: 767px) {
                flex-direction: column;
                background-size: 100%, auto;
            }
            .HomeSlider__slide-icon-container {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            .HomeSlider__slide-icon-container {
                padding-right: 80px;
                img {
                    width: auto!important;
                    height: 470px;
                    transform: scale(0);
                    transition: all 0.8s cubic-bezier(0.55, 0, 0.1, 1);
                    @media (max-width: 1199px) {
                        height: 240px;
                    }
                    @media (max-width: 767px) {
                        height: 70px;
                        margin-bottom: 15px;
                    }
                }
                @media (max-width: 767px) {
                    padding-right: 0;
                }
            }
            .HomeSlider__slide-btn {
                transform: translateY(400px);
                transition: all 0.7s cubic-bezier(0.55, 0, 0.1, 1);
                @media (max-width: 767px) {
                    display: inline-block!important;
                }
            }
        }

        &-text-std {
            font: 900 45px/50px $f-primary;
            color: $c-white;
            text-transform: uppercase;
            transform: translateX(150%);
            transition: all 0.8s cubic-bezier(0.55, 0, 0.1, 1);
            @media (max-width: 1199px) {
                font: 900 35px/40px $f-primary;
            }
        }
        &-text-std-grey {
            font: 900 70px/80px $f-primary;
            color: $c-light-grey;
            text-transform: uppercase;
            max-width: 680px;
            @media (max-width: 1199px) {
                font: 900 50px/60px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 30px/40px $f-primary;
                max-width: 100%;
            }
        }
        &-text-std-white {
            font: 900 70px/80px $f-primary;
            color: $c-white;
            text-transform: uppercase;
            max-width: 680px;
            margin-bottom: 30px;
            @media (max-width: 1199px) {
                font: 900 50px/60px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 30px/40px $f-primary;
                max-width: 100%;
            }
        }
        &-text-curl {
            font: 400 45px/50px $f-secondary;
            color: $c-white;
            margin-bottom: 30px;
            transform: translateX(150%);
            transition: all 0.8s cubic-bezier(0.55, 0, 0.1, 1);
            transition-delay: 0.2s;
            opacity: 0;
            @media (max-width: 1199px) {
                font: 900 25px/35px $f-primary;
            }
        }
        &-text-disclaimer {
            font: 400 12px/16px $f-primary;
            color: $c-white;
            margin-top: 15px;
            color: $c-white;
        }
    }
    .owl-item {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
    .owl-dots {
        position: absolute;
        right: 40px;
        top: 50%;
        transform: translateY(-50%);
        @media (max-width: 835px) {
            right: 15px;
            top: 93px;
            transform: translateY(0);
        }
    }
    .owl-dot {
        display: block;
        margin-bottom: 8px;
        background: none;
        border: none;
        span {
            width: 15px;
            height: 15px;
            background: $c-light-grey;
            display: inline-block;
            @media (max-width: 835px) {
                width: 10px;
                height: 10px;
            }
        }
        &.active {
            span {
                background: #FFF;
            }
        }
        @media (max-width: 835px) {
            margin-bottom: 2px;
        }
    }

    &__content-center {
        display: flex;
        justify-content: center;
        flex-direction: column;
    }
}

.HomeSlider--affaires {
    @media (min-width: 768px) {
        margin-bottom: 10px;
    }
    // .HomeSlider__slide {
    //     @media (max-width: 767px) {
    //         height: auto;
    //     }
    // }
    .HomeSlider__slide--slideOne {
        padding: 150px 0;
        background: url('../../../img/affaire-bg-slider.jpg') right top -2px no-repeat;
        background-size: cover;
        @media (max-width: 767px) {
            padding-bottom: 40px;
        }
    }
    .HomeSlider__title {
        color: $c-white;
        font: 900 80px/79px $f-primary;
        padding-top: 0;
        @media (max-width:1750px) {
            font: 900 70px/104px $f-primary;
        }
        @media (max-width:1500px) {
            font: 900 50px/74px $f-primary;
        }
        @media (max-width:1350px) {
            font: 900 45px/60px $f-primary;
            padding-top: 120px;
        }
        @media (max-width:1199px) {
            font: 900 35px/50px $f-primary;
            padding-top: 47px;
        }
        @media (max-width:1024px) {
            text-align: right;
        }
        @media (max-width: 767px) {
            text-align: center;
            max-width: 100%;
        }
    }
    .HomeSlider__title-exp {
        font: 400 30px/30px $f-primary;
        color: $c-white;
        text-transform: none;
        margin-top: 30px;
        display: block;
    }
    .Button--slideOne {
        &:after {
            display: none;
        }
        @media (max-width: 767px) {
            margin-top: 20px;
        }
    }
    .is-doodled--underlined {
        &:after {
            top: 100%;
        }
    }
    // @media (max-width: 767px) {
    //     height: auto;
    //     padding-bottom: 40px;
    // }
}

.color-gray {
    color: $c-light-grey;
}

.owl-item {
    &.active > {
        .HomeSlider__slide--slideOne  {
            .HomeSlider__title {
                transform: translateY(0);
            }
            .television {
                transform: scale(1)!important;
            }
            .gamepad {
                opacity: 1;
                transform: translateX(0);
            }
            .ontariopromo {
                transform: scale(1);
            }
            .laptop {
                transform: translateX(0);
            }
            .mobile {
                opacity: 1;
                transform: translateX(0);
            }
            .decodeur {
                opacity: 1;
                transform: translateY(0);
            }
            .btn-container {
                opacity: 1;
            }
        }
        .HomeSlider__slide--ami {
            @media(max-width: 767px){
                height: 350px;
            }
            .HomeSlider__slide--pad {
                padding: 100px 30px;
            }
            .HomeSlider__slide-text-std {
                transform: translateY(0);
            }
            .HomeSlider__slide-text-curl {
                transform: translateY(0);
                opacity: 1;
            }
        }
        .HomeSlider__slide--superzone {
            .HomeSlider__slide-btn {
                transform: translateY(0);
                transition-delay: 0.2s;
            }
            .HomeSlider__slide-icon-container {
                img {
                    transform: scale(1);
                }
            }
        }
    }
}
.owl-item {
    &.active {
        .HomeSlider__slide--slideOne--ontario  {
            @media (max-width: 991px) {
                background-position: top -310px right -330px;
            }
            @media (max-width: 767px) {
                background-position: center top -190px;
            }
            .HomeSlider__title {
                transform: translateY(0);
            }

            .ontariopromo {
                transform: scale(1);
            }
            .btn-container {
                opacity: 1;
            }
        }
    }
}
.doodled-arrow {
    position: relative;
    &:before {
        content: "";
        position: absolute;
        width: 59px;
        height: 59px;
        background: url("../../../img/Icon/drawing-arrow05.svg") center center no-repeat;
        background-size: 100%;
        left: -65px;
        top: 90px;
        @media (max-width:1500px) {
            top: 52px
        }
        @media (max-width:1199px) {
            width: 30px;
            height: 30px;
            left: -30px;
            top: 40px;
        }
        @media (max-width: 767px) {
            top: 20px;
            background-image: url("../../../img/Icon/drawing-arrow05-white.svg");
            display: none;
        }
    }
    &:after {
        position: absolute;
        width: 59px;
        height: 59px;
        background: url("../../../img/Icon/drawing-arrow05.svg") center center no-repeat;
        transform: scale(1, -1) rotate(-160deg);
        background-size: 100%;
        left: auto;
        top: 40px;
        content: "";
        @media (max-width:1500px) {
            left: auto;
            top: 17px;
        }
        @media (max-width:1350px) {
            //left: 154px;
        }
        @media (max-width:1199px) {
            width: 30px;
            height: 30px;
            //left: 120px;
        }
        @media (max-width: 767px) {
            background-image: url("../../../img/Icon/drawing-arrow05-white.svg");
            //left: 68px;
            top: 6px;
            display: none;
        }
    }
}

.is-ami--en {
    &:after {
        @media (max-width: 767px) {
            width: 36%!important;
        }
    }
}

.icon-support {
    position: relative;
    &:after {
        content: "";
        width: 80px;
        height: 80px;
        background: url("../../../img/Icon/ic_support-grey.svg") left top no-repeat;
        background-size: 100%;
        position: absolute;
        right: -100px;
        top: 10px;
        transform: rotate(-20deg);
        animation: fadeIn 1s infinite alternate;
        @media (max-width: 399px) {
            display: none;
        }
    }
}

.HomeSlider__slide--slideOne--ontario {
    .HomeSlider__title {
        &:lang(en) {
            margin-top: 50px;
            font: 900 60px/80px $f-primary;
            @media (max-width: 1484px) {
                margin-top: 20px;
                font: 900 40px/60px $f-primary;
            }
            @media (max-width: 1199px) {
                margin-top: 0px;
                font: 900 30px/50px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 20px/40px $f-primary;
            }
        }
        .btn-container {
            &:lang(en) {
                @media (max-width: 1199px) {
                    margin-top: 20px;
                }
                @media (max-width: 767px) {
                    margin-top: 10px;
                }
            }
        }
    }

}


.HomeSlider__playPause {
    max-width: 1170px;
    margin: 0 auto;
    z-index:10;
    position:relative;
    bottom:62px;
    margin-left: 88px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    @media (max-width: 1300px) {
        bottom: 36px;
        margin-left: 184px;
    }
    @media (max-width: 991px) {
        bottom: 36px;
        margin-left: 132px;
    }
}

.page-template-page-accueil-affaires {
    .HomeSlider__playPause { 
        text-align: center;
        margin-left: 0;
        @media (max-width: 1300px) {
            bottom: 56px;
            margin-left: 0;
        }
        @media (max-width: 991px) {
            bottom: 56px;
            margin-left: 0;
        }
    }
}