@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-light-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-light-webfont.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-thin-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-thin-webfont.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-regular-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-regular-webfont.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-medium-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-medium-webfont.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-bold-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-bold-webfont.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

}
@font-face {
    font-family: 'BrandonGrotesque';
    src: url('../bases/fonts/brandongrotesque/brandongrotesque-black-webfont.woff2') format('woff2'),
         url('../bases/fonts/brandongrotesque/brandongrotesque-black-webfont.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* gloria-hallelujah-regular - latin */
@font-face {
    font-family: 'Gloria Hallelujah';
    font-style: normal;
    font-weight: 400;
    src: url('../bases/fonts/gloria-hallelujah/gloria-hallelujah-v8-latin-regular.eot');
    /* IE9 Compat Modes */
    src: local('Gloria Hallelujah'), local('GloriaHallelujah'), url('../bases/fonts/gloria-hallelujah/gloria-hallelujah-v8-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../bases/fonts/gloria-hallelujah/gloria-hallelujah-v8-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
    url('../bases/fonts/gloria-hallelujah/gloria-hallelujah-v8-latin-regular.woff') format('woff'), /* Modern Browsers */
    url('../bases/fonts/gloria-hallelujah/gloria-hallelujah-v8-latin-regular.ttf') format('truetype');
    /* Legacy iOS */
}