import { Qualification, QualifyService } from '@common/ts/services/Qualify.service';
import { Directive } from '@core/directive';

export class InternetProviderTabDirective extends Directive {
	public static selector: string = '[data-internet-provider-tab]';

	public attrs: {
		qualification: string;
	};

	//#region Private properties
	private qualificator: QualifyService = QualifyService.getInstance();

	private qualification: Qualification = this.qualificator.getQualification();
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLElement) {
		super(host, [
			{name: 'data-internet-provider-tab', as: 'qualification'}
		]);
	}

	onAppInit(): void {
		const [provider, province] = this.attrs.qualification.split(',');

		if (this.qualification && this.qualification.isValid) {
			const { internetType, fullProvince } = this.qualification.details;

			if (internetType === provider && fullProvince === province) {
				this.host.click();
			}
		}
	}
	//#endregion
}