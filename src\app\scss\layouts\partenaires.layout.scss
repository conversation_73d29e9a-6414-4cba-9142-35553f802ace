#loginForm {
    display: inline-flex;
    flex-direction: column;
    gap: 10px;  
    padding: 40px;
    border-radius: 20px;
    background: #FFF;
    min-width: 300px;
    label {
        font-size: 16px;
    }
    input[type="text"], input[type="password"] {
        border: 1px solid #000;
        border-radius: 5px;
        padding: 10px;
        width: 100%;
    }
    
}

.FormWrapperURL {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
    .FormWrapperURL__left,
    .FormWrapperURL__right {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
        display: inline-flex;
        flex-direction: column;
        gap: 10px;  
        padding: 40px;
        border-radius: 20px;
        background: #FFF;
        min-width: 300px;
        min-height: 260px;
        justify-content: center;
        label {
            font-size: 16px;
        }
        form {
            display: flex;
            flex-direction: column; 
            gap: 10px;
            text-align: center;
        }
    }
    
}

.PageTitle--partenaires {
    position: relative!important;
    overflow: hidden!important;
    .PageTitle__img {
        bottom: -720px!important;
        right: 0!important;
        @media (max-width: 1300px) {
            right:-200px!important;
        }
        @media (max-width: 767px) {
            right:-600px!important;
        }
        img {
            @media (max-width: 1300px) {
                height: auto;
            }
        }
    }
}

.page-template-page-partenaires {
    .FAQCard__body {
        height: inherit!important;
    }
}