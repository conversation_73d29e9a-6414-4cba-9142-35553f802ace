import { Directive } from '@core/directive';

export class IsStickyDirective extends Directive {
	static selector = '[data-is-sticky]';

	_isStickyValue = false;

	$wrapper = jQuery('<div style="display: block; position: relative;"/>');

	get dataIsSticky() {
		return this._isStickyValue;
	}
	set dataIsSticky(val) {
		if (val !== this._isStickyValue) {
			this._isStickyValue = val;

			this.update(val);
		}
	}

	constructor(host) {
		super(host, [
			{name: 'data-is-sticky', as: 'stickySide', default: 'top', accept: ['top', 'bottom']},
			{name: 'data-add-class-on-top', type: 'eval', default: false},
			{name: 'data-top-offset', type: 'int', default: window.innerHeight * 0.66667},
			{name: 'data-top-class-name', default: 'is-at-top'},
			'data-sticky-class-name'
		]);
		this._init();
	}

	update(val = this._isStickyValue) {
		if (val)
			this._enableSticky();
		else
            this._disableSticky();
	}

	_init() {
		this.wrapHost();
		this._bindEvents();
		this.update();
		this._onScroll();
	}

	wrapHost() {
		this.host.parentNode.insertBefore(this.$wrapper[0], this.host);
		this.$wrapper.append(this.$host);
	}

	_bindEvents() {
		jQuery(window).on('scroll', this._onScroll.bind(this));
	}

	_onScroll() {
		const { stickySide, dataAddClassOnTop, dataTopClassName, datatopOffset } = this.attrs;
		
		if (stickySide === 'top') {
			const wrapperPos = this.$wrapper.offset().top;
			const scrollPos = window.pageYOffset;

			if (scrollPos >= wrapperPos && !this.dataIsSticky)
				this.dataIsSticky = true;
			else if (scrollPos < wrapperPos && this.dataIsSticky)
				this.dataIsSticky = false;
		} else {
			const wrapperPos = this.$wrapper.offset().top + this.$wrapper.height();
			const scrollPos = window.pageYOffset + window.innerHeight;

			if (scrollPos < wrapperPos && !this.dataIsSticky)
				this.dataIsSticky = true;
			else if (scrollPos >= wrapperPos && this.dataIsSticky)
				this.dataIsSticky = false;
		}

		if (dataAddClassOnTop) {
			const hasClass = this.$host.hasClass(dataTopClassName);

			if (window.pageYOffset <= datatopOffset && !hasClass)
				this.$host.addClass(dataTopClassName);
			else if (window.pageYOffset > datatopOffset && hasClass)
				this.$host.removeClass(dataTopClassName);
		}
	}

	_enableSticky() {
		this.host.style.position = 'fixed';
		this.host.style[this.attrs.stickySide] = '0px';
		
		if (this.attrs.dataStickyClassName)
			this.$host.addClass(this.attrs.dataStickyClassName);
	}

	_disableSticky() {
		this.host.style.position = '';
		this.host.style[this.attrs.stickySide] = '';

		if (this.attrs.dataStickyClassName)
			this.$host.removeClass(this.attrs.dataStickyClassName);
	}
}