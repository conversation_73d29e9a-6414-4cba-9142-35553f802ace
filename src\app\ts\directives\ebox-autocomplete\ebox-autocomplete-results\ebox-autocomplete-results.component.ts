import { TweenLite } from 'gsap/TweenLite';
import { Power2, Back } from 'gsap/EasePack';

import { Component } from '@core/component';
import { EboxAutocompleteResult } from '../ebox-autocomplete.directive';
import { AutoCompleteComponent } from '@common/js/components/auto-complete/auto-complete.component';
import './ebox-autocomplete-results.component.scss';

export class EboxAutocompleteResultsComponent extends Component {
	public static selector: string = 'ebox-autocomplete-results';

	//#region Component settings
	public static template = require('./ebox-autocomplete-results.component.hbs');

	public state: EboxAutocompleteResultsState = {
		results: [],
		isLoading: true
	};
	//#endregion

	//#region Private properties
	private isOpenedVal: boolean = true;
	//#endregion

	//#region Public properties
	public get isOpened(): boolean {
		return this.isOpenedVal;
	}
	//#endregion

	//#region Lifecycles
	constructor(host: HTMLElement) {
		super(host);
		this.host.classList.add('ebox-autocomplete-results');
		this.bindEvents();
	}
	//#endregion

	//#region Public methods
	public show(noTransition: boolean = false): Promise<void> {
		return new Promise(resolve => {
			const { SHOW_DURATION } = AutoCompleteComponent;

			TweenLite.set(this.host, {clearProps: 'display'});
			TweenLite.to(this.host, (noTransition ? 0 : SHOW_DURATION), {
				y: 0,
				alpha: 1,
				ease: Back.easeOut,
				onComplete: resolve
			});

			this.isOpenedVal = true;
		});
	}

	public hide(noTransition: boolean = false): Promise<void> {
		return new Promise(resolve => {
			const { HIDE_DURATION, ANIMATION_TRAVEL_DISTANCE } = AutoCompleteComponent;

			TweenLite.to(this.host, (noTransition ? 0 : HIDE_DURATION), {
				y: ANIMATION_TRAVEL_DISTANCE,
				alpha: 0,
				ease: Power2.easeInt,
				onComplete: () => {
					TweenLite.set(this.host, {display: 'none'});
					resolve();
				}
			});

			this.isOpenedVal = false;
		});
	}
	//#endregion

	//#region Private methods
	private bindEvents(): void {
		this.$host.on('click', '.ebox-autocomplete-results__item', this.onItemClick.bind(this));
	}

	private onItemClick(evt: JQuery.Event): void {
		const clickedItem: HTMLElement = evt.target as HTMLElement;
	}
	//#endregion
}

export interface EboxAutocompleteResultsState {
	results?: EboxAutocompleteResult[];
	isLoading?: boolean;
}
