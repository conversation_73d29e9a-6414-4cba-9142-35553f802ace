.SliderItem {
    &__box {
        width: 220px;
        height: 146px;
        position: relative;
        top: 5px;
        left: 50%;
        transform: translateX(-50%);
        a {
            width: 100%;
            height: 146px;
            display: block;
            transition: $t-primary;
            &:hover {
                box-shadow: 0 2px 6px 0 rgba(#000, 0.5);
                border-radius: 12px;
            }
            @media (max-width: 991px) {
                height: 134px;
            }
            @media (max-width: 767px) {
                height: 107px;
            }
        }
        @media (max-width: 991px) {
            width: 186px;
            height: 134px;
        }
        @media (max-width: 767px) {
            width: 168px;
            height: 107px;
        }
    }
    img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 0 15px;
    }
}
