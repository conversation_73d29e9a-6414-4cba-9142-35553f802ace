.RedonneDetails {
	padding: 65px 0;
	background: #E4E4E4;

	&__block {
		&:not(:last-child) {
			margin-bottom: 53px;

			@media (max-width: 767px) {
				margin-bottom: 65px;
			}
		}

		p {
			position: relative;

			&:not(:last-of-type) {
				margin-bottom: 30px;
			}
		}

		&-title {
			position: relative;
			display: table;
			color: $c-primary-dark;
			font-size: 35px;
			line-height: 1.3;
			margin-bottom: 36px;
			padding-right: 64px;

			.doodled-underlined {
				position: relative;

				&:after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					display: block;
					width: 100%;
					height: 9px;
					background: {
						image: url('../../img/Signaler_ligne.svg');
						size: 100% 100%;
						position: center;
						repeat: no-repeat;
					}
				}
			}

			#donMoney {
				position: absolute;
				width: 50px;
				height: 57px;
				bottom: 10px;
				right: 0;
				margin-left: 15px;
				transform: rotate(10deg);
			}

			@media (max-width: 767px) {
				font-size: 27px;
				margin-bottom: 34px;
			}
			@media (min-width: 400px) {
				br {
					display: none;
				}
			}
			@media (max-width: 399px) {
				padding-right: 0;

				#donMoney {
					width: 38px;
					height: 45px;
					bottom: auto;
					right: auto;
					top: -23px;
					left: 103px;
					transform: rotate(10deg);

					@include applyAtRoot('html:lang(en)') {
						left: 185px;
					}
				}
			}
		}
	}
}



#signalSmile {
	position: relative;
	width: 29px;
	height: 29px;
	top: 10px;
	left: 5px;

	@media (max-width: 767px) {
		position: absolute;
		width: 29px;
		height: 29px;
		top: auto;
		left: auto;
		right: 0;
		bottom: -30px;
	}
}