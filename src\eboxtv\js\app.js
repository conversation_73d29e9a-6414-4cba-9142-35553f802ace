/// <reference path="./global.d.ts"/>


// JAVASCRIPT
/* Polyfills */
import 'regenerator-runtime/runtime';
import 'core-js/es6/set';
import 'core-js/es6/symbol';
import 'core-js/es6/promise';
import 'core-js/es7/object';
import 'core-js/modules/es6.array.from';
import templatePolyfill from 'template-polyfill';
import './polyfills';

/* GSAP */
import 'gsap/CSSPlugin';
import 'bootstrap/dist/js/bootstrap';

/* jQuery custom extensions */
import '@common/js/jQuery.isWithin';

/* Directives */
import { App } from '@core';
import {
	ChannelSelectDirective,
	TabControllerDirective,
	ToggleAllChannelSelectDirective,
	BindListDirective,
	OrderCounterDirective,
	CostCounterDirective,
	AddClassAtHeightDirective,
	StickySidebarDirective,
    SidePanelDirective,
    SummarySectionHideDirective,
    ResumeControllerDirective,
	RemoveClassOnClick,
	SectionToggleDirective,
	StickyResumeDirective,
	ValidateLoginFormDirective,
	WarnForMoreDirective,
	SearchFormDirective,
	EBTVToggleClassOnClickDirective,
	PersonalInfoDirective
} from './directives';

import { ToggleClassOnChangeDirective } from '@common/js/directives/modals/toggle-class-on-change.directive';
import { EnableOnValueDirective } from '@common/js/directives/modals/enable-onvalue.directive';
import { EnableOnSpecificValueDirective } from '@common/js/directives/modals/enable-onspecificvalue.directive';

import { SetCookieDirective } from '@common/js/directives/set-cookie.directive';
import { ValidationController } from '@common/js/directives/validation-controller.directive';
import { ClearableDirective } from '@common/js/directives/clearable/clearable.directive';

import { checkWPVars } from './runs/checkWPVars.run';
import { TotalOrderCounter } from './directives/total-order-counter.directive';
import { PriceBannerDirective } from './directives/price-banner.directive';
import { FinalOrderButtonDirective } from '../ts/directives/final-order-button.directive';
import { AccessifyDirective } from './directives/accessify/accessify.directive';
import { QualificationBannerDirective } from '@common/ts/directives/qualification-banner.directive';
import { ShowOnInitDirective } from '@common/ts/directives/show-on-init/show-on-init.directive';
import { HideOnInitDirective } from '@common/ts/directives/hide-on-init/hide-on-init.directive';
import { HideIfEmptyDirective } from '../ts/directives/hide-if-empty.directive.ts';
import { FilterSectionsDirective } from '../ts/directives/filter-sections.directive';
import { AutoscrollDirective } from '@common/ts/directives/autoscroll.directive';
import { ToggleClassOnClickDirective } from '@common/ts/directives/toggle-class-on-click.directive';
import { IsStickyDirective } from './directives/is-sticky.directive';
import { LocalisationDisplayDirective } from '@common/ts/directives/localisation-display.directive';
import { ShowOnLocalizedDirective } from '@common/ts/directives/show-on-localized/show-on-localized.directive';
import { HideOnLocalizedDirective } from '@common/ts/directives/hide-on-localized/hide-on-localized.directive';
import { MenuItemHasChildren } from '@app/ts/directives/menu-item-has-children.directive';
import { MenuCloserDirective } from '@app/ts/directives/menu-closer.directive';
import { ForfaitsListingComponent } from '@eboxtv/ts/components/forfaits-listing.component';
import { _BubbleDirective } from '@eboxtv/js/directives/_bubble/_bubble.directive';
import { ShowIfCookieDirective } from '@common/ts/directives/show-if-cookie/show-if-cookie.directive';
import { AddClassIfCookieDirective } from '@common/ts/directives/add-class-if-cookie.directive';
import { GformOnValidateDirective } from '@app/ts/directives/gform-on-validate.directive';
import { ShowOnPlainPackage } from '@eboxtv/ts/directives/show-on-plain-package/show-on-plain-package.directive';
import { SelectTVPackage } from '@eboxtv/ts/directives/select-tv-package.directive';
import { NumeriqueSelectTVPackage } from '@eboxtv/ts/directives/numerique-select-tv-package.directive';
import { PlainSectionToggleDirective } from '@common/ts/directives/plain-section-toggle.directive';
import { ForfaitTitleDirective } from '@eboxtv/ts/directives/forfait-title.directive';
import { OrderButtonDirective } from '@common/ts/directives/order-button.directive';
import { OptionSelectDirective } from '../ts/directives/option-select.directive';
import { WPRedirectDirective } from '@common/ts/directives/wp-redirections.directive';
import { EBOXPhoneNumberDirective } from '@common/ts/directives/ebox-phone-number.directive';
import { DecodeurDirective } from "@eboxtv/js/directives/decodeur.directive";
import { LocalizerDirective } from '@common/ts/directives/localizer.directive';
import { chatBubbleDirective } from './directives/chatBubble.directive';


import { CommandeTotalOrderCounter } from "@eboxtv/js/directives/commande-total-order-counter.directive"
import { CommandeOrderCounterDirective } from "@eboxtv/js/directives/commande-order-counter.directive";
import { CommandeSidePanelDirective } from "@eboxtv/js/directives/commande-side-pannel.directive";
import { CommandeCostCounterDirective } from "@eboxtv/js/directives/commande-cost-counter.directive";
import { CommandePriceBannerDirective } from "@eboxtv/js/directives/commande-price-banner.directive";
import { NumericPriceBannerDirective } from './directives/numeric-price-banner.directive';


window.onload = bootstrapOnLoad;

window.promotions = {
	internet : [],
	installation : [],
	livraison : [],
	modem : [],
	routeur : [],
	transfert : [],
	duo : []
};

new WPRedirectDirective(document.body);

const runs = [
	templatePolyfill,
	checkWPVars.bind(null, 'eboxtv_vars', [
		'modal_address',
		'modal_ville',
		'modal_address_notice',
		'modal_ville_notice',
		'modal_no_package'
	], ['ebtv-translations.php'])
];

// if (!isPage(4729) && !isPage(10834)) {
	runs.push(
		checkWPVars.bind(null, 'wpLocalizedVars', ['eboxtvChannels', 'eboxtvOptions'], ['ebtv-channels.php']),
		checkWPVars.bind(null, 'promotions', ['internet', 'installation', 'livraison', 'modem', 'routeur', 'transfert', 'duo'], ['localizePromo.php'])
	);
// }

OrderButtonDirective.ALL_DISABLED = false;

function bootstrapOnLoad() {
	console.time('Time took to bootstrap App');
	const myApp = new App({
		runs,
		directives: [
			GformOnValidateDirective,
			ChannelSelectDirective,
			TabControllerDirective,
			ToggleAllChannelSelectDirective,
			BindListDirective,
			OrderCounterDirective,
			TotalOrderCounter,
			CostCounterDirective,
			ToggleClassOnChangeDirective,
			AddClassAtHeightDirective,
			EBTVToggleClassOnClickDirective,
			StickySidebarDirective,
			EnableOnValueDirective,
			SidePanelDirective,
			EnableOnSpecificValueDirective,
			SummarySectionHideDirective,
			ResumeControllerDirective,
			RemoveClassOnClick,
			SectionToggleDirective,
			PlainSectionToggleDirective,
			StickyResumeDirective,
			AutoscrollDirective,
			ValidateLoginFormDirective,
			SetCookieDirective,
			WarnForMoreDirective,
			{
				use: ValidationController,
				isSingleton: true,
				selector: ValidationController.selector
			},
			ClearableDirective,
			SearchFormDirective,
			PriceBannerDirective,
			FinalOrderButtonDirective,
			AccessifyDirective,
			QualificationBannerDirective,
			ShowOnInitDirective,
			HideOnInitDirective,
			HideIfEmptyDirective,
			FilterSectionsDirective,
			IsStickyDirective,
			LocalizerDirective,
			LocalisationDisplayDirective,
			ShowOnLocalizedDirective,
			HideOnLocalizedDirective,
			ForfaitsListingComponent,
			_BubbleDirective,
			ShowIfCookieDirective,
			AddClassIfCookieDirective,
			ShowOnPlainPackage,
			SelectTVPackage,
			NumeriqueSelectTVPackage,
			ForfaitTitleDirective,
			OrderButtonDirective,
			OptionSelectDirective,
			EBOXPhoneNumberDirective,
			PersonalInfoDirective,
			CommandeTotalOrderCounter,
			CommandeOrderCounterDirective,
			CommandeSidePanelDirective,
			CommandeCostCounterDirective,
			CommandePriceBannerDirective,
			NumericPriceBannerDirective,
			DecodeurDirective,
			chatBubbleDirective
		]
	}).bootstrap(false);
	console.timeEnd('Time took to bootstrap App');

}

function isPage(id) {
	return document.body.className.indexOf('page-id-'+id) !== -1;
}