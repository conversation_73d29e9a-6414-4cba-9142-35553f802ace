// TWO COLUMNS - LEFT TEXT / RIGHT IMG
@import './TwoCol-LeftTextRightImg.scss';

// TWO COLUMNS - LEFT IMG / RIGHT TEXT
@import './TwoCol-LeftImgRightText.scss';

// TWO COLUMNS - LEFT IMG / RIGHT TEXT
@import './TwoCol-LeftImgRightTextGrey.scss';

// CARDS
@import './CardListing.scss';
@import './CardItem.scss';

// DIVIDER
@import './Divider.scss';

// BANDE ROUGE
@import './BandeRouge.component.scss';

// BANDE ROUGE CAROUSEL
@import './BandeRougeCarousel.component.scss';

// CAROUSSEL SLIDER LOGOS
@import './SliderWrapper.scss';
@import './SliderItem.scss';

// 3 CALLTOS
@import './ThreeCalltos.component.scss';

// 3 CALLTOS - AFFAIRES
@import './ThreeCalltos-Affaires.component.scss';

// BANDE FORMULAIRE
@import './BandeFormulaire.component.scss';
// THREE COLUMNS - CALLTOS
@import './ThreeCol-CalltosWrapper.scss';
@import './ThreeCol-CalltosItem.scss';

// TWO COLUMNS - CONTENT
@import './TwoCol.scss';

// ENTÊTE - SINGLE ARTICLE
@import './BandeEnteteSingleArticle.scss';

// CARRIERES
@import './CarrieresListing.component.scss';

// TOGGLE
@import './ToggleWrapper.scss';
@import './ToggleItem.scss';

// CONTACT
@import './CoordonneesMap.component.scss';
