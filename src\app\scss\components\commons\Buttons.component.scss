.But<PERSON> {
    background: $c-primary;
    color: $c-white;
    display: inline-block;
    border-radius: 6px;
    font: 700 16px/60px $f-primary;
    text-transform: uppercase;
    padding: 0 30px;
    transition: $t-primary;
    &:hover {
        text-decoration: none;
        background: $c-medium-grey;
        color: $c-white;
        box-shadow: 5px 5px 5px rgba(52, 52, 52, 0.5);
    }
    &:focus {
        background: $c-medium-grey;
        color: $c-white;
        outline: 2px solid blue;
    }
    @media (max-width: 1024px) {
        font: 700 12px/40px $f-primary;
        padding: 0 20px;
    }

    &--round {
        border-radius: 40px;
    }

    &--small {
        line-height: 48px;
        padding: 0 24px;
    }

    &--long {
        min-width: 200px;
        text-align: center;
    }

    &--slideOne, &--has-mouse {
        margin-left: 7%;
        position: relative;

        &:after {
            background: url('../../../img/Icon/ic_computer-mouse-red.svg') center center no-repeat;
            background-size: 100%;
            width: 59px;
            height: 59px;
            position: absolute;
            right: -52px;
            bottom: -51px;
            content: "";
            @media (max-width: 991px ) {
                width: 32px;
                height: 32px;
                right: -28px;
                bottom: -28px;
            }
            @media (max-width: 835px ) {
                width: 40px;
                height: 40px;
                right: -35px;
                bottom: -35px;
            }
        }

        &.Button--has-mouse-white {
            &:after {
                background-image: url('../../../img/Icon/ic_computer-mouse.svg');
            }
        }
    }

    &--callto {
        &:hover {
            background: $c-white;
            color: $c-primary;
        }
    }
    &--transparent {
        background: none;
        border: 3px solid #FFF;

    }
    &--qualification {
        background: none;
        font: 700 10px/30px $f-primary;
        border: 2px solid $c-white;
        padding: 0 20px;
        &:hover {
            border: 2px solid $c-medium-grey;
        }
    }
    &--mention {
        background: none;
        font: 700 12px/40px $f-primary;
        border: 2px solid $c-white;
        padding: 0 20px;
        margin-right: 15px;
        &:hover {
            border: 1px solid $c-medium-grey;
        }
        @media (max-width: 991px) {
            margin-right: 0;
        }
    }
    &--listing {
        font: 700 12px/40px $f-primary;
    }
    &--wysiwygGeneral {
        margin: 40px 0 0 0;
    }
    &--affaires {
        margin: 40px 0 0 0;
        &:focus {
            color: $c-white;
        }
    }
    &--white {
        background: $c-white;
        color: $c-primary;
    }
}



.ButtonEffect {
    border-radius: 6px;
    border: 0;
    height: 60px;
    min-width: 200px;
    width: auto;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: $c-white;
    font-weight: 700;
    padding: 0 20px;
    text-transform: uppercase;
    font-size: 16px !important;
    font-family: 'BrandonGrotesque', sans-serif;
    transition: all 0.2s $cubic, min-width 0s linear;
    will-change: transform;
    position: relative;
    overflow: hidden;
    z-index: 2;
    @media( max-width: 991px) {
        height: 40px;
        font-size: 12px!important;
        min-width: 120px;
    }
    @media (max-width: 767px) {
        padding: 0 20px;
        line-height: 18px;
    }
    &:before {
        width: 100%;
        height: 100%;
        content: '';
        position: absolute;
        display: block;
        z-index: -1;
        left: 0;
        top: 0;
        background: $c-primary;
        opacity: 1;
        transition: all 0.2s $cubic;
    }
    &:after {
        width: 100%;
        height: 100%;
        content: '';
        display: block;
        position: absolute;
        z-index: -1;
        left: 0;
        top: 0;
        transform: translateX(-100%);
        background: $c-grey;
        transition: all 0.2s $cubic;
    }
    &:hover, &:focus {
        background: $c-grey;
        color: $c-white;
        box-shadow: 5px 5px 5px rgba($c-grey, 0.5);
        &:after {
            transform: translateX(0);
        }
    }
    &:focus {
        outline: 2px solid blue;
    }
    &--mediumgrey {
        &:before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            background: $c-primary;;
            opacity: 1;
            transition: all 0.2s $cubic;
        }
        &:after {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            transform: translateX(-100%);
            background: $c-medium-grey;
            transition: all 0.2s $cubic;
        }

        &:hover, &:focus {
            background: $c-medium-grey!important;
            color: $c-white;
            box-shadow: 5px 5px 5px rgba($c-medium-grey, 0.5);
            &:after {
                transform: translateX(0);

            }
            &:before {
                opacity: 0;
            }
        }
    }
    &--transparent {
        background: none;
        color: $c-white;
        border: 3px solid $c-white;
        &:before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            background: none;
            opacity: 1;
            transition: all 0.2s $cubic;
        }
        &:after {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            transform: translateX(-100%);
            background: $c-white;
            transition: all 0.2s $cubic;
        }
        &:hover, &:focus {
            color: $c-primary;
            background: $c-white;
            &:after {
                transform: translateX(0);

            }
            &:before {
                opacity: 0;
            }
        }
    }
    &--white {
        background: $c-white;
        color: $c-primary;
        border: 0px solid rgba($c-white, 0);
        margin: 40px 0 0 0;
        @media( max-width: 991px) {
            height: 40px;
            font-size: 12px!important;
            min-width: 120px;
        }
        &:before {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            background: $c-white;
            opacity: 1;
            transition: all 0.2s $cubic;
        }
        &:after {
            width: 100%;
            height: 100%;
            content: "";
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            transform: translateX(-100%);
            background: $c-grey;
            transition: all 0.2s $cubic;
        }
        &:hover {
            color: $c-white;
            background: $c-grey;
            border-color: $c-grey;
            &:after {
                transform: translateX(0);

            }
            &:before {
                opacity: 0;
            }
        }
    }
    &--listing {
        font: 700 12px/40px $f-primary!important;
        min-width: initial;
        height: initial;
        white-space: nowrap;
        flex-shrink: 0;
    }
    &--mention {
        font: 700 12px/40px $f-primary!important;
        border: 2px solid $c-white;
        padding: 0 20px;
        margin-right: 15px;
        min-width: initial;
        height: initial;

        @media (max-width: 991px) {
            margin-right: 0;
        }
    }
    &--qualification {
        font: 700 10px/1.4 $f-primary!important;
        border: 2px solid $c-white;
        padding: 8px 20px;
        min-width: initial;
        height: initial;
        text-align: center;

        @media (max-width: 400px) {
            padding: 8px 10px;
        }
    }
    &--card {
        font: 700 12px/40px $f-primary!important;
        min-width: 176px;
        height: 40px;
        bottom: -32px;
        z-index: 1;
        @media (max-width: 767px) {
            font-size: 8px!important;
            line-height: 22px!important;
            min-width: 120px;
            height: 27px;
            bottom: -8px;
        }
    }
    &--calltos {
        font: 700 12px/40px $f-primary!important;
        min-width: 146px;
        height: 40px;
        margin: 30px 0 0 0;
    }
    &--border {
        background: none;
        border: 3px solid $c-primary;
        color: $c-primary;
        &:before {
            background: none;
        }
        &:hover, &:focus {
            border-color: $c-grey;
        }
    }
    &--routeurs {
        margin: 40px 0 0 0;
    }
}

.ButtonModems {
    color: $c-primary;
    display: inline-block;
    font: 700 16px/22px $f-primary;
    padding: 5px 0;
    transition: $t-primary;
    float: left;
    margin: 0 60px 0 0;
    svg {
        float: left;
        margin: -20px 15px 0 0;
        fill: $c-primary;
        width: 45px;
        height: 45px;
        transition: $t-primary;
    }
    &:hover {
        svg {
            fill: $c-grey;
        }
    }
}

.Wysiwyg .ButtonEffect:focus {
    outline: 2px solid #BE2323;
}

.ButtonRouteurs {
    color: $c-primary;
    display: inline-block;
    font: 700 16px/22px $f-primary;
    padding: 5px 0;
    transition: $t-primary;
    svg {
        float: left;
        margin: -17px 15px 0 0;
        fill: $c-primary;
        width: 45px;
        height: 45px;
        transition: $t-primary;
    }
    &:hover {
        svg {
            fill: $c-grey;
        }
    }
}

.TransparentButton {
    border: 2px #FFF solid;
    padding: 9px 10px 8px;
    border-radius: 7px;
    float: right;
    width: 100%;
    font: 700 12px/22px $f-primary;
    color: #FFF;
    text-decoration: none;
    max-width: 170px;
    text-transform: uppercase;
    text-align: center;
    transition: all 0.1s linear;

    &--small {
        border: 2px #FFF solid;
        border-radius: 5px;
        color: #FFF;
        font: 700 11px/20px $f-primary;
        text-decoration:none ;
        padding: 3px 12px;
        text-transform: uppercase;
        display: inline-block;
        width: auto;
        margin-left: 20px;
    }

    &--red {
        color: $c-primary;
        border-color: $c-primary;
        float: none;
        margin-left: 0;

        &:hover {
            background: $c-primary !important;
            color: #FFF !important;
        }
    
    }

    &:hover {
        background: #FFF;
        color: #303030;
    }

    &--grey-hover {
        &:hover {
            color: #FFF;
            background: #626262;
            border-color: #626262;
        }
    }

    &:focus {
        color: #FFF;

        &:hover {
            color: #303030;
        }
    }
}