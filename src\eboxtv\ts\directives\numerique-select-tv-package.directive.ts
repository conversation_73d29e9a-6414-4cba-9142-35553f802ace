import { SetCookieDirective } from '@common/js/directives/set-cookie.directive';
import { TVOrderService } from '@eboxtv/js/services';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { OrderOnlineService } from "@eboxtv/js/services/orderOnline.service";
import { CoreTools } from '@core/helpers';
import { PromotionsService } from '@eboxtv/js/services/promotions.service';
import { DecodeurDirective } from '@eboxtv/js/directives/decodeur.directive';
import { LocalStorage } from '@common/ts/services/LocalStorage';
export class NumeriqueSelectTVPackage extends SetCookieDirective {
	public static selector: string = '[data-numerique-select-tv-package]';

	private tvOrder: TVOrderService = TVOrderService.getInstance();
	private orderService = OrderOnlineService.getInstance();
	private promotionsService = PromotionsService.getInstance();

	private cardTVItem : any = document.querySelectorAll('.cardTVItem');

	private a_la_carte_option : any = {
		a_la_carte : false,
		nbChaines : 0,
		prmSku : '',
		prmPrix : 0
	}

	private hasTV: 'true' | 'false';
	private valid: boolean;

	public attrs: {
		packageID: any;
		redirectTo?: string;
		href?: string;
	};

	constructor(host: HTMLAnchorElement) {
		super(host, [
			{ name: 'data-numerique-select-tv-package', as: 'packageID', default: null },
			'redirect-to'
		]);

		this.initForm()
	}



	setCookie(): void {
		let { packageID } = this.attrs;

		if (packageID == 'no') {
			CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});

		} else {
			CookiesService.setCookies({
				name: 'hasTV',
				value: true
			});
		}

		if (packageID === null) {
			this.tvOrder.removeBundle();
			CookiesService.setCookies({
				name: 'hasFeaturedBundle',
				value: false
			});
		} else {
			this.tvOrder.addBundle(packageID);

			const currentBundle: any = this.tvOrder.getSelectedBundle();

			if (currentBundle) {
				CookiesService.setCookies({
					name: 'hasFeaturedBundle',
					value: currentBundle.isFeatured
				});
			}
		}

		// Cookie pour savoir que l'utilisateur à selectionné une option
		CookiesService.setCookies({
			name: 'isTvPackageSelected',
			value: true
		});

		const expirationDate = new Date();
		expirationDate.setDate(expirationDate.getDate() + 3);

		/*CookiesService.setCookies({
			name: 'CommandeStarted',
			value: 'oui',
			expires: expirationDate
		});*/





		this.orderService.initCart();
	}

	_onHostClick(evt: any) {
		evt.preventDefault();		
		if(evt.target != document.getElementById('noTVLink')){
			var element = <HTMLScriptElement>this.host;
			var toDelete = 0;
			if(!element.classList.contains('tv-items-cart--active')){
				this.toggleSelection(element);
			}else{
				this.deleteSelection();
				toDelete = 1;
			}

			if(toDelete){

				CookiesService.setCookies({
					name: 'hasTV',
					value: false
				});
				
				jQuery('.tv-items-cart--active').removeClass('tv-items-cart--active');

				LocalStorage.set('selectedBundles', []);
				LocalStorage.delete('forfaitOption');
				LocalStorage.delete('eboxTVOptions');
				LocalStorage.delete('selectedChannels');
				LocalStorage.delete('decodeur');
				LocalStorage.delete('DecodeurPromotion');
				this.orderService.saveTVOption({})
			}else{
				const a_la_carte = this.$host.attr('data-a-la-carte');

				this.a_la_carte_option.a_la_carte = this.$host.attr('data-a-la-carte') ? true : false;
				this.a_la_carte_option.nbChaines = this.$host.attr('data-nbChaines');
				this.a_la_carte_option.prmSku = this.$host.attr('data-prm-sku');
				this.a_la_carte_option.prmPrix = this.$host.attr('data-prm-prix').replace(',', '.');

				// Setting cookie
				this.setCookie();
				LocalStorage.set('forfaitOption', JSON.stringify(this.a_la_carte_option));



				// ------ A REMETTRE QUAND LES PROMOTIONS SONT A REMETTRE EN PLACE ------
				const decodeur = LocalStorage.get('decodeur');
				this.promotionsService.getInternetPromotion();
				this.promotionsService.getEquipementInternetPromotion();
				if(decodeur != null){
					this.promotionsService.promoDecodeur(decodeur.qty, decodeur.buyRent);
				}
		
				
			}

			this.orderService.initCart();
			// If the host has a href, we redirect the user to that href.
			//this.redirect();
		}
		else{
			evt.preventDefault();
			CookiesService.setCookies({
				name: 'hasTV',
				value: false
			});
			const decodeur = LocalStorage.get('decodeur');
			this.promotionsService.getInternetPromotion();
			this.promotionsService.getEquipementInternetPromotion();
			if(decodeur != null){
				this.promotionsService.promoDecodeur(decodeur.qty, decodeur.buyRent);
			}
		}
	}

	initForm(){
		const bundle = this.orderService.retTVOption();
		let { packageID } = this.attrs;

		if(packageID == bundle[0]){
			this.$host.addClass('tv-items-cart--active');
		}

	}

	redirect() {
		let href;

		if (this.attrs.redirectTo) {
			href = this.attrs.redirectTo;
		} else if (this.attrs.href) {
			href = this.attrs.href;
		}

		if (href !== undefined) {
			location.href = href;
		}
	}

	toggleSelection(target : any){
		this.cardTVItem.forEach((el : any) => {
            el.classList.remove('tv-items-cart--active');
        });
        target.classList.add('tv-items-cart--active');
	}

	deleteSelection(){
		this.cardTVItem.forEach((el : any) => {
            el.classList.remove('tv-items-cart--active');
        });
	}
}