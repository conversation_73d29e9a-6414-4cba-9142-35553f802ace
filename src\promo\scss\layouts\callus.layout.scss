.callus {
	text-align: center;

	&__title {
		@include bigSectionTitle;
		font-size: 40px;
		.or {
			color: $c-primary-dark;
		}

		@media (max-width: 500px) {
			font-size: 28px;
			line-height: 35px;
			margin-bottom: 0;
		}
	}

	&__link {
		position: relative;
		display: inline-block;
		font: 900 60px/58px $f-primary;
		color: $c-primary-dark;

		@media (max-width: 500px) {
			font-size: 37px;
		}

		&:hover, &:focus {
			text-decoration: none;
			color: $c-primary;
		}

		&:hover, &--highlight {
			.callus__splash-icon {
				animation: splash 0.6s steps(3) infinite;
			}
		}

		// &--highlight {
		// 	animation: flash 1s steps(1) infinite;
		// }
	}

	&__splash-icon {
		position: absolute;
		display: inline-block;
		top: 50%;
		margin-top: -24px;
		fill: $c-primary;
		width: 48px;
		height: 48px;

		&--left {
			right: 100%;
		}

		&--right {
			left: 100%;
			margin-left: -10px;
		}

		@media (max-width: 500px) {
			width: 30px;
			height: 30px;
			margin-top: -15px;

			&--left {
				right: 100%;
				margin-right: 5px;
			}

			&--right {
				margin-left: 0;
			}
		}
	}
}