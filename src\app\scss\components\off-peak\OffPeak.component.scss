.OffPeak {
    padding: 100px 0;
    position: relative;
    display: inline-block;
    width: 100%;
    @media (max-width: 991px) {
        padding: 140px 0 70px;
    }
    ul {
        margin: 0;
        li:first-of-type {
            .OffPeak__forfaitwrapper {
                @media (max-width: 767px) {
                    margin-bottom: 140px;
                }
            }
        }
    }
    &__title {
        text-transform: uppercase;
        font: 900 45px/50px $f-primary;
        margin-bottom: 30px;
        @media (max-width: 991px) {
            font: 900 26px/32px $f-primary;
        }
        @media (max-width: 767px) {
            font: 900 22px/24px $f-primary;
            margin-bottom: 40px;
        }

    }
    &__notice {
        font: 400 12px/16px $f-primary;
        margin-bottom: 100px;
        @media (max-width: 991px) {
            margin-bottom: 70px;
        }
        @media (max-width: 767px) {
            margin-bottom: 40px;
        }
    }

    &__listing {
        padding-top: 100px;
        padding-bottom: 100px;
        @media (max-width: 991px)  {
            padding-top: 70px;
            padding-bottom: 70px;
        }
        @media (max-width: 767px)  {
            padding-top: 40px;
            padding-bottom: 40px;
        }
    }
    &__forfaitwrapper {
        min-height: 548px;
        box-shadow: 0 2px 4px 0 rgba(#000, 0.5);
        width: 100%;
        border-radius: 5px;
        background: $c-white;
        .ButtonEffect {
            position: absolute;
            left: 50%;
            bottom: -30px;
            transform: translateX(-50%);
            @media (max-width: 991px) {
                bottom: -20px;
            }
        }
        @media (max-width: 767px) {
            min-height: 408px;
        }
    }
    &__top {
        height: 203px;
        background: url('../../../img/grey-pattern.png') left top repeat;
        width: 100%;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        p {
            color: $c-white;
            text-transform: uppercase;
            margin-top: 30px;
            margin-bottom: 0;
            font: 900 45px/40px $f-primary;
            position: relative;
            @media (max-width: 1199px) {
                font: 900 40px/40px $f-primary;
            }
            @media (max-width: 991px) {
                font: 900 33px/30px $f-primary;
            }
            @media (max-width: 767px) {
                font: 900 30px/35px $f-primary;
                padding: 0 15px;

            }
            .is-doodled--underlined {
                &:after {
                    top: 100%;
                    height: 12%;
                    margin-top: 6px;
                }
            }
            .arrowed {
                position: absolute;
                right: 0;
                bottom: -36px;
                text-transform: none;
                font: 400 12px/16px $f-secondary;
                &:before {
                    width: 20px;
                    height: 20px;
                    background: url("../../../img/Icon/drawing-arrow04-white.svg") center center no-repeat;
                    background-size: 100%;
                    content: "";
                    position: absolute;
                    display: inline-block;
                    left:-24px;
                    top: -6px;
                    transform: rotate(-15deg);
                }
            }
        }
    }
    &__bottom {
        padding: 30px;
    }

    .RedCircle {
        width: 130px;
        height: 130px;
        background: url('../../../img/back-red-circle.png') center center no-repeat;
        background-size: 130px 130px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: -65px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        &__price  {
            position: relative;
            color: $c-white;
            margin-top: 5px;
            padding-top: 5px;
            border: none!important;
            font: 900 45px/36px $f-primary!important;
            width: 62px;
            margin: 0!important;
            sup {
                position: relative;
                top: -13px;
                font: 900 17px $f-primary!important;
            }
        }
        &__mois {
            position: absolute;
            font: 400 8px/12px $f-secondary;
            left: 2px;
            bottom: -8px;
            width: 40px;
    
        }
        &__type {
            color: $c-white;
            font: 700 28px $f-primary!important;
            margin: 0!important;
        }
    }
}

.Button {
    &__offpeak {
        margin: 40px 15px 100px;
        @media (max-width: 991px) {
            margin: 40px 15px 100px;
        }
        @media (max-width: 767px) {
            margin: 20px 15px 70px;
        }
    }
}