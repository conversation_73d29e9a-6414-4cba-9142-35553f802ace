export function checkWPVars(globalContainerName, wpRequiredVarNames = [], phpFileNames = []) {
	const missing = [];

	if (!(globalContainerName in window))
		throw new Error(`Cannot find wordpress provided variable ${ globalContainerName }. See ${ phpFileNames.join(', ') } for more details.`);

	for (let i = 0; i < wpRequiredVarNames.length; i++) {
		const wpVarName = wpRequiredVarNames[i];

		if (!(wpVarName in window[globalContainerName]))
			missing.push(wpVarName);
	}

	if (missing.length)
		throw new Error(`Global variable${ missing.length > 1 ? 's' : '' } [${ missing.join(', ') }] ${ missing.length > 1 ? 'are' : 'is' } mandatory in this project and must be provided by wordpress PHP script. Please check file ${ phpFileNames.join(', ') }.`);
}