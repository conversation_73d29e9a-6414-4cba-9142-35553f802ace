<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="74px" viewBox="0 0 300 74" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 49 (51002) - http://www.bohemiancoding.com/sketch -->
    <title>path-1</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <polygon id="path-1" points="0 0 300 0 300 59.8550725 150 70 0 59.8550725"></polygon>
        <filter x="-1.3%" y="-2.9%" width="102.7%" height="111.4%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="triangle-bottom-bg">
            <g id="path-1-link" fill="#000000" fill-rule="nonzero">
                <polygon id="path-1" points="0 0 300 0 300 59.8550725 150 70 0 59.8550725"></polygon>
            </g>
            <g id="path-1-link">
                <g id="path-1">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#1F1F1F" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
            </g>
        </g>
    </g>
</svg>