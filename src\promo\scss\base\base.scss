.wrapper {
	@include wrapper;
}


[data-owl-carousel] {
	position: relative;
	display: block;
	width: 100%;
	padding-bottom: 70px;

	.owl-item {
		display: inline-block;
		text-align: center;
		vertical-align: middle;
	}

	.owl-nav.disabled {
		display: none;
	}

	.owl-dots {
		position: absolute;
		display: flex;
		justify-content: space-around;
		width: 100%;
		padding: 0 30px;
		bottom: 0;
		left: 50%;
		text-align: center;
		transform: translateX(-50%);

		.owl-dot {
			display: inline-block;
			width: 16px;
			height: 16px;
			border: 8px solid $c-primary-dark;
			border-radius: 100%;
			cursor: pointer;
			transition: all 0.25s cubic-bezier(.55, 0, .1, 1);

			&.active {
				border-width: 3px;
			}
		}
	}
}

.mobile-only {
	@media (min-width: 768px) {
		display: none !important;
	}
}

.desktop-only {
	@media (max-width: 767px) {
		display: none !important;
	}
}