import { QualifyService, Qualification } from '@common/ts/services/Qualify.service';

export function popUpListing() {
	setTimeout(() => {
		const qualifier: QualifyService = QualifyService.getInstance();
		const qualification: Qualification = qualifier.getQualification();
		// const { internetType } = qualification && qualification.details;
		// const $popUpForfait: JQuery = jQuery(`#popup-listingforfaits-${ internetType }`);

		// if ($popUpForfait.length && window.sessionStorage.getItem('hasOfferedListingPromo') !== `popup-listingforfaits-${ internetType }`) {
		// 	$popUpForfait.on('show.bs.modal', () => {
		// 		window.sessionStorage.setItem('hasOfferedListingPromo', `popup-listingforfaits-${ internetType }`);
		// 		$popUpForfait.off('show.bs.modal');
		// 	}).modal('show');

		// 	$popUpForfait.find('.modal-close').on('click', () => {
		// 		$popUpForfait.modal('hide');
		// 	});
		// }
	}, 500);
}