@mixin containerBottomPadding() {
	padding-bottom: 145px;
	
	@media (max-width: map-get($ebtvBreakpoints, smallDesktop)) {
		padding-bottom: 145px;
	}
	@media (max-width: map-get($ebtvBreakpoints, laptop)) {
		padding-bottom: 145px;
	}
	@media (max-width: map-get($ebtvBreakpoints, tabletPortrait)) {
		padding-bottom: 110px;
	}
	@media (max-width: map-get($ebtvBreakpoints, mobileLandscape)) {
		padding-bottom: 70px;
	}
}