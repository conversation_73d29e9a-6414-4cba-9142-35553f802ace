import { LocalStorage } from '@common/ts/services/LocalStorage.ts';
import { Tools } from '@common/ts/services/Tools.service.ts';
import { CookiesService } from '@common/ts/services/Cookies.service';
import { ChannelsService } from './channels.service';
import { CoreTools } from '@core/helpers';

export class PromotionsService {

    //#region Singleton creation and distribution
	/**
	 * @private
	 */
	static _instance;
	/**
	 * @public
	 * @return {PromotionsService}
	*/
	static getInstance() {
		if (PromotionsService._instance === undefined)
			PromotionsService._instance = new PromotionsService();

		return PromotionsService._instance;
	}

    currentInternetPromotion = LocalStorage.get('InternetPromotion') !== null ? LocalStorage.get('InternetPromotion') : [];
    currentEquipementInternetPromotion = LocalStorage.get('EquipementInternetPromotion') !== null ? LocalStorage.get('InternetPromotion') : [];
    currentDuoPromotion = LocalStorage.get('DuoPromotion') !== null ? LocalStorage.get('DuoPromotion') : [];
    currentInfoPromotion = LocalStorage.get('InfoPromotion') !== null ? LocalStorage.get('InfoPromotion') : [];

    newInternetPromotion = [];
    newEquipementInternetPromotion = [];
    newDuoPromotion = [];
    newInfoPromotion = [];

    _promoInternet = window.promotions ? window.promotions.internet : [];
    _promoInstallation = window.promotions ? window.promotions.installation : [];
    _promoLivraison = window.promotions ? window.promotions.livraison : [];
    _promoModem = window.promotions ? window.promotions.modem : [];
    _promoRouteur = window.promotions ? window.promotions.routeur : [];
    _promoTransfert = window.promotions ? window.promotions.transfert : [];
    _promoDuo = window.promotions ? window.promotions.duo : [];
	
	//#endregion

    hasTV = null;

    // INTERNET
    getInternetPromotion(){
        this.hasTV = CookiesService.getCookies('hasTV', true);
        const internetPlanLocal = LocalStorage.get('internetPlan');
        this.newInternetPromotion = [];
            var internetLocal = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : '';
            this._promoInternet.forEach((promo)=>{
                if(internetLocal.forfait == promo.type_internet){
                    if(!this.hasTV){
                        if(promo.promoCodeADSL && promo.prixADSL && internetPlanLocal.prixADSL){
                            promo.prix = promo.prixADSL;
                            promo.promoCode = promo.promoCodeADSL;
                        }
                        this._addInternetPromotion(promo);
                    }
                    else if(promo.promoCode == "SIT00756" && promo.promoCode == "SIT00757" && promo.promoCode == "SIT00760"){
                        this._addInternetPromotion(promo);
                    }
                }
            });

        this.saveInternetPromotion();

    }
    
    _addInternetPromotion(promo){
        this.newInternetPromotion.push(promo);
    }

    saveInternetPromotion(){
        LocalStorage.set('InternetPromotion', JSON.stringify(this.newInternetPromotion));
    }

    // Livraison, transfert et installation
    getInfoPromotion(shippingType, haveInternet, oldTech, actualProvider){

        
        const internetPlanLocal = LocalStorage.get('internetPlan');
        this.newInfoPromotion = [];

        if(internetPlanLocal.downloadSpeed == '6'){
            var fraisActivation = {
                prix: '20',
                titre_panier: CoreTools.translate('Crédit - Frais d\'activation de la ligne', 'Credit - Line Activation Fee'),
                promoCode: 'SIT00786',
                recurrent: 'non',
                type: 'livraison',
                post_title: CoreTools.translate('Crédit - Frais d\'activation de la ligne', 'Credit - Line Activation Fee'),
            }
            this.newInfoPromotion.push(fraisActivation);
        }
        
        if(internetPlanLocal.cableTech !== 'DSL' && internetPlanLocal.technology !== 'DSL'){
            if(haveInternet != undefined){

                // if(internetPlanLocal.forfait != 'EBX00387'){
                    if(haveInternet == 'true' && oldTech == 'cable_or_fiber' && actualProvider != undefined && actualProvider != 'defaultValue'){
                        this._promoInstallation.forEach((promo)=>{
                            if(promo.promoCode != 'SIT00775' && promo.promoCode != 'SIT00722' && promo.promoCode != 'SIT00303'){
                                this.newInfoPromotion.push(promo);
                            }
                        });
                    }else{
                        this._promoInstallation.forEach((promo)=>{
                            if(promo.promoCode != 'SIT00775' && promo.promoCode != 'SIT00722' && promo.promoCode != 'SIT00303'){
                                this.newInfoPromotion.push(promo);
                            }
                        });
                    }
                // }
            }
        }
        else if(internetPlanLocal.downloadSpeed == '25' || internetPlanLocal.downloadSpeed == '50' || internetPlanLocal.downloadSpeed == '30' || internetPlanLocal.downloadSpeed == '60'){

            if(haveInternet == 'true' && oldTech == 'cable_or_fiber' && actualProvider != undefined && actualProvider != 'defaultValue'){
                this._promoInstallation.forEach((promo)=>{
                    if(promo.promoCode == 'SIT00775'){
                        this.newInfoPromotion.push(promo);
                    }
                });
            }else{
                this._promoInstallation.forEach((promo)=>{
                    if(promo.promoCode == 'SIT00775'){
                        this.newInfoPromotion.push(promo);
                    }
                });
            }
        }
        else if(internetPlanLocal.downloadSpeed == '6'){
            if(haveInternet == 'true' && oldTech == 'cable_or_fiber' && actualProvider != undefined && actualProvider != 'defaultValue'){
                this._promoInstallation.forEach((promo)=>{
                    if(promo.promoCode == 'SIT00722'){
                        this.newInfoPromotion.push(promo);
                    }
                });
            }else{
                this._promoInstallation.forEach((promo)=>{
                    if(promo.promoCode == 'SIT00722'){
                        this.newInfoPromotion.push(promo);
                    }
                });
            }
        }

        if(shippingType == 'shipping'){
            if(internetPlanLocal.cableTech !== 'DSL' && internetPlanLocal.technology !== 'DSL'){
                var found = 0;
                this._promoLivraison.forEach((promo)=>{
                    if(found == 0 && promo.promoCode == 'SIT00755'){
                        this.newInfoPromotion.push(promo);
                        found = 1;
                    }
                });
            }
            else if(internetPlanLocal.downloadSpeed == '25' || internetPlanLocal.downloadSpeed == '50'){
                var found = 0;
                this._promoLivraison.forEach((promo)=>{
                    if(found == 0 && promo.promoCode == 'SIT00755'){
                        this.newInfoPromotion.push(promo);
                        found = 1;
                    }
                });
            }
            else if(internetPlanLocal.downloadSpeed == '6'){
                var found = 0;
                this._promoLivraison.forEach((promo)=>{
                    if(found == 0 && promo.promoCode == 'SIT00755'){
                        this.newInfoPromotion.push(promo);
                        found = 1;
                    }
                });
            }else{
                this._promoLivraison.forEach((promo)=>{
                    if(promo.promoCode == 'SIT00755'){
                        this.newInfoPromotion.push(promo);
                    }
                });
            }

        }

        LocalStorage.set('InfoPromotion', JSON.stringify(this.newInfoPromotion));
        

    }

    // DUO
    getDuoPromotion(){

        var internetLocal = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : '';
        var bundleLocal = LocalStorage.get('selectedBundles') !== null ? LocalStorage.get('selectedBundles') : '';
        const chaines = LocalStorage.get('selectedChannels');

        this.newDuoPromotion = [];

        if(internetLocal.forfait && bundleLocal[0]){
            
            var is15Channel = false;
            var channelLength = 0;
            for (const [key, value] of Object.entries(chaines)) {
                if(key == 'a-la-carte') {
                    channelLength = value.length;
                    if(channelLength && channelLength >= 15){
                        is15Channel = true;
                    }
                }
            }

            var eligibleBundles = ["12021", "12024"];
            if(eligibleBundles.includes(bundleLocal[0]) || is15Channel){
                var promoCode = "";
                if(internetLocal.cableTech == 'FTTH'){
                    promoCode = 'PRM00308'
                }else{
                    promoCode = 'PRM00307'
                }

                var promoDuo = {
                    bundle_id: bundleLocal[0],
                    prix: "5.00",
                    promoCode: promoCode,   
                    titre_panier: CoreTools.translate('Promotion Duo', 'Duo Promotion'),
                    recurrent: 'oui',
                    type_internet: internetLocal.forfait
                }
                this._addDuoPromotion(promoDuo);

            }else{
                this._addDuoPromotion("");
            }
        
        }
        /*this._promoDuo.forEach((promo)=>{
            
            if(bundleLocal[0] == promo.bundle_id && internetLocal.forfait == promo.type_internet){
                this._addDuoPromotion(promo);
            }
        
        });*/

        this.saveDuoPromotion();

    }

    _addDuoPromotion(promo){
        this.newDuoPromotion.push(promo);
    }

    saveDuoPromotion(){
        
        if(this.newDuoPromotion.length > 0 && this.newDuoPromotion[0] != ""){
            LocalStorage.set('DuoPromotion', JSON.stringify(this.newDuoPromotion));
        }else{
            LocalStorage.set('DuoPromotion', "[]");
        }
    }

    // Equipements Internet ( modem + routeur )
    getEquipementInternetPromotion(){
        const bundleCheck = [106507, 12360, 12359, 11624, 11758, 12021, 12024]
        const repBundleCheck = this.checkBundleArray(bundleCheck)
        this.newEquipementInternetPromotion = [];
        if(!repBundleCheck){
            var qualification = CookiesService.getCookies('eboxQualification', true);
            if(qualification){

                var equipementInternetLocal = LocalStorage.get('internetEquipement') !== null ? LocalStorage.get('internetEquipement') : '';

                var internetLocal = LocalStorage.get('internetPlan') !== null ? LocalStorage.get('internetPlan') : '';
                var phoneLocal = LocalStorage.get('phonePlan') !== null ? LocalStorage.get('phonePlan') : '';
                if(internetLocal && internetLocal.technology != 'v-cable'){

                    var fournisseur = internetLocal.technology;
                    var downloadSpeed = internetLocal.downloadSpeed;
                    var province = qualification.details.fullProvince;
                    if(equipementInternetLocal.length > 0){
                        equipementInternetLocal.forEach( (equipement) => {
    
                            // MODEM
                            if(equipement.type == 'modem'){
                
                                // MODEM
                                this._promoModem.forEach( (promo) => {
                                    if(internetLocal.cableTech == 'DSL' || internetLocal.technology == 'DSL'){
                                        if(promo.recurrent == 'oui' && equipement.buyRent == 'rent'){
                                            if(phoneLocal && phoneLocal.type != 'none'){
                                                if(promo.recurrent == 'oui' && equipement.buyRent == 'rent'){
                                                    if(promo.promoCode == 'SIT00763'){
                                                        this._addEquipementInternetPromotion(promo);
                                                    }
                                                }

                                            }
                                            else{
                                                if(promo.promoCode == 'SIT00744'){
                                                    this._addEquipementInternetPromotion(promo);
                                                }
                                            }
                                        }
                                        else if(promo.recurrent == 'non' && equipement.buyRent == 'buy'){
                                            if(promo.promoCode != 'SIT00744' && promo.promoCode != 'SIT00763'){
                                                this._addEquipementInternetPromotion(promo);

                                            }
                                        }


                                    }
                                    else{
                                        if(promo.promoCode != 'SIT00744' && promo.promoCode != 'SIT00763'){
                                            if(promo.recurrent == 'oui' && equipement.buyRent == 'rent'){
                
                                                if( (fournisseur == 'c-cable' && province == 'quebec' && downloadSpeed >= 120) || (fournisseur == 'c-cable' && province == 'ontario' && downloadSpeed == 1000) ){
                                                    promo.prix = "5.95";
                                                    promo.promoCode = 'PRM00164'
                                                }else{
                                                    promo.prix = "5.95";
                                                    if(fournisseur == 'c-cable'){
                                                        promo.promoCode = 'PRM00164'
                                                    }
                                                }
                        
                                                this._addEquipementInternetPromotion(promo);
                                            }else if(promo.recurrent == 'non' && equipement.buyRent == 'buy'){
                                                this._addEquipementInternetPromotion(promo);
                                            }
                                        }
                                    }

                                });
                
                            }else if(equipement.type == 'router'){
                
                                // ROUTEUR


                                this._promoRouteur.forEach( (promo) => {
                                    if(promo.recurrent == 'oui' && equipement.buyRent == 'rent'){
                                        this._addEquipementInternetPromotion(promo);
                                    }else if(promo.recurrent == 'non' && equipement.buyRent == 'buy'){
                                        this._addEquipementInternetPromotion(promo);
                                    }
                                });
                
                            }
        
                        });
                    }

                }
            }

        }


        this.saveEquipementInternetPromotion();

    }

    _addEquipementInternetPromotion(promo){
        this.newEquipementInternetPromotion.push(promo);
    }

    //
    saveEquipementInternetPromotion(){
        LocalStorage.set('EquipementInternetPromotion', JSON.stringify(this.newEquipementInternetPromotion));
    }


    checkBundleArray(array){
        this.hasTV = CookiesService.getCookies('hasTV', true);
        let bundleID =  LocalStorage.get('selectedBundles');
        if(this.hasTV){
            return true;
            // if(bundleID == null){
            //     return false
            // }
            // else if( bundleID.length == 0){
            //     return true
            // }
            // else{
            //     bundleID = parseInt(bundleID[0]);
            //         if(array.includes(bundleID)){
            //             return true
            //         }
            //         else{
            //             return false
            //         }
            // }
        }
        else{
            return false;
        }
    }

    promoDecodeur(qty, buyrent){
        const bundleidCheck = [12020, 5273, 8075,11762];
        const arrayCheck = this.checkBundleArrayDecodeur(bundleidCheck);
        if(!arrayCheck){
            if(qty >= 1 && buyrent == 'rent'){
                var decodeurArray = [
                    {
                        prix: '6.95',
                        titre_panier: CoreTools.translate('Location du décodeur gratuit 1 an', 'TV receiver rental free for 1 year'),
                        promoCode: 'PRM00132',
                        recurrent: 'oui',
                        type: 'decodeur',
                        post_title: CoreTools.translate('Location du décodeur gratuit 1 an', 'TV receiver rental free for 1 year'),
                    }
                ]
                LocalStorage.set('DecodeurPromotion', JSON.stringify(decodeurArray));
            }else{
                LocalStorage.set('DecodeurPromotion', JSON.stringify([]));
            }
        }
        else{
            LocalStorage.set('DecodeurPromotion', JSON.stringify([]));
        }
    }

    checkBundleArrayDecodeur(array){
        this.hasTV = CookiesService.getCookies('hasTV', true);
        let bundleID = LocalStorage.get('selectedBundles');
        if(bundleID == null){
            return false;
        }
        else if( bundleID.length == 0){
            return true;
        }
        else{
            bundleID = parseInt(bundleID[0]);
            if(this.hasTV){
                if(array.includes(bundleID)){
                    return true;
                }
                else{
                    return false;
                }
            }
            else{
                return true;
            }
        }
    }
}