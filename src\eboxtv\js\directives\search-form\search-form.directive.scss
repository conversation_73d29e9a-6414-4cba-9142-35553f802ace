@import '~@app/scss/bases/font-names.base.scss';
@import '~@common/scss/helpers/variables/colors.variables.scss';

[search-form] {
	[search-form-noresult] {
		display: none;
	}

	.channel {
		&--premium {
			$premiumBannerColor: darken($c-primary, 10%);
			$darkPremiumBannerColor: darken($premiumBannerColor, 10%);

			&:not(.channel--unavailable) {
				.channel {					
					&__banner {
						top: auto;
						bottom: 36px;
						left: -5px;

						+ .channel__logo-container {
							.channel__logo {
								padding-bottom: 22px;
							}
						}
					}
			
					&__banner-wrapper {
						padding: 4px 5px;
						font: 400 12px/1.2 $f-primary;
						background-color: $premiumBannerColor;
			
						&:before, &:after {
							top: auto;
							bottom: 100%;
						}
			
						&:before {
							border-width: 0 0 5px 5px;
							border-color: transparent transparent $darkPremiumBannerColor transparent;
						}
				
						&:after {
							border-width: 5px 0 0 5px;
							border-color: transparent transparent transparent $darkPremiumBannerColor;
						}
					}
				}
			}
		}
	}
}