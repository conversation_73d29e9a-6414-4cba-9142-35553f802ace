.Ribbon {
	position: absolute;
	display: block;
	font-size: 0;

	&__title {
		color: #FFF;
	}

	&--corner {
		top: 16px;
		right: -26px;
		height: 25px;
		transform: rotate(45deg);

		.Ribbon {
			&__svg {
				width: 110px;
				height: 25px;
			}

			&__title {
				margin-top: -2px;
				font: 600 12px/16px $f-primary;
			}
		}
	}

	&--left, &--right {
		top: 24px;

		.Ribbon {
			&__svg {
				width: 114px;
				height: 37px;
			}

			&__title-wrapper {
				margin-top: 1px;
			}
		}
	}
	
	&--left {
		left: -5px;

		.Ribbon {
			&__title-wrapper {
				padding: 0 16px 0 8px;
			}
		}
	}

	&--right {
		right: -5px;

		.Ribbon {
			&__title-wrapper {
				padding: 0 8px 0 16px;
			}
		}
	}

	&--center {
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 100%;

		.Ribbon {
			&__title-wrapper {
				padding: 0 8px 0 16px;
			}

			&__svg {
				display: none;
			}

			&__fake-svg {
				position: relative;
				width: 100%;
				height: 32px;
				box-sizing: content-box;
				padding: 0 5px;
				left: -5px;
				background: $c-primary;

				&:before, &:after {
					content: '';
					position: absolute;
					display: block;
					width: 0;
					height: 0;
					bottom: 100%;
					border-style: solid;
					filter: url(#darkenFilter);
				}

				&:before {
					left: 0;
					border-width: 0 0 5px 5px;
					border-color: transparent transparent $c-primary transparent;
				}

				&:after {
					right: 0;
					border-width: 5px 0 0 5px;
					border-color: transparent transparent transparent $c-primary;
				}
			}
		}
	}

	&__wrapper {
		position: relative;
	}

	&__title-wrapper {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: 100%;
	}
	
	&__title {
		display: block;
		font: 100 12px/16px $f-primary;
		user-select: none;
		white-space: nowrap;
		max-width: 100%;
		overflow: hidden;
	}

	&__svg {
		display: inline-block;
		fill: $c-primary;
	}
}