.ebtv-resume {
    position: absolute;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    width: 100%;
    max-height: 85vh;
    max-height: calc(100vh - 100px);
    left: 0;
    bottom: 0;
    background: url('~@common/img/ebox-texture-background--red.jpg') center center repeat;
    z-index: 10;
    @media (max-width: 991px) {
        padding: 11px 0;
        z-index: 40;
        transform: none !important;
        max-height: none;
    }

    @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
        @at-root {
            body.is-search-results-opened, body.is-search-input-focused {
                .ebtv-resume {
                    display: none;
                }
            }
        }
    }

    &--opened {
        .ebtv-btn-details__icon {
            transform: rotate(0deg);
            @media (max-width: 991px) {
                transform: rotate(90deg);

            }
        }
    }

    &__sticky-cage {
        display: block;
        float: left;
        width: 100%;
        height: 104px;

        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            height: 112px;
        }
        @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
            height: 76px;
        }
    }

    &__content {
        &.container {
            padding: {
                top: 80px!important;
                bottom: 80px!important;
            };
        }
        @media (max-width: 991px) {
            width: 100%;
        }
    }

    &__section {
        @media (max-width: 991px) {
            padding-left: 30px;
            padding-right: 30px;
        }
        &--paddingright {
            padding-right: 61px;
            @media (max-width: 991px) {
                padding-right: 30px;
                border-right: 1px solid $c-white;
            }
        }
        &--paddingleft {
            padding-left: 61px;
            @media (max-width: 991px) {
                padding-left: 30px;
            }
        }
    }

    &__ebtv-btn-details.ebtv-btn-details {
        @media (max-width: #{map-get($ebtvBreakpoints, tabletPortrait) - 1}) {
            display: none;
        }
    }
}
.ebtv-resume--closed {
    .ebtv-resume__container {

        &--tablethead {
            display: none;
        }
        &--wrapper {
            display: none;
        }
        @media (max-width: 991px) {
            position: fixed;
            top: 0;
            transform: translateX(100%);
            transition: all 0.3s $cubic;

        }
    }
}
.ebtv-resume__container {
    overflow: auto;
    //display: none;
    @media (max-width: 991px) {
        overflow: visible;
    }

    .ebtv-resume__container__wrapper {
        @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
            padding-top: 0;
        }
    }
    .ebtv-resume-head  {
        display: none;
    }
    .ebtv-resume__container--tablethead {
        position: relative;
        p {
            color: $c-white;
            font-size: 22px;
            margin: 0 auto;
            line-height: 52px;
            font-weight: 500;
            font-family: $ebtv-f-secondary;
        }
        button {
            position: absolute;
            right: 10px;
            top: 10px;
            background: none;
            border : 0;
            width: 32px;
            height: 32px;
            svg {
                fill: $c-white;
                width: 32px;
                height: 32px;
            }
        }
    }


    background: url('~@common/img/ebox-texture-background--red.jpg') center center repeat;
    @media (max-width: 991px) {
        position: fixed;
        top: 0;
        transform: translateX(100%);
        right: 0;
        max-width: 661px;
        width: 100%;
        z-index: inherit;
    }
    &--tablethead {
        height: 70px;
        background: url('../../img/ebtv-back-header-tablet-resume.svg') center bottom no-repeat;
        width: 100%;
        display:none;
        @media (max-width: 991px) {
            display: flex;

        }

    }
    &--wrapper {
        @media (max-width: 991px) {
            padding-top: 13px;
            display: flex;
            flex-grow: 1;
            overflow: auto;
            height: calc(100vh - 70px);
            flex-direction: column;
            .ebtv-resume__head  {
                display: block;

                .ebtv-resume-head__title {
                    font-size: 16px;
                    font-weight: 500;
                }
                .ebtv-resume-head__price {
                    font: 700 22px $ebtv-f-primary;
                    sup {
                        font-size: 12px;
                    }

                    span {
                        display: none;
                    }
                }
            }
        }
    }
}

.ebtv-resume__sticky-cage {
    @media (max-width: 767px) {
        .ebtv-resume__head {
            .ebtv-resume-head__title {
                font-size: 12px;
                font-weight: 700;
                line-height: 14px;
            }
        }
    }
}


.ebtv-resume-head  {
    margin: 0 auto;
    position: relative;
    // &:after {
    //     width: 100%;
    //     height: 2px;
    //     background: $c-white;
    //     display: inline-block;
    //     position: absolute;
    //     left: 0;
    //     bottom: -22px;
    //     z-index: 2;
    //     @media (max-width: 991px) {
    //         display: none;
    //     }
    // }

    @media (max-width: map-get($ebtvBreakpoints, BSmd)) {
        margin-bottom: 0;
    }

    .col-sm-4 {
        display: flex;
        height: 100px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:nth-child(2),
        &:nth-child(1) {
            border-right: 1px solid $c-white;
            @media (max-width: 991px) {
                //border-right: 0;
            }
        }
        @media (max-width: 991px) {
            height: 75px;
        }
    }

    &__flex-row {
        display: flex;
        @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
            height: 55px;
        }
    }

    &__column {
        position: relative;
        display: inline-flex;
        align-items: center;
        text-align: center;
        float: none;
        flex-grow: 1;

        &--center {
            &:before, &:after {
                content: '';
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                height: 40px;
                width: 3px;
                border-radius: 2px;
                background: #D89C99;
                @media (max-width: map-get($ebtvBreakpoints, tabletLandscape)) {
                    width: 2px;
                }
                @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
                    width: 1px;
                }
            }

            &:before {
                left: 0;
            }

            &:after {
                right: 0;
            }
        }

        &--mobile {
            float: left;
            &:after {
                content: none;
            }
        }

        &--final {
            text-align: left;
            color: $c-white;
        }

        &--actions {
            justify-content: flex-end;

            @media (max-width: 767px) {
                justify-content: center;
            }
        }

        .ebtv-btn-procede {
            width: auto;
            line-height: 45px;
            padding: 0 25px;
            font-size: 12px;
        }

        @media (max-width: map-get($ebtvBreakpoints, bigMobile)) {
            padding: 0 5px;
        }
    }

    &__price, &__submit-btn {
        display: inline-block;
        text-align: center;
    }

    &__price-container {
        margin-right: 60px;
    }

    &__message {
        font: 400 14px/20px $ebtv-f-primary;
    }


    &__title {
        font: 700 20px/24px $ebtv-f-primary;
        color: $c-white;
        margin-bottom: 0;

        &--message {
            font-size: 24px;
            line-height: 28px;
        }

        @media (max-width: 991px) {
            font-size: 16px;
            font-weight: 500;
        }
    }

    &__price {
        font: 700 25px/30px $ebtv-f-primary;
        color: $c-white;
        margin-bottom: 0;
        sup {
            font-weight: 900;
            font-size: 15px;
        }
        @media (max-width: 991px) {
            font: 700 22px $ebtv-f-primary;
            sup {
                font-size: 12px;
            }
            .ebtv-resume-head__cost-permonth {
                display: none;
            }
        }
    }

    &__cost-counter {
        font-weight: 700;
        letter-spacing: 1px;
    }

    &__cost-permonth {
        font-weight: 300;
    }

}

.ebtv-section-head {
    width: 100%;
    margin-bottom: 36px;
    border-bottom: 1px solid $c-white;
    position: relative;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    &__title-container {
        display: flex
    }
    &__price-container {
        display: flex;
        text-align: right;
        &__price {
            color: $c-white;
            font: 700 20px/35px $ebtv-f-primary;
            &--darken {
                color: $c-black;
            }
        }

    }
    &__title {
        color: $c-white;
        font: 700 25px/35px $ebtv-f-primary;
        display: inline-block;
        margin: 0;
    }
    &__icon {
        width: 35px;
        height: 35px;
        fill: $c-white;
        display: inline-block;
        vertical-align: top;
        margin-right: 10px;
    }

    &__ebtv-btn-procede {
        border: none;
    }
}

.ebtv-section-body-container {
    position: relative;
    &:after {
        content: "";
        width: 1px;
        background: $c-white;
        height: 100%;
        position: absolute;
        right: -61px;
        top: 0;
        @media (max-width: 991px) {
            width: 0;
            right: 0;
        }
    }
}
.ebtv-section-body  {
    &__title {
        font: 700 25px/31px $ebtv-f-primary;
        color: $c-white;
        margin: 0;
        &--uppercase {
            text-transform: uppercase;
        }
        @media(max-width:991px) {
            font: 700 22px/26px $ebtv-f-primary;
        }
    }
    &__subtitle {
        color: $c-white;
        font: 500 16px/22px $ebtv-f-primary;
        margin: 0;
    }

}

.ebtv-price-container {
    margin-top: 15px;
    &__text {
        font: 500 16px/22px $ebtv-f-primary;
        color: $c-white;
        &--bold {
            font-weight: 700;
        }
    }
}

.ebtv-section-edit {
    margin-top: 30px;
    margin-bottom: 84px;
    text-align: right;
    &--last {
        margin-bottom: 0;
    }
    &__btn {
        color: $c-white;
        font: 16px/22px $ebtv-f-primary;
        span {
            padding: 0 1px;
        }
        &:hover {
            color: $c-black;
        }
    }
}

.ebtv-resume__container--wrapper {
    .ebtv-resume-head {
        padding-bottom: 13px!important;
        border-bottom: 2px solid $c-white;
        .col-sm-6 {
            &:nth-child(1) {
                // border-right: 1px solid $c-white;
            }
        }
    }
    .container {
        width: 100%;
    }
}