@import '~@common/scss/helpers/variables/colors.variables.scss';
@import '~@app/scss/bases/font-names.base.scss';

qualified-speeds,
.Promos__epargne {
	display: inline-block;

	.QualifiedSpeeds {
		display: inline-block;
		min-width: 200px;
		padding: 9px 15px;
		border: 2px solid $c-light-grey;
		border-radius: 6px;

		&__container {
			display: flex;
			font-size: 0;
		}

		&__col {
			display: inline-flex;
			flex: 1 1 50%;
			align-items: center;
			border-right: 1px solid $c-light-grey;
			padding-left: 15px;

			&:first-child {
				padding-left: 0;
				padding-right: 15px;
			}

			&:last-child {
				border-right: none;
			}
		}

		&__title {
			font: 400 16px/1.4 $f-secondary;
		}

		&__title-bold {
			display: block;
			font: 700 16px/20px $f-primary;
			text-transform: uppercase;
			white-space: nowrap;
		}

		&__speed-list {
			display: inline-block;
			list-style: none;
			padding: 0;
			margin: 0;
			color: #626262;
		}

		&__speed-item {
			white-space: nowrap;
		}

		&__speed-icon, &__speed-title {
			display: inline-block;
			vertical-align: middle;
		}

		&__speed-icon {
			width: 24px;
			height: 24px;
			fill: currentColor;
			margin: 0 4px 0 0;
		}

		&__speed-title {
			font: 400 16px/23px $f-primary;
		}

		&__speed-number {
			font-weight: 700;
		}
	}
}

.Promos__epargne {
	.QualifiedSpeeds {
		min-width: auto;
		transition: all 0.2s cubic-bezier(0.55, 0, 0.1, 1);
		background: #BE2323;
		border: 2px solid #BE2323;
		
	}
	.QualifiedSpeeds__col {
		border-right: 0;
		&:first-child {
			padding-right: 0;
		}
	}
	&:hover {
		color: #FFF;
		.QualifiedSpeeds {
			background: #333333;
			border: 2px solid #333333;
		}
	}
}

.Promos__link {
	color: #FFF;
}