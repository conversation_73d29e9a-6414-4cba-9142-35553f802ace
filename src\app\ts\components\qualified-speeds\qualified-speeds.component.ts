import { Component } from '@core/component';
import { QualifyService, Qualification } from '@common/ts/services/Qualify.service';
import { CoreTools } from '@core/helpers';
import './qualified-speeds.component.scss';

export class QualifiedSpeedsComponent extends Component {
	static selector: string = 'qualified-speeds';

	static template: string = require('./qualified-speeds.component.hbs');
	
	private $qualifier: QualifyService = QualifyService.getInstance();
	private $qualification: Qualification = this.$qualifier.getQualification();

	attrs: {
		download?: number;
		upload?: number;
	};

	state: QualifiedSpeedsState = {
		downloadSpeed: this.attrs.download ? this.attrs.download : this.$qualification ? parseFloat(this.$qualification.details.ServiceSpeed).toFixed(1) : null,
		uploadSpeed: this.attrs.upload ? this.attrs.upload : this.$qualification ? parseFloat(this.$qualification.details.mb_upload || this.$qualification.details.UploadSpeed).toFixed(1) : null,
		downloadSpeedmbitsLabel: "",
		uploadSpeedmbitsLabel: ""
	};

	constructor(host: HTMLElement) {
		super(host, [
			{name: 'download', type: 'float'},
			{name: 'upload', type: 'float'}
		]);
		this.onInit();
		
	}

	private onInit(): void {
		if(CoreTools.lang === 'fr'){
			this.state.downloadSpeedmbitsLabel = 'Vitesse de téléchargement maximum: ' + this.state.downloadSpeed + '  Mégabits par seconde';
			this.state.uploadSpeedmbitsLabel = 'Vitesse d\'envoi maximum: ' + this.state.uploadSpeed + '  Mégabits par seconde';
		}else{
			this.state.downloadSpeedmbitsLabel = 'Maximum Download Speed: ' + this.state.downloadSpeed + ' Megabits per second';
			this.state.uploadSpeedmbitsLabel = 'Maximum Upload Speed: ' + this.state.uploadSpeed + ' Megabits per second';
		}
	}
}

interface QualifiedSpeedsState {
	downloadSpeed?: string|number;
	uploadSpeed?: string|number;
	downloadSpeedmbitsLabel?: string;
	uploadSpeedmbitsLabel?: string;
}

interface QualifiedSpeedsAttrs {
	download?: number;
	upload?: number;
}