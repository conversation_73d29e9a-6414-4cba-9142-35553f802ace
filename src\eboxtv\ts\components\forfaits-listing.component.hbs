<ul class="ForfaitsListing">
	{{#each items as |item| }}
	<li class="ForfaitsListing__item{{#if (eq item.type 'base')}} ForfaitsListing__item--faded{{/if}}" {{#if ../color}}style="color: {{ ../color }};"{{/if}}>
		{{#if (ifCond item.icon '!==' '')}} 
		<svg  class="ForfaitsListing__icon">
			<use xlink:href="#{{ item.icon }}"></use>
		</svg>
	{{/if}}
		{{#if (ifCond item.icon '!==' '')}}
		<h6 class="ForfaitsListing__title">{{{ item.title }}}{{#if item.count}} ({{ item.count }}){{/if}}</h6>
		{{else}}
		<h6 class="ForfaitsListing__title" {{#if (not item.subtotal) }} style="font-weight: bold;" {{/if}}>{{{ item.title }}}{{#if item.link}} <a href="{{ item.link }}" style="color: currentColor;">({{ item.count }})</a> {{/if}}</h6>
		{{!-- <button style="border: none; background: none;" class="sticky-cart-action-btn"><svg style="width: 10px; height: 10px; fill: white;" class="icon icon-ic-edit-info2022"><use xlink:href="#icon-ic-edit-info2022"></use></svg></button> --}}
		{{/if}}
		{{#if item.subtotal}}
		<span style="flex-grow: 1;"></span>
		{{else}}
		<span class="ForfaitsListing__sep"></span>
		{{/if}}
		<span class="ForfaitsListing__price">{{#if item.promo}}-{{/if}}{{formatPrice item.cost}}{{sectionMonthly}}</span>
	</li>
	{{/each}}
</ul>