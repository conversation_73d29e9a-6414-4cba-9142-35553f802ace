.CardListing {
    width: 100%;
    padding: 100px 0 20px;
    display: inline-block;
    position: relative;
    &__title {
        font-weight: 700;
        margin: 0 0 70px;
        @media (max-width: 991px) {
            margin: 0 0 60px 5px;
            font: 700 35px/40px $f-primary;
        }
        @media (max-width: 767px) {
            margin: 0 0 40px 0;
            font: 700 20px/24px $f-primary;
        }
    }
    ul {
        margin: 0;
        padding: 0;
        li {
            display: inline-block;
            margin: 0 60px 80px 0;
            padding: 0;
            vertical-align: top;
            &:nth-child(3n) {
                margin-right: 0;
                @media (max-width: 991px) {
                    margin-right: 30px;
                }
            }
            @media (max-width: 1199px) {
                margin: 0 30px 80px 0;
            }
            @media (max-width: 991px) {
                margin: 0 20px 70px 0;
                &:nth-child(odd) {
                    margin: 0 22px 70px 6px;
                }
                &:nth-child(even) {
                    margin-right: 6px;
                    float: right
                }
            }
            @media (max-width: 767px) {
                width: 45%;
                margin: 0 30px 42px 0;
                &:nth-child(odd) {
                    margin: 0 4% 42px 0;
                }
                &:nth-child(even) {
                    margin-right: 0;
                    float: right
                }
            }
            @media (max-width: 600px) {
                width: 100%;
                margin: 0 0 20px 0;
                &:nth-child(odd) {
                    margin: 0 0 20px 0;
                }
                &:nth-child(even) {
                    margin-right: 0;
                    float: right
                }
            }
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0 0;
    }
    @media (max-width: 767px) {
        padding: 40px 0 20px;
    }
}
