.infos {
	background: $c-grey;
	padding: 30px 0 45px;

	@media (max-width: map-get($breakpoints, carouselMobile)) {
		padding-top: 50px;
	}

	&__wrapper {
		.row {
			display: flex;
			flex-wrap: wrap;
			align-items: end;
		}
	}

	&__block-container {
		margin-bottom: 60px;

		&.carousel-active {
			overflow: hidden;
			margin: 0 0 60px;

			.infos__infoblock {
				display: block;
				float: none;
				width: auto;
			}

			.owl-dot {
				border-color: $c-mid-grey;
				&.active {
					border-color: $c-grey-dark;
				}
			}
		}
	}

	&__infoblock {
		margin-bottom: 30px;
		vertical-align: top;
		width: 49.9%;

		&:last-child, &:nth-last-child(2) {
			margin-bottom: 0;
		}

		&:first-child {
			.infoblock {
				&__title {
					
				}	
			}
		}
	}

	&__notice-item {
		font: 400 12px $f-primary;
	}

	.owl-dots {
		width: 100%;
		max-width: 215px;
	}
}