/* global jQuery */
import './section-toggle.directive.scss';
import { Directive } from '@core/directive';
import { TweenLite } from 'gsap/TweenLite';
import TimelineLite from 'gsap/TimelineLite';
import { Power2 } from 'gsap/EasePack';
import { TVOrderService } from '../../services';
import { ChannelSelectDirective } from '..';
import { CoreTools } from '@core/helpers';
import { ViewChangeDetector } from '@common/ts/services/ViewChangeDetector.service.ts';

export class SectionToggleDirective extends Directive {
	// STATIC PROPERTIES //
	static selector = '[section-toggle]';


	// PRIVATE PROPERTIES //
	_isOpenedValue = this.attrs.isOpened;
	_hideSectionValue = false;
	_mediaMatchValue = this.attrs.mediaQuery ? window.matchMedia(this.attrs.mediaQuery).matches : true;
	_$triggerElement = jQuery(this.attrs.triggerSelector, this.host);
	_contentElement  = this.host.querySelector(this.attrs.containerSelector);

	$TVOrder = TVOrderService.getInstance();
	$changeDetector = ViewChangeDetector.getInstance();
	
	// GETTERS AND SETTERS //
	get isOpened() {
		return this._isOpenedValue;
	}
	set isOpened(val) {
		if (val !== this._isOpenedValue) {
			this._isOpenedValue = val;
			this._update(val);
		}
	}

	get mediaMatch() {
		return this._mediaMatchValue;
	}
	set mediaMatch(val) {
		if (val !== this._mediaMatchValue) {
			this._mediaMatchValue = val;
			this._onMediaMatchChange(val);
		}
	}

	get hideSection() {
		return this._hideSectionValue;
	}
	set hideSection(val) {
		if (val !== this._hideSectionValue) {
			this._hideSectionValue = val;

			if (val)
				this.$host.hide();
			else
				this.$host.show();
		}
	}

	constructor(host) {
		super(host, [
			{name: 'trigger-selector', required: true},
			{name: 'is-opened', type: 'eval', default: false},
			'container-selector',
			'opened-class-name',
			'media-query'
		]);

		this._init();
	}

	_init() {
		this._subscribeToObservablesChange();
		this._bindEvents();
		setTimeout(this._update.bind(this, this._isOpenedValue, true), 100);
	}


	// PUBLIC METHODS
	open(noTrans = false) {
		const speed = noTrans ? 0 : 0.3;
		const tl = new TimelineLite();

		jQuery(this.host).find('.channel-list__list').show();

		if (this._currentTL)
			this._currentTL.kill();

		this._currentTL = tl.add(this._addClassName.bind(this))
			.to(this.host, speed, {
				height: this.host.scrollHeight,
				ease: Power2.easeOut
			});

		if (this._contentElement) {
			tl.fromTo(this._contentElement, 0.3, {
				y: 30,
				alpha: 0
			}, {
				y: 0,
				ease: Power2.easeOut,
				alpha: 1
			}, 0.1)
			.set(this._contentElement, {clearProps: 'y, opacity'});
		}

		tl.set(this.host, {overflow: 'visible'});
		this._isOpenedValue = true;

		var action = CoreTools.translate('développé', 'expanded');
		this.srSpeak('Section ' + jQuery(this.host).find('.channel-list__header').find('h4').text() + action, 'assertive');

	}

	close(noTrans = false) {
		const speed = noTrans ? 0 : 0.2;
		const borderWidth = parseInt(getComputedStyle(this.host).getPropertyValue('border-width')) || 0;
		const tl = new TimelineLite();
		
		if (this._currentTL)
			this._currentTL.kill();

		this._currentTL = tl.add(this._removeClassName.bind(this))
			.set(this.host, {overflow: 'hidden'})
			.to(this.host, speed, {
				height: this._$triggerElement.outerHeight() + borderWidth - 2,
				ease: Power2.easeInOut
			});

		this._isOpenedValue = false;

		var action = CoreTools.translate('reduit', 'collapsed');
		this.srSpeak('Section ' + jQuery(this.host).find('.channel-list__header').find('h4').text() + action, 'assertive');


		jQuery(this.host).find('.channel-list__list').hide();
		
	}


	// PRIVATE METHODS
	_subscribeToObservablesChange() {
		this.$TVOrder.selectedBundlesChange$.subscribe(
			this._onBundlesChanges.bind(this)
		);
	}

	_onBundlesChanges() {
		setTimeout(this._hideIfEmpty.bind(this));
	}

	_onViewChanges(changer) {
		if (!this.mediaMatch) {
			setTimeout(this._reset.bind(this), 100);
		}
	}

	_hideIfEmpty() {
		const channelSelects = this.host.querySelectorAll('.channel-list__item');
		let   channelsAllHidden = false;

		for (let i = 0; i < channelSelects.length; i++) {
			if (getComputedStyle(channelSelects[i]).display === 'none')
				channelsAllHidden = true;
			else if (channelsAllHidden)
				channelsAllHidden = false;
		}

		this.hideSection = channelsAllHidden;
	}

	_bindEvents() {
		this._$triggerElement.on('click', this._onTriggerClick.bind(this));

		if (this.attrs.mediaQuery) {
			jQuery(window).on('resize', this._onResize.bind(this));
		}
	}

	_onTriggerClick(evt) {
		this._toggleValue();
	}

	_toggleValue() {
		this.isOpened = !this.isOpened;
	}

	_onMediaMatchChange(val = this._mediaMatchValue) {
		if (!val)
			this._reset();
		else
			this[(this.attrs.isOpened ? 'open' : 'close')](true);
	}

	_update(val = this._isOpenedValue, noTrans = false) {
		if (this.attrs.mediaQuery && !window.matchMedia(this.attrs.mediaQuery).matches)
			return;

		if (val)
			this.open(noTrans);
		else
			this.close(noTrans);
	}

	_onResize() {
		if (!this.$host.is(':visible')) return;

		this.mediaMatch = window.matchMedia(this.attrs.mediaQuery).matches;

		if (!this.isOpened && !this.mediaMatch)
			this.open(true);

		if (this.isOpened)
			this._updateSize();
	}

	_updateSize() {
		const height = this._getDirectChildrenHeight();
		TweenLite.set(this.host, { height });
	}

	_addClassName() {
		if (this.attrs.openedClassName)
			this.$host.addClass(this.attrs.openedClassName);
	}

	_removeClassName() {
		if (this.attrs.openedClassName)
			this.$host.removeClass(this.attrs.openedClassName);
	}

	_reset() {
		TweenLite.set(this.host, {
			clearProps: 'all'
		});
	}

	_getDirectChildrenHeight() {
		let total = 0;

		for (let i = 0; i < this.host.children.length; i++) {
			const currentChild = this.host.children[i];
			total += currentChild.clientHeight;
		}
		return total;
	}

	srSpeak(text, priority) {
		var el = document.createElement("div");
		var id = "speak-" + Date.now();
		el.setAttribute("id", id);
		el.setAttribute("aria-live", priority || "polite");
		el.classList.add("visually-hidden");
		document.body.appendChild(el);
  
		window.setTimeout(function () {
		  document.getElementById(id).innerHTML = text;
		}, 100);
  
		window.setTimeout(function () {
			document.body.removeChild(document.getElementById(id));
		}, 1000);
	}
}