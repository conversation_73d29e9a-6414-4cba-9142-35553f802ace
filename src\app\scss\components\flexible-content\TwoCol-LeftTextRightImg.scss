.TwoCol-LeftTextRightImg {
    padding: 100px 0;
    width: 100%;
    display: inline-block;
    position: relative;
    &__title {
        margin: 0 0 80px;
    }
    &__leftTxt {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 45px 0 15px;
        // &.col-md-pull-6 {
        //     position: static;
        // }
        h2, h3, h4, h5, h6 {
            margin: 0 0 10px;
        }
        p {
            margin: 0 0 30px;
            &:last-of-type {
                margin: 0;
            }
        }
        p + ul {
            margin: 0 0 30px;
        }
        .ButtonEffect {
            margin: 40px 40px 0 0;
            float: left;
            &--border {
                height: 60px;
                margin: 40px 0 0!important;
                float: left;
                line-height: 58px;
                &:hover {
                    border-color: $c-grey;
                }
                @media (max-width: 1199px) {
                    margin: 15px 0 0 0!important;
                }
                @media (max-width: 1024px) {
                    border-width: 2px;
                    height: 40px;
                    line-height: 38px;
                    margin: 40px 0 0 0!important;
                    padding: 0 13px;
                }
                @media (max-width: 356px) {
                    margin: 10px 0 0 0!important;
                }
            }
            @media (max-width: 767px) {
                margin: 40px 10px 0 0;
                padding: 0 13px;
            }
        }
        @media (max-width: 991px) {
            width: 100%;
            padding: 0 19px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
    }
    &__rightImg {
        width: 50%;
        position: relative;
        float: left;
        padding: 0 15px;
        overflow: hidden;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.7s $cubic;
        will-change: opacity, transform;
        display: flex;
        align-items: center;
        justify-content: center;
        &--shown {
            opacity: 1;
            transform: translateX(0);
        }
        img {
            max-width: 100%;
            height: auto;

            @media (max-width: 991px) {
                height: auto;
                max-height: 100%;
                width: auto;
                max-width: 100%;
            }
        }

        @media (max-width: 991px) {
            width: 100%;
            padding: 0 22px;
            margin: 0 0 30px;
        }
        @media (max-width: 767px) {
            padding: 0 15px;
        }
    }
    @media (max-width: 991px) {
        padding: 70px 0;
    }
    @media (max-width: 767px) {
        padding: 40px 0;
    }
}

body.page-id-8265, body.page-id-10827 {
    .TwoCol-LeftTextRightImg {
        &__leftTxt {
            h2, h3, h4 {
                margin: 0 0 60px;
                @media (max-width: 1024px) {
                    margin: 0 0 10px;
                }
            }
            h3 {
                &.short-title {
                    max-width: 485px
                }
                @media (max-width: 420px) {
                    line-height: 30px;
                }
            }
            h5 + ul {
                margin: 0 0 40px;
                @media (max-width: 1024px) {
                    margin: 0 0 30px;
                }
            }
            .col2 {
                float: left;
                margin: 0 55px 0 0;
                @media (max-width: 767px) {
                    float: none;
                    margin: 0;
                }
                &:last-child {
                    margin: 0;
                }
            }
            .Wysiwyg {
                display: inline-block;
                transform: translateY(-50%);
                top: 50%;
                position: relative;
                @media (max-width: 991px) {
                    transform: none;
                    top: auto;
                }
            }
        }
    }
}