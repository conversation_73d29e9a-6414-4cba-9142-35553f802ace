.doodled {
	position: relative;
	z-index: 1;

	&:before, &:after {
		position: absolute;
		content: '';
		background: {
			size: contain;
			position: center;
			repeat: no-repeat;
		}
	}
}

.doodle-img {
	position: absolute;
}


// DOLLARED
.full-dollared {
	&:before {
		right: 100%;
		top: 15px;
		margin-right: 6px;
		width: 15px;
		height: 15px;
		background-image: url('../../img/dollar-left.svg');
		transform: rotate(-20deg);
	}

	&:after {
		width: 18px;
		height: 18px;
		left: 100%;
		background-image: url('../../img/dollar-right.svg');
		transform: rotate(3deg);
		bottom: 15px;
		margin-left: 5px;
	}
}


// UNDERLINES
.underlined, .underlined2 {
	&:after {
		width: 106%;
		height: 10px;
		top: 100%;
		left: 50%;
		margin-top: -3px;
		transform: translateX(-50%);
		background-size: unset;
	}
}

.underlined {
	&:after {
		background-image: url('../../img/underline01.svg');
	}
}

.underlined2 {
	&:after {
		margin-top: -5px;
		background-image: url('../../img/underline02.svg');
	}
}


// SPLASHED
.full-splashed {
	&:before, &:after {
		top: 0;
		height: 100%;
	}

	&:before {
		right: 100%;
		margin-right: 6px;
		width: 15px;
		height: 100%;
		background-image: url('../../img/right--white.svg');
		transform: rotateX(180deg) rotateY(180deg) rotate(-15deg);
	}

	&:after {
		width: 30px;
		height: 120%;
		left: 100%;
		background-image: url('../../img/left--white.svg');
		top: -5px;
		margin-left: 10px;
		transform: rotateX(180deg) rotateY(180deg);
	}
}

.splashed {
	&:after {
		width: 150%;
		height: 160%;
		top: 45%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-image: url('../../img/highlight-free.svg');
		background-size: contain;
	}
}


// ARROWED
.full-arrowed {
	&:before {
		right: 100%;
		margin-right: 15px;
		width: 42px;
		height: 20px;
		bottom: 10px;
		background-image: url('../../img/arrow-left.svg');
		transform: rotateX(180deg) rotate(-15deg);
	}

	&:after {
		width: 42px;
		height: 20px;
		left: 100%;
		background-image: url('../../img/arrow-right.svg');
		bottom: 20px;
		margin-left: 10px;
	}
}


// CIRCLED
.circled {
	position: relative;
	z-index: 1;

	&:before {
		width: 130%;
		height: 200%;
		top: -20px;
		left: -6px;
		background-image: url('../../img/circle.svg');
		z-index: -1;
		@include fromRootSelector(':lang(en_CA)') {
			width: 120%;
			left: -20px;
		}
	}
}