<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="46" height="27" viewBox="0 0 46 27">
  <defs>
    <polygon id="b" points="143.428 109 125.907 122.355 108.387 109"/>
    <filter id="a" width="165.6%" height="272.2%" x="-32.8%" y="-71.1%" filterUnits="objectBoundingBox">
      <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="3.5"/>
      <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" transform="translate(-103 -104)">
    <use fill="black" filter="url(#a)" xlink:href="#b"/>
    <use fill="#FFFFFF" xlink:href="#b"/>
  </g>
</svg>
