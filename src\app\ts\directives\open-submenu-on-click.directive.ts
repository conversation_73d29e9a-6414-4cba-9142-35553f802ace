import { Directive } from "@core/directive";

export class OpenSubmenuOnClick extends Directive {

    static selector: string = '.menu-item-has-children > a';

    private $targetElement: J<PERSON><PERSON>y;

	constructor(host: HTMLElement) {
        super(host);
        this.init();
    }

    private init() {
        this.cacheDom();
        this.bindEvents();
    }

    private cacheDom() {
        //this.parentContainer = this.$host.parent();
    }

    private bindEvents() {
		this.$host.on('click', this.onHostClick.bind(this));
    }

    private onHostClick(evt: MouseEvent) {

        evt.preventDefault();

        
        if (this.$host.parent().hasClass('NavPrinc__item--subopen')) {
            this.$host.parent().removeClass('NavPrinc__item--subopen');
            this.$host.parent().next('.NavPrinc__submenu').removeClass('NavPrinc__submenu--opened');
        } else {
            this.$host.parent().addClass('NavPrinc__item--subopen');
            this.$host.parent().next('.NavPrinc__submenu').addClass('NavPrinc__submenu--opened');
        }
    }

}