/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { LocalStorage } from '@common/ts/services/LocalStorage.ts';
import { CookiesService } from '@common/ts/services/Cookies.service';

export class NumeriquePopupDirective extends Directive {

	_$OrderService = OrderOnlineService.getInstance();

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[data-numeriquePopup]';

    type = "";
    fournisseur = "";
    minVitesse = 25;
    langue = "";

	constructor(host) {
		super(host, []);
		this._onInit(host);
	}

	_onInit(host) {

        this.langue = CookiesService.getCookies('wp-wpml_current_language');

        this.type = host.getAttribute('data-popup');

        this.fournisseur = host.getAttribute('data-fournisseur');

        if( this.fournisseur == 'cv-cable' ){
            this.minVitesse = 50;
        }else if( this.fournisseur == 'c-cable' ){
            this.minVitesse = 15;
        }else if( this.fournisseur == 'v-cable'){
            this.minVitesse = 30;
        }

        this.verifyDSLminVitesse()



        this.initPopup(this.type, host);

	}

    initPopup(type, host){


        switch(type){

            case 'telephonie':
                this.telephonePopup();
                break;
            case 'internet':
                this.internetPopup();
                break;
            case 'internetValidation':
                host.addEventListener('click', function(evt){
                    this.internetPopupValidation(evt);
                }.bind(this));
                break;
            case 'telephonieValidation':
                host.addEventListener('click', function(evt){
                    this.telephoniePopupValidation(evt);
                }.bind(this));
                break;
            case 'teleLanding':
                host.addEventListener('click', function(evt){
                    this.eboxTvPopup(evt);
                }.bind(this));
                break;
            case 'teleValidation':
                host.addEventListener('click', function(evt){
                    this.telePopupValidation(evt);
                }.bind(this))
                break;
            case 'phoneValidation':
                host.addEventListener('click', function(evt){
                    this.phonePopupValidation(evt);
                }.bind(this))
                break;
            case 'landingChainePrincipal':
                    this.redirectIfNoTV();
                break;
            case 'landingChainePremium':
                    this.redirectIfNoTV();
                break;

            case 'deleteTvCheckForTelephonie':
                host.addEventListener('click', function(evt){
                    this.deleteTvCheckForTelephonie(evt);
                }.bind(this));
                break;
            case 'validationTvForfait':
                break;
            case 'validationInfonuagique':
                break;
            case 'toNumeric':
                host.addEventListener('click', function(evt){
                    this.toNumeric(evt);
                }.bind(this));
                break;
            case 'toPremium':
                host.addEventListener('click', function(evt){
                    this.toPremium(evt);
                }.bind(this));
                break;
            case 'AlaCarteValidation':
                host.addEventListener('click', function(evt){
                    this.alaCarteValidation(evt);
                }.bind(this));
                break;
            case 'PremiumChannelValidation':
                host.addEventListener('click', function(evt){
                    this.premiumChannelValidation(evt);
                }.bind(this));
                break;
            case 'noThanksTelephony':
                host.addEventListener('click', function(evt){
                    this.noThanksTelephony(evt);
                }.bind(this));
                break;
            case 'deleteTelevision':
                host.addEventListener('click', function(evt){
                    this.deleteTelevision(evt);
                }.bind(this));
                break;
            case 'deleteTelephonie':
            host.addEventListener('click', function(evt){
                this.deteleTelephonie(evt);
            }.bind(this));
                break;
            case 'VerifInfoPerso':
                this.verifInfoPerso();
                break;
            default:
                break;

        }
        
    }

    verifInfoPerso(evt){
        var selection_internet = this._$OrderService.retInternetPlan();
        if( !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            
            if(!LocalStorage.get('qualificationAddresse')){
                province_complet = CookiesService.getCookies('eboxprovince');
            }else{
                var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
                var province_complet = "";

                if(province == "on"){
                    province_complet = 'ontario';
                }else{
                    province_complet = 'quebec';
                }
            }

            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/forfaits-internet/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/internet-packages/");
            }
            
        }
    }

    deleteTelevision(evt){
        console.log('tv');
        CookiesService.setCookies({
            name: 'hasTV',
            value: false
        });

        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        LocalStorage.set('selectedBundles', []);
        LocalStorage.delete('forfaitOption');
        LocalStorage.delete('eboxTVOptions');
        LocalStorage.delete('selectedChannels');
        LocalStorage.delete('decodeur');
        LocalStorage.delete('DecodeurPromotion');
        this._$OrderService.saveTVOption({});

        document.querySelector('.televisionPrice .detail-price').innerHTML = "";
        jQuery('#sticky-cart-update-btn-television').closest('a').remove();

		//jQuery('#sticky-cart-add-btn-television').show();

        if(this.langue == 'fr'){
            var html = '<a role="button" aria-label="Télévision Ajouter" href="/' + province_complet + '/residentiel/television/"><button aria-hidden="true" title="Télévision Ajouter" class="sticky-cart-action-btn" id="sticky-cart-add-btn-television"><svg class="icon icon-ic_add"><use xlink:href="#icon-ic_add"></use></svg></button></a>';
        }else{
            var html = '<a role="button" aria-label="Television Add" href="/en/' + province_complet + '/residential/television/"><button aria-hidden="true" title="Television Add" class="sticky-cart-action-btn" id="sticky-cart-add-btn-television"><svg class="icon icon-ic_add"><use xlink:href="#icon-ic_add"></use></svg></button></a>';
        }
        jQuery('.televisionPrice .sticky-cart-action-container').prepend(html);

        jQuery('#sticky-cart-cancel-btn-tv').remove();

        this._$OrderService.initCart();
    }

    deteleTelephonie(evt){

        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        console.log('telephonie');
        LocalStorage.delete('phonePlan');
        LocalStorage.delete('phoneEquipement');
        document.querySelector('.telephonyPrice .detail-price').innerHTML = "";
        jQuery('#sticky-cart-update-btn-telephonie').closest('a').remove();

		//jQuery('#sticky-cart-add-btn-telephonie').show();

        if(this.langue == 'fr'){
            var html = '<a role="button" aria-label="Télévision Ajouter" href="/' + province_complet + '/residentiel/television/"><button aria-hidden="true" title="Télévision Ajouter" class="sticky-cart-action-btn" id="sticky-cart-add-btn-television"><svg class="icon icon-ic_add"><use xlink:href="#icon-ic_add"></use></svg></button></a>';
        }else{
            var html = '<a role="button" aria-label="Telephony Add" href="/en/' + province_complet + '/residential/telephony/"><button aria-hidden="true" title="Telephony Add" class="sticky-cart-action-btn" id="sticky-cart-add-btn-telephonie"><svg class="icon icon-ic_add"><use xlink:href="#icon-ic_add"></use></svg></button></a>';
        }
        jQuery('.telephonyPrice .sticky-cart-action-container').prepend(html);

        jQuery('#sticky-cart-cancel-btn-telephonie').remove();

        this._$OrderService.initCart();
    }

    noThanksTelephony(evt){
        evt.preventDefault();

        var selection_forfait =  this._$OrderService._retForfaitOption();

        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        if(  !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) ){
            
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
            }
        }else{
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
            }
        }
    }

    premiumChannelValidation(evt){
        evt.preventDefault();

        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        if(this.langue == 'fr'){
            window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
        }else{
            window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
        }
    }

    alaCarteValidation(evt){
        evt.preventDefault();

        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        setTimeout(function(){
            var channelLength = 0;
            const chaines = LocalStorage.get('selectedChannels')
            const bundle = LocalStorage.get('selectedBundles');
            var error = 0;

            if(bundle == '113570' || bundle == '113581' || bundle == '117645' || bundle == '117647' || bundle == '118376' || bundle == '118377'){
                for (const [key, value] of Object.entries(chaines)) {
                    if(key == 'a-la-carte') {
                        channelLength = value.length;
                        if(channelLength < 15){
                            jQuery('#alert-required').modal();
                            error = 1;
                        }
                    }
                }
            }

            

            if(error == 0){
                if(jQuery('#alert-alert.in').length == 0){
                    this.langue = CookiesService.getCookies('wp-wpml_current_language');
                    if(this.langue == 'fr'){
                        window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                    }else{
                        window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                    }
                }
            }
        },1000);
    }

    toNumeric(evt){
        evt.preventDefault();

        var selection_phone =  this._$OrderService.retPhoneOrder();
        var selection_forfait =  this._$OrderService._retForfaitOption();
        var selection_internet = this._$OrderService.retInternetPlan();

        jQuery('.button-for-premium').hide();
        jQuery('.button-for-numeric').show();

        if( !(selection_phone && !jQuery.isEmptyObject(selection_phone)) && !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) && !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            jQuery('#alert-disponibilite').modal();
        }else{
            jQuery('#alert-disponibilitecustomer').modal();
        }
    }

    toPremium(evt){
        evt.preventDefault();

        var selection_phone =  this._$OrderService.retPhoneOrder();
        var selection_forfait =  this._$OrderService._retForfaitOption();
        var selection_internet = this._$OrderService.retInternetPlan();

        jQuery('.button-for-premium').show();
        jQuery('.button-for-numeric').hide();

        if( !(selection_phone && !jQuery.isEmptyObject(selection_phone)) && !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) && !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            jQuery('#alert-disponibilite').modal();
        }else{
            jQuery('#alert-disponibilitecustomer').modal();
        }
    }

    deleteTvCheckForTelephonie(){

        CookiesService.setCookies({
            name: 'hasTV',
            value: false
        });

        LocalStorage.set('selectedBundles', []);
        LocalStorage.delete('forfaitOption');
        LocalStorage.delete('eboxTVOptions');
        LocalStorage.delete('selectedChannels');
        LocalStorage.delete('decodeur');
        LocalStorage.delete('DecodeurPromotion');
        this._$OrderService.saveTVOption({});

        var selection =  this._$OrderService.retPhoneOrder();
        var noTelPopupAfter = jQuery('#alert-needed-internet-upgrade').attr('noTelPopupAfter');
        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        if( !(selection && !jQuery.isEmptyObject(selection)) && noTelPopupAfter != '1' ){
            jQuery('#alert-nextsteptelephonie').attr('data-noTv', '1');
            jQuery('#alert-nextsteptelephonie').modal();
        }else{
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
            }
        }

    }

    telephoniePopupValidation(evt){
        evt.preventDefault();

        var selection_internet = this._$OrderService.retInternetPlan();
        var selection_forfait =  this._$OrderService._retForfaitOption();
        if(LocalStorage.get('qualificationAddresse')){
            var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        }else{
            var province = CookiesService.getCookies('eboxprovince');
            console.log(province);
            if(province == 'quebec'){
                province = 'qc';
            }else{
                province = 'on';
            }
        }
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }
        if( !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            jQuery('#alert-needinternet').modal();
            setTimeout(function(){
                jQuery('#alert-needinternet').find('.modal-body').find('a').focus();
            },500);
        }else if(  !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) ){
            if(province == 'on'){

                if(this.langue == 'fr'){
                    window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                }else{
                    window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                }
            }else{
                    
                if(this.langue == 'fr'){
                    window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                }else{
                    window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                }
                
            }
        }else{
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
            }
        }
        
    }

    phonePopupValidation(evt){
        evt.preventDefault();
        var selection =  this._$OrderService.retPhoneOrder();
        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        if( !(selection && !jQuery.isEmptyObject(selection)) ){
            jQuery('#alert-nextsteptelephonie').modal();
        }else{
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
            }
        }
    }

    telePopupValidation(evt){

        var selection =  this._$OrderService._retForfaitOption();
        var selection_internet = this._$OrderService.retInternetPlan();
        var noTv = jQuery('#alert-nextsteptelephonie').attr('data-noTv');
        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }
        this.verifyDSLminVitesse();

        if(noTv == '1'){
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
            }
        }else{
            if( !(selection && !jQuery.isEmptyObject(selection)) ){

                if(province == 'on'){
                    if(this.langue == 'fr'){
                        window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                    }else{
                        window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                    }
                }else{
                    evt.preventDefault();
                    if( !(selection_internet && selection_internet.forfait && selection_internet.downloadSpeed && selection_internet.downloadSpeed >= this.minVitesse) ){
                        jQuery('#alert-needed-internet-upgrade').attr('noTelPopupAfter', '1');
                        jQuery('#alert-needed-internet-upgrade').modal();
                    }else{
                        jQuery('#alert-nextsteptv').modal();
                    }
                }
            }else{
                if(this.langue == 'fr'){
                    window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte/");
                }else{
                    window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
                }
            }
        }
    }

    eboxTvPopup(evt){
        evt.preventDefault();
        if(LocalStorage.get('qualificationAddresse')){
            var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        }else{
            var province = CookiesService.getCookies('eboxprovince');
            console.log(province);
            if(province == 'quebec'){
                province = 'qc';
            }else{
                province = 'on';
            }
        }
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        var validationOkay = this.validationChoice('eboxTV');

        if(validationOkay == true){
            var selection = this._$OrderService.retInternetPlan();
            var selection_infoNuagique = this._$OrderService._reteboxTVOptions();
            var selection_forfait =  this._$OrderService._retForfaitOption();
            var selection_phone =  this._$OrderService.retPhoneOrder();

            //this.verifyDSLminVitesse()

            // Si aucun forfait internet
            if( !(selection && selection.forfait && selection.downloadSpeed && selection.downloadSpeed >= this.minVitesse) ){
                    jQuery('#alert-nextstepinternet').modal();
            }else if( selection_infoNuagique && selection_infoNuagique.cloudSpace && selection_infoNuagique.cloudSpace.min != 0 && parseInt(selection.downloadSpeed) < parseInt(selection_infoNuagique.cloudSpace.min)){
                jQuery('.vitessemin_infonuagique').text(selection_infoNuagique.cloudSpace.min);
                jQuery('#alert-needed-internet-infonuagique').modal();
            }else{
                if( !(selection_phone && !jQuery.isEmptyObject(selection_phone)) ){
                    jQuery('#alert-nextsteptelephonie').modal();
                }else{
                    if( selection_forfait && !jQuery.isEmptyObject(selection_forfait) ){
                        if(this.langue == 'fr'){
                            window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte/");
                        }else{
                            window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
                        }
                    }else{
                        if(this.langue == 'fr'){
                            window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                        }else{
                            window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                        }
                    }
                }
                
            }
        }else{
            if(validationOkay != 'decodeur'){
                jQuery('#alert-missinginfonuagique').modal();
            }else{
                jQuery('#alert-missingdecodeur').modal();
            }
        }

    }

    telephonePopup(){

        var selection = this._$OrderService.retInternetPlan();

        // Si aucun forfait internet
        if( !(selection && selection.forfait) ){
            setTimeout( () => {
                jQuery('#alert-nextstepinternet').modal();
            }, 2000);
        }

    }

    internetPopupValidation(evt){
        evt.preventDefault();
        

        var validationOkay = this.validationChoice('internet');
        if(validationOkay){
            var selection =  this._$OrderService.retPhoneOrder();
            var selection_forfait =  this._$OrderService._retForfaitOption();
            var selection_internet = this._$OrderService.retInternetPlan();
            var selection_infoNuagique = this._$OrderService._reteboxTVOptions();
            var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
            var province_complet = "";

            if(province == "on"){
                province_complet = 'ontario';
            }else{
                province_complet = 'quebec';
            }
        
            this.verifyDSLminVitesse();

            if( selection_internet && selection_internet.forfait && selection_internet.downloadSpeed && parseInt(selection_internet.downloadSpeed) < parseInt(this.minVitesse)){
                
                var found_internet_above_25 = false;
                jQuery('.item-internet').each(function(){
                    if(parseInt(jQuery(this).attr('data-vitesse')) >= parseInt(this.minVitesse)){
                        found_internet_above_25 = true;
                    }
                });

                if(!found_internet_above_25){
                    jQuery('#alert-nextsteptelephonie').modal();
                }else{
                    jQuery('#alert-needed-internet-upgrade').modal();
                }
            }else if(  selection_forfait && !jQuery.isEmptyObject(selection_forfait) ){
                if( !(selection_internet && selection_internet.forfait && selection_internet.downloadSpeed && parseInt(selection_internet.downloadSpeed) >= parseInt(this.minVitesse)) ){
                    jQuery('#alert-needed-internet-upgrade').modal();
                }else if( selection_infoNuagique && selection_infoNuagique.cloudSpace && selection_infoNuagique.cloudSpace.min != 0 && parseInt(selection_internet.downloadSpeed) < parseInt(selection_infoNuagique.cloudSpace.min)) {
                    jQuery('.vitessemin_infonuagique').text(selection_infoNuagique.cloudSpace.min);
                    jQuery('#alert-needed-internet-infonuagique').modal();
                }else{
                    if( !(selection && !jQuery.isEmptyObject(selection)) ){
                        jQuery('#alert-nextsteptelephonie').modal();
                    }else{
                        if(this.langue == 'fr'){
                            window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte/");
                        }else{
                            window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
                        }
                    }
                }
            }else if(  !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) ){
                if(province == 'on'){
                    jQuery('#alert-nextsteptelephonie').modal();
                }else{
                    if( !jQuery.isEmptyObject(selection_forfait) && !(selection_internet && selection_internet.forfait && selection_internet.downloadSpeed && parseInt(selection_internet.downloadSpeed) >= parseInt(this.minVitesse)) ){
                        jQuery('#alert-needed-internet-upgrade').modal();
                    }else{
                        jQuery('#alert-nextsteptv').modal();
                    }
                }
            }else if( !(selection && !jQuery.isEmptyObject(selection)) ){
                jQuery('#alert-nextsteptelephonie').modal();
            }else{
                if(this.langue == 'fr'){
                    window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte/");
                }else{
                    window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
                }
            }
        }else{
            jQuery('#alert-missingproduct').modal();
        }
    }

    internetPopup(){
    }

    redirectIfNoTV(){

        //this.verifyDSLminVitesse()

        // Si aucun forfait télé
        var selection =  this._$OrderService._retForfaitOption();
        if( !(selection && !jQuery.isEmptyObject(selection)) ){
            window.location.replace("/");
            if(this.langue == 'fr'){
                window.location.replace("/");
            }else{
                window.location.replace("/en/");
            }
        }

        // Si vitesse internet insuffisante
        var selection = this._$OrderService.retInternetPlan();
        if( !(selection && selection.forfait && selection.downloadSpeed && selection.downloadSpeed >= this.minVitesse) ){
                setTimeout( () => {
                jQuery('#alert-nextstepinternet').modal();
            }, 1000);
        }

    }

    toggleSelection(target){
        this.cardInternet.forEach(el => {
            el.classList.remove('VirageNum__packages-slider-item--active');
        });
        target.classList.add('VirageNum__packages-slider-item--active');
        if(target.getAttribute('data-sku') !== 'none'){
            this.selection.forfait = target.getAttribute('data-sku');
            this.selection.modem = 'rent';
            this.selection.router = 'rent';
            this.selection.downloadSpeed = target.getAttribute('data-vitesse');
            this.selection.planPrice = target.getAttribute('data-price');
            this.selection.technology = target.getAttribute('data-fournisseur');
            this.selection.title = target.getAttribute('data-title');
        }
        else{
            this.selection = {};
        }
         
        this._$OrderService.saveInternetService(this.selection);
    }

    verifyDSLminVitesse(){
        var selection_internet = this._$OrderService.retInternetPlan();
        if(selection_internet && selection_internet.cableTech == "DSL"){
            this.minVitesse = 25;
            jQuery('.minVitesseText').text(this.minVitesse);
        }
    }

    validationChoice(type){
        var isOkay = true;
        if(type == 'eboxTV'){
            var selection_infoNuagique = this._$OrderService._reteboxTVOptions();
            var selection_forfait =  this._$OrderService._retForfaitOption();
            if(selection_forfait && !jQuery.isEmptyObject(selection_forfait)){
                if(  !(selection_infoNuagique && !jQuery.isEmptyObject(selection_infoNuagique)) ){
                    isOkay = 'infoNuagique';
                }
            }

            // decodeur
            if(document.querySelectorAll('.tv-items-cart--receiver.tv-items-cart--active').length == 0){
                isOkay = 'decodeur';
            }
        }else if(type == 'internet'){
            var selection_internet = this._$OrderService.retInternetPlan();
            if(  !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
                isOkay = false;
            }
        }

        return isOkay;
    }

    validationPopupIfRequired(){
        var isRequired = true;
        if(type == 'eboxTV'){
            var selection_infoNuagique = this._$OrderService._reteboxTVOptions();
            var selection_forfait =  this._$OrderService._retForfaitOption();
            if(selection_forfait && !jQuery.isEmptyObject(selection_forfait)){
                if(  !(selection_infoNuagique && !jQuery.isEmptyObject(selection_infoNuagique)) ){
                    isRequired = false;
                }
            }
        }else if(type == 'internet'){
            var selection_internet = this._$OrderService.retInternetPlan();
            if(  !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
                isRequired = false;
            }
        }
    }

    initTranslation(evt){
        evt.preventDefault();

        var selection_internet = this._$OrderService.retInternetPlan();
        var selection_forfait =  this._$OrderService._retForfaitOption();
        var province = LocalStorage.get('qualificationAddresse').pc.toLowerCase();
        var province_complet = "";

        if(province == "on"){
            province_complet = 'ontario';
        }else{
            province_complet = 'quebec';
        }

        if( !(selection_internet && !jQuery.isEmptyObject(selection_internet)) ){
            jQuery('#alert-needinternet').modal();
            setTimeout(function(){
                jQuery('#alert-needinternet').find('.modal-body').find('a').focus();
            },500);
        }else if(  !(selection_forfait && !jQuery.isEmptyObject(selection_forfait)) ){
            if(province == 'on'){
                if(this.langue == 'fr'){
                    window.location.replace("/" + province_complet + "/residentiel/informations-personnelles/");
                }else{
                    window.location.replace("/en/" + province_complet + "/residential/personal-informations/");
                }
            }else{
                if( !(selection_internet && selection_internet.forfait && selection_internet.downloadSpeed && selection_internet.downloadSpeed >= this.minVitesse) ){
                    jQuery('#alert-needed-internet-upgrade').modal();
                }else{
                    jQuery('#alert-nextsteptv').modal();
                }
            }
        }else{
            if(this.langue == 'fr'){
                window.location.replace("/" + province_complet + "/residentiel/television/chaines-a-la-carte/");
            }else{
                window.location.replace("/en/" + province_complet + "/residential/television/a-la-carte-channels/");
            }
        }
        
    }

}
