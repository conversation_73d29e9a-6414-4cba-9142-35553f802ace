import { Tools } from '@common/ts/services/Tools.service';
import { Subject } from 'rxjs/Subject';
import { CoreTools } from '@core/helpers';

export class OriginRatioChecker {
	//#region Singleton creation and distribution
	static _instance;
	/**
	 * @return {OriginRatioChecker}
	*/
	static getInstance() {
		if (OriginRatioChecker._instance === undefined)
			OriginRatioChecker._instance = new OriginRatioChecker();

		return OriginRatioChecker._instance;
	}
	//#endregion

	//#region Static readonly values

	static get RATIO_COUNTRY() {
		return 'canada';
	}

	/**
	 * Represents the minimum percentage of the minimum amount of 
	 * selected channels having OriginRatioChecker.RATIO_COUNTRY as their country
	 * in the current order.
	 */
	static get RATIO() {
		return 0.5;
	}

	/**
	 * Adds to OriginRatioChecker.RATIO
	 * 
	 * For exemple, the rule when the code was created, the rule was that the 
	 * users had to choose at least 50% + 1 channel of canadian channel.
	 * 
	 * In this exemple, "50%" is represented by OriginRatioChecker.RATIO and the 
	 * "+ 1 channel" is represented by OriginRatioChecker.RATIO_OFFSET.
	 */
	static get RATIO_OFFSET() {
		return 1;
	}
	//#endregion

	//#region Private properties
	_ratioValidationValue = {};
	_ratioValidationChangeSubject$ = new Subject();
	//#endregion

	//#region Public properties
	ratioValidationChange$ = this._ratioValidationChangeSubject$.asObservable();

	get ratioValidation() {
		return this._ratioValidationValue;
	}
	set ratioValidation(validation) {
		this._ratioValidationValue = validation;
		this._ratioValidationChangeSubject$.next(validation);
	}
	//#endregion

	//#region Public methods
	check(channels = [], action, alwaysValid = false) {
		if (alwaysValid) {
			return {isValid: true};
		}
		/* Removing duplicates.
		There might be some duplicate channels because what is sent to this method
		is simply a merge of arrays of already selected channels and possibly new channels
		which might already be selected.*/
		channels = CoreTools.removeDuplicates(channels, 'ID');

		const validChannels = [];
		const invalidChannels = [];
		const rawMinimumQty = (channels.length * OriginRatioChecker.RATIO) + OriginRatioChecker.RATIO_OFFSET; 
		const minimumQty = ~~rawMinimumQty;

		for (let i = 0; i < channels.length; i++) {
			const channel = channels[i];
			const alreadyEvaluated = validChannels.has(channel.ID) || invalidChannels.has(channel.ID);

			if (channel.origin === OriginRatioChecker.RATIO_COUNTRY)
				validChannels.push(channel.ID);
			else
				invalidChannels.push(channel.ID);
		}
		
		const validation = this.ratioValidation = {
			isValid: channels.length === 0 ? true : validChannels.length >= minimumQty,
			currentRatio: channels.length === 0 ? 0.6 : Tools.roundToDecimal(validChannels.length / channels.length, 0.001),
			minimumQty,
			validChannels,
			invalidChannels,
			action,
			qtyBeforeInvalid: rawMinimumQty - validChannels.length
		};

		return validation;
	}

	getErrorMessage() {
		const validation = this.ratioValidation;
		let alertMessage = null;

		if (!validation.isValid) {
			const { currentRatio } = validation;

			if (Tools.lang === 'fr') {
				alertMessage = 'Tu dois revoir ta sélection, car elle ne contient pas assez de chaînes canadiennes ' +
					`(minimum de <strong>${ (OriginRatioChecker.RATIO * 100).toString() }% + ${ OriginRatioChecker.RATIO_OFFSET.toString() }</strong>).` +
					'<br/><br/>' +
					'EBOX contribue à la création et à la présentation d\'une programmation canadienne. ' +
					'Pour les détails de la politique du Conseil de la radiodiffusion et des télécommunications canadiennes, ' +
					'c’est ici : <a href="http://crtc.gc.ca" target="_blank" rel="noopener">crtc.gc.ca</a>';
			} else {
				alertMessage = 'Please revise your selection as it does not contain enough Canadian channels ' +
					`(minimum of <strong>${ (OriginRatioChecker.RATIO * 100).toString() }% + ${ OriginRatioChecker.RATIO_OFFSET.toString() }</strong>).` +
					'<br/><br/>' +
					'EBOX contributes to the creation and presentation of Canadian programming. ' +
					'For details on the Canadian Radio-television and Telecommunications Commission\'s policy, ' +
					'click here: <a href="http://crtc.gc.ca" target="_blank" rel="noopener">crtc.gc.ca</a>';
			}
		}

		return alertMessage;
	}
	//#endregion
}