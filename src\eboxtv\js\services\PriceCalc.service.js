/* global eboxtvOptions */
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { TVOrderService } from './TVOrder.service';
import { Tools } from '@common/ts/services/Tools.service';
import { ChannelsService } from './channels.service';
import { PriceStepsManager } from '../../ts/services/PriceStepsManager';
import { TVOptionsService } from '../../ts/services/TVOptions.service';
import { LocalStorage } from '@common/ts/services/LocalStorage';

export class PriceCalcService {
	/** @type PriceCalcService */
	static instance;

	/** @return PriceCalcService */
	static getInstance() {
		if (PriceCalcService.instance === undefined)
			PriceCalcService.instance = new PriceCalcService();
		return PriceCalcService.instance;
	}

	static stepsArray = 'wpLocalizedVars' in window ? wpLocalizedVars.eboxtvOptions.steps.details : [];

	static RECEIVER_COST = 'wpLocalizedVars' in window ? (wpLocalizedVars.eboxtvOptions.receiver.cost || 0) : 0;

	static COST_PER_STEP = 5;

	static get BASE_COST() {
		const tvOrderService = TVOrderService.getInstance();
		const currentBundle = tvOrderService.getSelectedBundle();

		if (currentBundle && currentBundle.isFeatured)
			return 0;
		else
			return getBaseCost();
	}

	static INITIAL_TOTALS_OBJECT = {
		bundle: 0,
		users: 0,
		channels: {
			'base': PriceCalcService.BASE_COST,
			'a-la-carte': 0,
			'others': 0,
			'premium': 0
		},
		total: 0,
		options: {
			cloudSpace: 0
		}
	};

	// PRIVATE PROPERTIES //
	/* Services */
	_$tvOrder = TVOrderService.getInstance();

	_$tvOptions = TVOptionsService.getInstance();

	_$channels = ChannelsService.getInstance();

	$stepsManager = PriceStepsManager.getInstance();

	/**
	 * @public
	 * @type {BehaviorSubject<TotalsObject>}
	 */
	totalsChange$ = new BehaviorSubject(this.totals);

	get totals() {
		const _this = this;
		const selectedChannels = this._$tvOrder.selectedChannels;
		const selectedBundle = this._$tvOrder.selectedBundlesObjects[0];
		const hasFeaturedBundle = (selectedBundle && selectedBundle.isFeatured);
		return {
			bundle: 0,
			users: 0,
			channels: {
				'base': hasFeaturedBundle ? 0 : PriceCalcService.BASE_COST,
				'a-la-carte': this.calcSetCost(selectedChannels['a-la-carte'], 'a-la-carte'),
				'others': this.calcSetCost(selectedChannels['others']),
				'premium': this.calcSetCost(selectedChannels['premium'], 'premium'),
				'pre-selected': this.calcSetCost(selectedChannels['pre-selected'], 'pre-selected')
			},
			get total() {
				return _this._getGrandTotal(this);
			},
			options: this._getOptionsTotal()
		}; 
	}


	constructor() {
		this._init();
	}

	getTotal(){
		const _this = this;
		const selectedChannels = this._$tvOrder.selectedChannels;
		const selectedBundle = this._$tvOrder.selectedBundlesObjects[0];
		const hasFeaturedBundle = (selectedBundle && selectedBundle.isFeatured);
		return {
			bundle: 0,
			users: 0,
			channels: {
				'base': hasFeaturedBundle ? 0 : PriceCalcService.BASE_COST,
				'a-la-carte': this.calcSetCostSticky(selectedChannels['a-la-carte'], 'a-la-carte'),
				'others': this.calcSetCostSticky(selectedChannels['others']),
				'premium': this.calcSetCostSticky(selectedChannels['premium'], 'premium'),
				'pre-selected': this.calcSetCostSticky(selectedChannels['pre-selected'], 'pre-selected')
			},
			get total() {
				return _this._getGrandTotal(this);
			},
			options: this._getOptionsTotal()
		}; 
	}

	_init() {
		this._subscribeToOrderChange();
	}

	_subscribeToOrderChange() {
		this._$tvOrder.selectedChannelsChange$.subscribe(
			this._onOrderChange.bind(this)
		);

		this._$tvOptions.optionChange$.subscribe(
			this._onOptionsChange.bind(this)
		);
	}

	getCostByStep(step = 0, includeExtras = false, includeOthers = false, debug = false) {
		let total = this.$stepsManager.getStepBySize(step).cost;

		if (includeExtras)
			total += this.getExtraCostTotal(includeOthers);

		if (debug) console.table(output);
		
		return Tools.roundToDecimal(total, 0.01);
	}

	getExtraCostTotal(includeOthers = false) {
		const {'a-la-carte': selectedChannels, others} = this._$tvOrder.selectedChannels;
		let output = 0;

		if(selectedChannels){
			selectedChannels.forEach(currentID => {
				const currentChannel = this._$tvOrder.getChannelById(currentID);
				
				if (currentChannel.extraCost) {
					output += currentChannel.extraCost;
				}
			});
		}

		if (includeOthers) {
			if(others){
				others.forEach(currentID => {
					const currentChannel = this._$tvOrder.getChannelById(currentID);

					if (currentChannel.extraCost) {
						output += currentChannel.extraCost;
					}
				});
			}
		}
		
		return output;
	}

	setOptionValue(name, value) {
		if (name in this._options) {
			this._options[name] = value;
		}

		this._onOrderChange();
	}

	/**
	 * @override
	 */
	_onOrderChange(orderObject) {
		this.totalsChange$.next(this.totals);
	}
	
	_onOptionsChange(options) {
		this.totalsChange$.next(this.totals);
	}

	_getGrandTotal(totalObject) {
		let total = 0;

		for (const key in totalObject) {
			if (key === 'total' || key === 'options') continue;
			const currentProp = totalObject[key];

			if (typeof currentProp === 'number') {
				total += currentProp;
			} else {
				const values = Object.values(currentProp);

				if(values){
					values.forEach(currentValue => {
						total += currentValue;
					});
				}
			}
		}
		return Tools.roundToDecimal(total, 0.01);
	}

	_getOptionsTotal() {
		const options = {...this._$tvOptions.getSelectedOptions()};

		for (const key in options) {
			options[key] = options[key] && options[key].price ? options[key].price : 0;
		}

		return options;
	}


	/**
	 * 
	 * @param {any[]} channels 
	 */
	calcFullCost(channels = []) {
		let cost = 0;

		if(channels){
			channels.forEach(currentSet => {
				cost += this.calcSetCost(currentSet);
			});
		}
		return Tools.roundToDecimal(cost, 0.01);
	}


	/**
	 * 
	 * @param {Set<any>} group 
	 */
	calcSetCost(set = new Set(), type) {
		let cost = 0;
		
		if (type === 'pre-selected') {
			const selectedBundle = this._$tvOrder.selectedBundlesObjects[0];

			if (selectedBundle) cost = selectedBundle.cost;
		} else if (type === 'a-la-carte') {
			let totalSelectedChannelSize = this._$tvOrder.getTotalSelectedChannelSize('a-la-carte');
			let partialCost = 0;
			let extraCost = 0;
			let currentStep = this._$tvOrder.currentStep;

			//if (totalSelectedChannelSize >= 10) {
			if (totalSelectedChannelSize >= TVOrderService.STEP_MIN_VALUE) {
				partialCost = this.getCostByStep(currentStep);
			}

			if(set){
				set.forEach(currentID => {
					const currentChannel = this._$tvOrder.getChannelById(currentID);
					if (currentChannel.extraCost) {
						extraCost += currentChannel.extraCost;
					}
				});
			}

			cost += partialCost + extraCost;
		} else {
			if(set){
				set.forEach(currentID => {
					const currentChannel = this._$tvOrder.getChannelById(currentID);

					if (currentChannel && currentChannel.post_parent == 0 && 'cost' in currentChannel)
						cost += currentChannel.cost;
				});
			}
		}

		return Tools.roundToDecimal(cost, 0.01);
	}

	/**
	 * 
	 * @param {Set<any>} group 
	 */
	 calcSetCostSticky(set = new Set(), type) {
		let cost = 0;
		
		if (type === 'pre-selected') {
			const selectedBundle = this._$tvOrder.selectedBundlesObjects[0];

			if (selectedBundle) cost = selectedBundle.cost;
		} else if (type === 'a-la-carte') {
			let totalSelectedChannelSize = this._$tvOrder.getTotalSelectedChannelSize('a-la-carte');
			let partialCost = 0;
			let extraCost = 0;

			if (totalSelectedChannelSize >= 10) {
				partialCost = this.getCostByStep(this._$tvOrder.currentStep);
			}

			if(set){
				set.forEach(currentID => {
					const currentChannel = this._$tvOrder.getChannelById(currentID);
					if (currentChannel.extraCost) {
						extraCost += currentChannel.extraCost;
					}
				});
			}

			cost += partialCost + extraCost;
		} else {
			if(set){
				set.forEach(currentID => {
					const currentChannel = this._$tvOrder.getChannelById(currentID);

					if (currentChannel && currentChannel.post_parent == 0 && 'cost' in currentChannel)
						cost += currentChannel.cost;
				});
			}
		}

		return Tools.roundToDecimal(cost, 0.01);
	}
}

function getBaseCost() {
	if ('wpLocalizedVars' in window && window.wpLocalizedVars.eboxtvOptions && window.wpLocalizedVars.eboxtvOptions.baseCost !== null && window.wpLocalizedVars.eboxtvOptions.baseCost !== undefined)
		return parseFloat(window.wpLocalizedVars.eboxtvOptions.baseCost);
	else
		return 0;
}