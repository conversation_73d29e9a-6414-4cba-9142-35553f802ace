/* global jQuery */
import { Directive } from '@core/directive';
import { OrderOnlineService } from "../services/orderOnline.service";
import { LocalStorage } from '@common/ts/services/LocalStorage';
import { CoreTools } from '@core/helpers';
import { PromotionsService } from '../services/promotions.service';
import { CookiesService } from '@common/ts/services/Cookies.service';
export class DecodeurDirective extends Directive {

	// INSTANCE DU SERVICE D PRDRE
	_$OrderService = OrderOnlineService.getInstance();
    _$promotionsService = PromotionsService.getInstance();

    //SELECTEUR DE LA DIRECTIVE
	static selector = '[option-decodeur]';

    decodeurInfo = {};

    newDecodeurInfo = {
        idRef: '',
        buyRent: '',
        price: '',
        code: '',
        qty: '',
        service: 'tv',
        type: 'eboxtv'
    }

	constructor(host) {
		super(host, []);

		this._onInit();
	}

	_onInit() {
        this.decodeurInfo = this._$OrderService.initDecodeur();
        if(this.host.type == "select-one"){

            if(this._$OrderService.decodeurInfo.idRef == this.host.getAttribute('data-id')){
                this.host.value = this._$OrderService.decodeurInfo.qty;
            }

            this.host.addEventListener('change', function(evt){
                this.numberSaveToLocalStorage(evt);
            }.bind(this));
        }else{

            if(this._$OrderService.decodeurInfo.idRef == this.host.getAttribute('data-id')){
                this.host.checked = true;
            }

            this.host.addEventListener('click', function(evt){
                this.basicSaveToLocalStorage(evt);
            }.bind(this));
        }
        jQuery('input[name="choixDecodeur"]').parent().parent().removeClass('TVForfaitItem__active');
        jQuery('input[name="choixDecodeur"]:checked').parent().parent().addClass('TVForfaitItem__active');

	}

    basicSaveToLocalStorage(evt){
        jQuery('input[name=' + evt.target.name + ']').parent().parent().removeClass('TVForfaitItem__active');
        jQuery('input[name=' + evt.target.name + ']:checked').parent().parent().addClass('TVForfaitItem__active');

        this.newDecodeurInfo.buyRent = this.host.getAttribute('data-buyrent');
        this.newDecodeurInfo.price = this.host.getAttribute('data-price');
        this.newDecodeurInfo.code = this.host.getAttribute('data-sku');
        this.newDecodeurInfo.idRef = this.host.getAttribute('data-id');
        if(document.getElementById('qty' + evt.target.id)){
            this.newDecodeurInfo.qty = document.getElementById('qty' + evt.target.id).value;
        }

        if(this.newDecodeurInfo.code != ""){
            var id = this.host.id;
            var qty_element = document.getElementById(id);
            this.newDecodeurInfo.buyRent = qty_element.value;
        }

        this._$OrderService.saveDecodeur(this.newDecodeurInfo);

        this._$promotionsService.promoDecodeur(this.newDecodeurInfo.qty, this.newDecodeurInfo.buyRent);

        this._$OrderService.initCart();

    }

    numberSaveToLocalStorage(evt){
        var id = this.host.id;
        var element_id = id.replace('qty', '');

        var input_element = document.getElementById(element_id);
        
        if(input_element.checked == true){
            this.newDecodeurInfo.buyRent = input_element.getAttribute('data-buyrent');
            this.newDecodeurInfo.price = input_element.getAttribute('data-price');
            this.newDecodeurInfo.code = input_element.getAttribute('data-sku');
            this.newDecodeurInfo.idRef = input_element.getAttribute('data-id');

            this.newDecodeurInfo.qty = this.host.value;
            

            this._$OrderService.saveDecodeur(this.newDecodeurInfo);

            this._$promotionsService.promoDecodeur(this.newDecodeurInfo.qty, this.newDecodeurInfo.buyRent);
            // this.promoDecodeur(this.newDecodeurInfo.qty, this.newDecodeurInfo.buyRent);

            this._$OrderService.initCart();
        }
    }



}


