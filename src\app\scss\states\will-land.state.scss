$travelDistance: 15%;

.will-land {
	position: relative;
	opacity: 0;
	transform: translate(0, $travelDistance);
	transition-property: transform, opacity;
	transition-duration: 0.6s;
	transition-timing-function: $cubic;
	will-change: transform, opacity;

	&--from-far-far-away {
		transform: scale(0.75);

		&.will-land--landed {
			transform: scale(1);
		}
	}

	&--from-right {
		transform: translate($travelDistance, 0);
	}

	&--from-left {
		transform: translate(-$travelDistance, 0);
	}

	&--landed {
		opacity: 1;
		transform: translate(0, 0);
	}
}