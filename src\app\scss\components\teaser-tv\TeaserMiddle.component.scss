$bracketHeight: 165px;

.TeaserMiddle {
	background: {
		image: url('~@common/img/back-body-tv.jpg');
	};
	position: relative;
	padding: 0 0 60px;

	@media (max-width: 991px) {
		padding-bottom: 40px;
	}
	@media (max-width: 600px) {
		padding-bottom: 10px;
	}

	&:before, &:after {
		content: '';
		position: absolute;
		display: block;
		width: 100%;
		left: 50%;
		transform: translateX(-50%);
		height: $bracketHeight;
		background: {
			image: url('../../../img/teaser-tv/shadow-white.svg');
			size: 2100px 170px;
			position: center bottom 30px;
			repeat: no-repeat;
		};

		@media (min-width: 1921px) {
			background-size: 110% 170px;
		}
	}

	&:before {
		bottom: 99%;
		transform: translateX(-50%) rotate(180deg);
	}
	&:after {
		top: 99%;
	}

	@at-root {
		body.is-safari, body.is-ios {
			.TeaserMiddle {
				&:before, &:after {
					height: 155px;
					background-position: center bottom 25px;
				}
			}
		}
	}
	
	// $separatorAngle: 3.39;
	// &__angle {
	// 	position: relative;
	// 	display: block;
	// 	width: 100%;
	// 	top: -40px;
	// 	height: 0;

	// 	&--bottom {
	// 		transform: rotate(180deg);
	// 		top: 105px;

	// 		@media (max-width: 991px) {
	// 			top: 55px;
	// 		}
	// 		@media (max-width: 600px) {
	// 			top: 30px;
	// 		}
	// 	}
		
	// 	&:after, &:before {
	// 		content: '';
	// 		position: absolute;
	// 		display: block;
	// 		width: 50%;
	// 		height: 130px;
	// 		padding: 0 1px;
	// 		box-sizing: content-box;
	// 		box-shadow: 0 -5px 30px 0 rgba(0, 0, 0, 0.45);
	// 		background: {
	// 			image: url('~@common/img/back-body-tv.jpg');
	// 			position: center bottom 25px;
	// 			repeat: no-repeat;
	// 		};
	// 		z-index: 0;
	// 	}

	// 	&:before {
	// 		left: -1px;
	// 		transform: rotate(#{ $separatorAngle }deg) skewX(#{ $separatorAngle }deg);
	// 	}
	// 	&:after {
	// 		right: -1px;
	// 		transform: rotate(-#{ $separatorAngle }deg) skewX(-#{ $separatorAngle }deg);
	// 	}
	// }

	&__wrapper {
		position: relative;
		z-index: 1;
	}

	&__decoder-container {
		position: relative;
		display: block;
		width: 95%;
		max-width: 1263px;
		height: auto;
		margin: -100px auto 0;
		// display: none;

		&:before {
			content: '';
			position: absolute;
			display: block;
			top: 50%;
			left: 11%;
			margin-top: -20px;
			width: 105px;
			height: 209px;
			opacity: 0;
			background: {
				image: url('../../../img/teaser-tv/arrow-curve.svg');
				repeat: no-repeat;
				size: cover;
			}
			transition: all 0.4s ease-out;

			@media (max-width: 1299px) {
				display: none;
			}
		}

		&--landed {
			&:before {
				opacity: 1;
			}
		}

		@media (max-width: 767px) {
			margin-top: -80px;
		}
		@media (max-width: 600px) {
			margin-top: -50px;
		}
		@media (max-width: 600px) {
			margin-top: -40px;
		}
	}

	&__decoder {
		max-width: 100%;
	}

	&__wrapper {
		text-align: center;
	}

	&__title {
		margin: 10px 0;
		display: inline-block;
		font: 900 45px/1.125 $f-primary;
		color: $c-black;
		text-transform: uppercase;

		@media (max-width: 991px) {
			font-size: 26px;
		}
		@media (max-width: 600px) {
			font-size: 18px;
		}
	}

	&__sub-title {
		font: 700 26px/1.125 $f-primary;
	}
}