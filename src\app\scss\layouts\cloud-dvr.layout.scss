@mixin cloudDVRBackground {
	background: {
		image: url('../../img/diagonal-right-bg.svg');
		position: right top;
		size: auto 100%;
		repeat: no-repeat;
	};
}

.cloud-dvr {
	color: #FFF;
	background-image: url('../../img/Body.png');

	+ .HomeControl {
		.HomeControl {
			&__consec {
				border-top: none;
			}
		}
	}

	&__wrapper {
		position: relative;
		max-width: 1920px;
		margin: 0 auto;
		padding: 154px 2.8vw 154px 7.29vw;
		@include cloudDVRBackground;

		@media (min-width: 1921px) {
			padding: 154px 140px 154px 54px;

			&:after {
				content: '';
				position: absolute;
				display: block;
				top: 0;
				left: 100%;
				height: 100%;
				width: calc((100vw - 1920px));
				background-color: $c-black;
			}
		}
	}

	&__row {
		display: flex;
		margin: 0 -28px;
	}

	&__col {
		padding: 0 28px;

		&--text {
			flex: 0 0 53.4%;
			width: 53.4%;
			
			@media (min-width: 1600px)  {
				min-width: 952px;
			}
		}

		&--image {
			flex: 0 1 46.6%;
		}
	}

	&__col-title {
		margin-bottom: 8px;
	}

	&__specs {
		display: flex;

		&.cloud-dvr__specs.cloud-dvr__specs {
			margin: 40px 0 0 !important;
			padding: 0;
	
			.owl-item {
				text-align: center;
			}

			@media (min-width: 1100px) {
				width: 100%;

				.owl-stage {
					display: flex;
				}
			}
		}

		.TitleIconBlock {
			&__title-part {
				&--top {
					font-size: 18px;
				}

				&--bottom {
					font-size: 22px;
				}

				@media (min-width: 1600px) {
					white-space: nowrap;
				}

				@media (min-width: 1100px) and (max-width: 1599px) {
					&--top {
						font-size: 1.04vw;
						line-height: 1.25vw;
					}
	
					&--bottom {
						font-size: 1.3vw;
						line-height: 1.56vw;
					}
				}
			}
		}
	}

	&__note {
		margin-top: 64px;
	}

	&__main-img {
		width: 774px;
		max-width: 100%;
	}

	&__controls {
		display: none;
	}

	@media (max-width: 1399px) {
		&__wrapper {
			padding-top: 120px;
			padding-bottom: 120px;
		}
	}

	@media (max-width: 1099px) {
		@include cloudDVRBackground;

		&__wrapper {
			width: 100%;
			max-width: 970px;
			padding: 60px 30px;
			background: none;
		}

		&__row {
			flex-direction: column-reverse;
			margin: -30px 0;
		}
	
		&__col {
			padding: 30px 0;
			text-align: center;

			&--text,
			&--image {
				flex: 0 0 100%;
				width: 100%;
			}
		}

		&__specs-wrapper {
			position: relative;
		}

		&__specs {
			display: flex;
			width: 100%;
			max-width: 880px;
			margin: 0 auto;

			&.cloud-dvr__specs.cloud-dvr__specs {
				margin: 40px 0 0 !important;
			}
		}
	
		&__spec {
			padding: 0 12px;
		}

		&__main-img {
			display: block;
			width: 100%;
			max-width: 400px;
			margin: 0 auto;
		}
	}

	@media (max-width: 919px) {
		&__specs-wrapper {
			padding: 0 48px;
		}

		&__controls {
			position: absolute;
			display: flex;
			justify-content: space-between;
			top: 50%;
			left: 0;
			transform: translateY(-50%);
			width: 100%;
		}

		&__control {
			z-index: 1;
		}
	}

	@media (max-width: 767px) {
		&__wrapper {
			padding: 60px 15px;
		}

		&__note {
			margin-top: 32px;
			text-align: left;
		}
	}

	@media (max-width: 599px) {
		background-position: left -150px top;
	}
}