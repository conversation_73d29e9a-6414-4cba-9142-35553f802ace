.footer {
	position: relative;
	background: #000;

	a {
		&:hover, &:focus {
			color: $c-primary-dark;
			text-decoration: none;
		}
	}

	&__autoscroll {
		position: absolute;
		top: -20px;
		left: 50%;
		margin-left: -20px;
		width: 40px;
		height: 40px;
		transform: translateY(0);
		transition: all 0.2s $cubic;

		&:hover {
			transform: translateY(-5px);
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			width: 52px;
			height: 52px;
			top: -26px;
			margin-left: -26px;
			&:hover {
				transform: none;
			}
		}
	}

	&__wrapper {
		padding-top: 34px;
		padding-bottom: 34px;

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			padding: 55px 7px;
		}
		@media (max-width: 400px) {
			padding: 60px 7px 7px;
		}
	}

	&__top {
		display: flex;
		align-items: center;
		margin-bottom: 23px;

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			display: block;
			align-items: initial;
			text-align: center;
		}
		@media (max-width: 400px) {
			text-align: left;
			margin-bottom: 0;
		}
	}

	&__links-container {
		flex: 1;
		margin: 0 30px;

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			display: block;
			margin: 0 0 15px 0;
		}
		@media (max-width: 400px) {
			margin-bottom: 0;
		}
	}

	&__links-wrapper {
		display: inline-block;
	}

	&__logo-container {
		@media (max-width: map-get($breakpoints, carouselTablet)) {
			display: block;
			margin-bottom: 15px;
		}
		@media (max-width: 400px) {
			margin-bottom: 43px;
		}
	}

	&__logo {
		display: inline-block;
		width: 150px;
		height: 40px;
	}

	&__link-list {
		display: inline-block;

		&:after {
			content: '';
			position: relative;
			display: inline-block;
			vertical-align: middle;
			width: 4px;
			height: 20px;
			margin: 0 15px;
			background: {
				repeat: no-repeat;
				size: contain;
				position: center;
			};
		}

		&:nth-child(odd) {
			&:after {
				background-image: url('../../img/divider01.svg');
			}
		}
		&:nth-child(even) {
			&:after {
				background-image: url('../../img/divider02.svg');
			}
		}

		&:last-child {
			&:after {
				content: none;
			}
		}

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			&--menu {
				display: block;
		
				&:after {
					content: none;
				}
			}

			&:after {
				margin: 0 10px;
				bottom: 3px;
			}
		}

		@media (max-width: 400px) {
			margin-bottom: 32px;

			&--menu {
				display: flex;
				justify-content: space-between;
			}
		}

	}

	&__link-item {
		display: inline-block;
		margin-right: 20px;
		&:last-child {
			margin-right: 0;
		}
	}

	&__link {
		font: 900 12px/12px $f-primary;
		color: $c-white;
		text-transform: uppercase;

		@media (max-width: map-get($breakpoints, carouselMobile)) {
			font-size: 16px;
			line-height: 40px;
		}
		@media (max-width: 400px) {
			line-height: 1;
		}
	}

	&__infos {
		@media (max-width: map-get($breakpoints, carouselTablet)) {
			display: inline-block;
		}
		@media (max-width: 400px) {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}

	&__tel-link, &__lng-list {
		display: inline-block;
		vertical-align: middle;

		@media (max-width: map-get($breakpoints, carouselTablet)) {
			display: block;
			margin: 0 0 15px;
		}

		@media (max-width: 400px) {
			display: inline-block;
			margin: 0;
		}
	}

	&__tel-link {
		font: 900 20px/28px $f-primary;
		color: $c-white;
		margin-right: 30px;

		@media (max-width: 400px) {
			font-size: 28px;
		}
	}

	&__lng-item {
		display: inline-block;
		&:after {
			content: '';
			position: relative;
			display: inline-block;
			bottom: 2px;
			vertical-align: middle;
			width: 2px;
			height: 15px;
			background: $c-primary-dark;
			margin: 0 4px;

			@media (max-width: 400px) {
				bottom: 4px;
			}
		}

		&:last-child {
			&:after {
				content: none;
			}
		}
	}

	&__lng-link {
		font: 900 14px/24px $f-primary;
		color: $c-primary-dark;
		text-transform: uppercase;

		&:hover, &:focus, &--current {
			color: $c-white !important;
		}

		@media (max-width: 400px) {
			font-size: 20px;
		}
	}

	&__copyright {
		font: 400 14px/12px $f-primary;
		color: $c-white;
		
		@media (max-width: map-get($breakpoints, carouselTablet)) {
			text-align: center;
		}

		@media (max-width: 400px) {
			text-align: left;
			margin-top: 28px;
		}
	}
}

.footerCity {
	a {
		&:hover {
			text-decoration: underline!important;
		}
	}
	li {
		display: inline;
		&:after {
			content:"|";
			display: inline-block;
			margin: 0 10px;
		}
		&:last-child {
			&:after {
				display: none;
			}
		}
	}
}