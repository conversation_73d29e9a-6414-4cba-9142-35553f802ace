
import { Directive } from '@core/directive';
import { App } from '@core/bootstrap';
import { MaterialInputDirective } from '@common/ts/directives/material-input/material-input.directive';

export class GformOnValidateDirective extends Directive {
    static selector: string = '.gform-on-validate';

    private innerApp: App;

    constructor(host: HTMLElement) {
        super(host, []);
        this.bindEvents();
        this.initApp();
    }

    private bindEvents(): void {
        jQuery(document).on('gform_post_render', this.onValidate.bind(this));
    }

    private onValidate(): void {
        this.destroyInnerApp();
        this.initApp();
    }

    private destroyInnerApp(): void {
        if (this.innerApp)
            this.innerApp.destroy();
    }

    private initApp(): void {
        this.innerApp = new App({
            rootElement: this.host,
            directives: [{
                use: MaterialInputDirective,
                selector: '.material-input input[type="text"], .material-input textarea'
            }]
        }).bootstrap(false);
    }
}