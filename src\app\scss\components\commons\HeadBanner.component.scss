.HeadBanner {
	position: relative;
	background-color: $c-primary;
	color: #FFF;
	width: 100%;
	font: 400 16px/24px $f-primary;
	z-index: 100;

	&--has-close-btn {
		display: none;
	}
	
	&--fixed {
		position: fixed;
		top: 0;
	}

	&__wrapper,
	&__text-wrapper {
		display: flex;
		align-items: center;
	}

	&__wrapper {
		@extend .container;
		justify-content: space-between;
		padding-top: 16px;
		padding-bottom: 16px;

		&:before,
		&:after {
			content: none;
		}
	}

	&__title {
		strong {
			font-size: 18px;
		}
		a {
			color: #FFF!important;
		}
		span {
			@media (max-width: 658px) {
				display: block;
			}
		}
	}

	&__text-wrapper {
		padding-right: 32px;
	}

	&__icon {
		width: 32px;
		height: 32px;
		fill: currentColor;
		margin-right: 16px;
		flex-shrink: 0;
		align-self: flex-start;
	}

	&__button {
		@extend .ButtonEffect, .ButtonEffect--transparent, .ButtonEffect--qualification;
		flex-shrink: 0;
		
	}
	&__close-button {
		position: absolute;
		top: 8px;
		right: 8px;
		padding: 0;
		appearance: none;
		background: none;
		border: none;

		&-icon {
			display: inline-block;
			width: 24px;
			height: 24px;
			fill: currentColor;
		}
	}

	@media (max-width: 799px) {
		&__wrapper {
			padding-right: 64px;
		}
	}

	@media (max-width: 599px) {
		&__wrapper {
			padding-right: 16px;
			// flex-direction: column;
		}

		&__title {
			strong {
				font: 700 14px/18px $f-primary;
			}
		}

		&__icon {
			margin-right: 8px;
		}

		&__text-wrapper {
			padding-right: 16px;
		}

		&__button {
			margin-left: auto;
		}
	}
}